// Import the polyfill at the very beginning
import 'intl-pluralrules';
import { initReactI18next } from 'react-i18next';
import { NativeModules, Platform } from 'react-native';
import ICU from 'i18next-icu';

import i18n from 'i18next';
import * as SecureStore from 'expo-secure-store';
import ar from '@/locales/ar.json';
import en from '@/locales/en.json';
import hi from '@/locales/hi.json';
import fr from '@/locales/fr.json';
import { SecureStoreKeys } from '@/constants/secureStoreKeys';

// Language Detector using SecureStore
const languageDetector = {
  type: 'languageDetector',
  async: true,
  detect: async (callback: any) => {
    let storedLocale = await SecureStore.getItemAsync(SecureStoreKeys.language);
    if (storedLocale) {
      callback(storedLocale);
    } else {
      const deviceLocale =
        Platform.OS === 'ios'
          ? NativeModules.SettingsManager?.settings?.AppleLocale ||
            NativeModules.SettingsManager?.settings?.AppleLanguages[0] ||
            'en'
          : NativeModules.I18nManager?.localeIdentifier || 'en';
      callback(deviceLocale.replace('_', '-'));
    }
  },
  init: () => {},
  cacheUserLanguage: async (locale: string) => {
    await SecureStore.setItemAsync(SecureStoreKeys.language, locale);
  },
} as any;

i18n
  .use(languageDetector)
  .use(initReactI18next)
  .use(ICU)
  .init({
    fallbackLng: 'ar', // Set Arabic as the default language
    resources: {
      en: { translation: en },
      ar: { translation: ar },
      hi: { translation: hi },
      fr: { translation: fr },
    },
    interpolation: {
      escapeValue: false, // React already safes from XSS
    },
    react: {
      useSuspense: false,
    },
  });

export default i18n;
