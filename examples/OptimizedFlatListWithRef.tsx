import React, { useRef, useCallback } from 'react';
import { View, Text, TouchableOpacity, FlatList } from 'react-native';
import OptimizedFlatList from '@/components/ui/OptimizedFlatList';

interface ExampleItem {
  _id: string;
  title: string;
  description: string;
}

/**
 * Example component demonstrating how to use OptimizedFlatList with ref
 * Shows how to access FlatList methods through the ref
 */
const OptimizedFlatListWithRefExample: React.FC = () => {
  // Create a ref to access FlatList methods
  const flatListRef = useRef<FlatList<ExampleItem>>(null);

  // Example data
  const data: ExampleItem[] = Array.from({ length: 100 }, (_, index) => ({
    _id: `item-${index}`,
    title: `Item ${index + 1}`,
    description: `This is the description for item ${index + 1}`,
  }));

  // Render item component
  const renderItem = useCallback(({ item }: { item: ExampleItem }) => (
    <View style={{
      padding: 16,
      marginVertical: 8,
      backgroundColor: '#f0f0f0',
      borderRadius: 8,
    }}>
      <Text style={{ fontSize: 16, fontWeight: 'bold' }}>{item.title}</Text>
      <Text style={{ fontSize: 14, color: '#666' }}>{item.description}</Text>
    </View>
  ), []);

  // Example methods using the ref
  const scrollToTop = useCallback(() => {
    flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
  }, []);

  const scrollToEnd = useCallback(() => {
    flatListRef.current?.scrollToEnd({ animated: true });
  }, []);

  const scrollToIndex = useCallback((index: number) => {
    flatListRef.current?.scrollToIndex({ index, animated: true });
  }, []);

  const flashScrollIndicators = useCallback(() => {
    flatListRef.current?.flashScrollIndicators();
  }, []);

  return (
    <View style={{ flex: 1 }}>
      {/* Control buttons */}
      <View style={{
        flexDirection: 'row',
        justifyContent: 'space-around',
        padding: 16,
        backgroundColor: '#fff',
        borderBottomWidth: 1,
        borderBottomColor: '#eee',
      }}>
        <TouchableOpacity
          onPress={scrollToTop}
          style={{
            padding: 8,
            backgroundColor: '#007AFF',
            borderRadius: 4,
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Top</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={scrollToEnd}
          style={{
            padding: 8,
            backgroundColor: '#007AFF',
            borderRadius: 4,
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>End</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={() => scrollToIndex(50)}
          style={{
            padding: 8,
            backgroundColor: '#007AFF',
            borderRadius: 4,
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Index 50</Text>
        </TouchableOpacity>

        <TouchableOpacity
          onPress={flashScrollIndicators}
          style={{
            padding: 8,
            backgroundColor: '#007AFF',
            borderRadius: 4,
          }}
        >
          <Text style={{ color: 'white', fontSize: 12 }}>Flash</Text>
        </TouchableOpacity>
      </View>

      {/* OptimizedFlatList with ref */}
      <OptimizedFlatList
        ref={flatListRef}
        data={data}
        renderItem={renderItem}
        itemHeight={80} // Known height for better performance
        enableVirtualization={true}
        showsVerticalScrollIndicator={true}
        contentContainerStyle={{ padding: 16 }}
        // All standard FlatList props are supported
        onEndReached={() => console.log('End reached')}
        onEndReachedThreshold={0.2}
        refreshing={false}
        onRefresh={() => console.log('Refreshing')}
      />
    </View>
  );
};

export default OptimizedFlatListWithRefExample;

/**
 * Usage Examples:
 * 
 * 1. Basic usage with ref:
 * ```tsx
 * const flatListRef = useRef<FlatList<YourItemType>>(null);
 * 
 * <OptimizedFlatList
 *   ref={flatListRef}
 *   data={yourData}
 *   renderItem={yourRenderItem}
 * />
 * ```
 * 
 * 2. Accessing FlatList methods:
 * ```tsx
 * // Scroll to top
 * flatListRef.current?.scrollToOffset({ offset: 0, animated: true });
 * 
 * // Scroll to specific index
 * flatListRef.current?.scrollToIndex({ index: 10, animated: true });
 * 
 * // Scroll to end
 * flatListRef.current?.scrollToEnd({ animated: true });
 * 
 * // Flash scroll indicators
 * flatListRef.current?.flashScrollIndicators();
 * 
 * // Get scroll metrics
 * const metrics = flatListRef.current?.getScrollableNode();
 * ```
 * 
 * 3. With performance optimizations:
 * ```tsx
 * <OptimizedFlatList
 *   ref={flatListRef}
 *   data={data}
 *   renderItem={renderItem}
 *   itemHeight={120} // Enables getItemLayout optimization
 *   enableVirtualization={true}
 *   onViewableItemsChanged={handleViewableItemsChanged}
 * />
 * ```
 */
