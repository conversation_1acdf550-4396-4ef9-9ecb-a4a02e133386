import React from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { Text, View } from 'react-native';
import { Link } from 'expo-router';

const PasswordChanged = () => {
  return (
    <ScreenTemplate>
      <View className='flex-1 justify-center items-center px-4'>
        <View className='justify-center items-center'>
          <Text className='text-white-50 font-poppins font-semibold text-4xl '>
            Password changed
          </Text>
          <Text className='font-poppins text-h4 text-neutral-200 max-w-[79%] text-center '>
            Your password has been changed succesfully!
          </Text>
        </View>
        <Link
          className='text-white-50 bg-secondary-300 w-full text-center py-3 font-poppins font-bold text-h5 rounded-3xl mt-10'
          href={'/auth'}
        >
          Back to login
        </Link>
      </View>
    </ScreenTemplate>
  );
};

export default PasswordChanged;
