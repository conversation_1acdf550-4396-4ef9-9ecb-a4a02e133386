import React, {
  Fragment,
  useEffect,
  useRef,
  useCallback,
  useMemo,
  memo,
} from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomInput from '@/components/ui/CustomInput';
import CustomDatePicker from '@/components/CustomDatePicker';
import CustomText from '@/components/ui/CustomText';
import GrayUser from '@/assets/icons/user-gray.svg';
import CustomPhoneInput from '@/components/ui/CustomPhoneInput';
import { Image } from 'expo-image';
import { useLocalSearchParams, useRouter } from 'expo-router';
import CustomButton from '@/components/ui/buttons/CustomButton';
import { normalized, COLORS } from '@/constants/Theme';
import CustomKeyboardAvoidingView from '@/components/ui/CustomKeyboardAvoidingView';
import { Controller } from 'react-hook-form';
import useUpdateUser from '@/hooks/userHooks/useUpadteUser';
import CustomRadioWithText from '@/components/ui/Radio/CustomRadioWithText';
import { GENDER_ENUM } from '@/types';
import BottomSheet, {
  BottomSheetView,
  BottomSheetBackdrop,
} from '@gorhom/bottom-sheet';

const snapPoints = ['25%'];

// Memoized Profile Image Component
const ProfileImage = memo(
  ({
    displayImage,
    onPress,
  }: {
    displayImage: string | null | undefined;
    onPress: () => void;
  }) => (
    <TouchableOpacity
      onPress={onPress}
      className='justify-center items-center bg-neutral-100 w-32 h-32 rounded-full mx-auto relative'
    >
      {displayImage ? (
        <Image source={{ uri: displayImage }} style={styles.image} />
      ) : (
        <GrayUser />
      )}
      <View className='bg-secondary-300 absolute w-10 h-10 rounded-full justify-center text-center bottom-0 right-0'>
        <CustomText className='text-white-50 text-center text-h1 font-bold'>
          +
        </CustomText>
      </View>
    </TouchableOpacity>
  ),
);

// Memoized Bottom Sheet Content
const BottomSheetContent = memo(
  ({
    onUpload,
    onRemove,
  }: {
    onUpload: () => Promise<void>;
    onRemove: () => void;
  }) => (
    <BottomSheetView className='flex-1 px-4 py-2'>
      <CustomText className='text-white-50 text-h3 font-bold mb-4'>
        Profile Photo
      </CustomText>
      <View className='gap-3'>
        <CustomButton onPress={onUpload} className='!rounded-lg'>
          Upload Image
        </CustomButton>
        <CustomButton
          onPress={onRemove}
          className='!rounded-lg'
          variant='secondary'
        >
          Remove Image
        </CustomButton>
      </View>
    </BottomSheetView>
  ),
);

const RegisterUserData = () => {
  const router = useRouter();
  const { userId } = useLocalSearchParams<{ userId: string }>();
  const bottomSheetRef = useRef<BottomSheet>(null);

  const {
    errors,
    handleSaveProfile,
    pickImage,
    control,
    watch,
    setValue,
    handleRemoveImage,
    isLoading,
    isSuccess,
  } = useUpdateUser(null);

  const displayImage = watch('displayImage');

  useEffect(() => {
    if (isSuccess) {
      router.push({
        pathname: '/auth/interestsScreen',
        params: { _id: userId },
      });
    }
  }, [isSuccess, router, userId]);

  const handleSheetChanges = useCallback((index: number) => {
    console.log('handleSheetChanges', index);
  }, []);

  const renderBackdrop = useCallback(
    (props: any) => (
      <BottomSheetBackdrop
        {...props}
        disappearsOnIndex={-1}
        appearsOnIndex={0}
        opacity={0.5}
      />
    ),
    [],
  );

  const handleUploadImage = useCallback(async () => {
    await pickImage();
    bottomSheetRef.current?.close();
  }, [pickImage]);

  const handleRemoveImagePress = useCallback(() => {
    handleRemoveImage();
    bottomSheetRef.current?.close();
  }, [handleRemoveImage]);

  const handleExpandBottomSheet = useCallback(() => {
    bottomSheetRef.current?.expand();
  }, []);

  const handleSaveProfilePress = useCallback(() => {
    handleSaveProfile({ userId });
  }, [handleSaveProfile, userId]);

  return (
    <ScreenTemplate>
      <View className='flex-1 px-2.5 pb-3'>
        <CustomKeyboardAvoidingView>
          <ScrollView keyboardDismissMode='on-drag'>
            <CustomText className='text-white-50 text-[38px] font-semibold max-w-[80%] my-3'>
              Set Up Your Profile
            </CustomText>

            <ProfileImage
              displayImage={displayImage}
              onPress={handleExpandBottomSheet}
            />

            <View className='flex-1 gap-4'>
              <CustomInput
                title='Full Name'
                control={control}
                name='displayName'
                placeholder='Fullname'
                error={errors.displayName?.message}
              />

              <Controller
                name='dateOfBirth'
                control={control}
                render={({ field: { value, onChange } }) => (
                  <CustomDatePicker
                    minimumDate={new Date(1970, 0, 1)}
                    maximumDate={new Date()}
                    onDateChange={onChange}
                    title='BirthDate'
                    value={new Date(value || '')}
                  />
                )}
              />

              <Controller
                control={control}
                name='phoneNumber'
                render={({ field: { onChange, value } }) => (
                  <CustomPhoneInput
                    isOptional
                    title='Phone Number'
                    countryCode={watch('countryCode') || ''}
                    onCountryCodeChange={(e) => setValue('countryCode', e)}
                    phoneNumber={value || ''}
                    onPhoneNumberChange={onChange}
                    error={
                      errors.phoneNumber?.message || errors.countryCode?.message
                    }
                  />
                )}
              />
              <CustomText className='text-white-50 -mb-2 text-h4'>
                Gender
              </CustomText>
              <View className='gap-4 flex-row'>
                <Controller
                  control={control}
                  name='gender'
                  render={({ field: { onChange, value } }) => (
                    <Fragment>
                      <CustomRadioWithText
                        checked={value === GENDER_ENUM.MALE}
                        title={'Male'}
                        onPress={() => onChange(GENDER_ENUM.MALE)}
                        buttonStyles='flex items-center flex-1 justify-between flex-row bg-white-50/10 px-4 py-3 rounded-xl'
                      />

                      <CustomRadioWithText
                        checked={value === GENDER_ENUM.FEMALE}
                        title={'Female'}
                        onPress={() => onChange(GENDER_ENUM.FEMALE)}
                        buttonStyles='flex items-center flex-1 justify-between flex-row bg-white-50/10 px-4 py-3 rounded-xl'
                      />
                    </Fragment>
                  )}
                />
              </View>
            </View>
          </ScrollView>
        </CustomKeyboardAvoidingView>
        <CustomButton onPress={handleSaveProfilePress} loading={isLoading}>
          Next
        </CustomButton>
      </View>

      <BottomSheet
        ref={bottomSheetRef}
        snapPoints={snapPoints}
        index={-1}
        onChange={handleSheetChanges}
        enablePanDownToClose
        backdropComponent={renderBackdrop}
        backgroundStyle={styles.bottomSheetBackground}
        handleIndicatorStyle={styles.handleIndicator}
      >
        <BottomSheetContent
          onUpload={handleUploadImage}
          onRemove={handleRemoveImagePress}
        />
      </BottomSheet>
    </ScreenTemplate>
  );
};

export default memo(RegisterUserData);

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 48,
    objectFit: 'cover',
  },
  listStyles: {
    flexGrow: 1,
    justifyContent: 'space-between',
    paddingBottom: normalized(20),
  },
  bottomSheetBackground: {
    backgroundColor: COLORS.neutral[900],
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
  },
  handleIndicator: {
    backgroundColor: COLORS.white[50],
    width: 40,
    height: 4,
    borderRadius: 2,
  },
});
