import {
  Keyboard,
  KeyboardAvoidingView,
  Platform,
  Text,
  TouchableOpacity,
  TouchableWithoutFeedback,
  View,
} from 'react-native';
import { useEffect, useState, useRef, useMemo } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { useLocalSearchParams, useRouter } from 'expo-router';
import GoBack from '@/layouts/GoBack';
import OTPInputs from '@/components/ui/OTPInputs';
import useValidateOTP from '@/hooks/AuthHooks/useValidateOTP';
import CustomButton from '@/components/ui/buttons/CustomButton';
import useResendAuthOTP from '@/hooks/AuthHooks/useResendAuthOTP';
import { useSession } from '@/context/AuthContext';
import { customToast } from '@/hooks/useCustomToast';

const OtpScreen = () => {
  const router = useRouter();
  const { user } = useSession();

  const { type, email, userId } = useLocalSearchParams() as {
    type?: 'reset' | 'register' | 'verify';
    email: string;
    userId?: string;
  };

  const [timer, setTimer] = useState(60);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  const { loading: resendOTPLoading, handleResendAuthOTP } = useResendAuthOTP();
  const {
    otpError,
    handleOtpChange,
    handleSubmit,
    isValid,
    loading,
    OTP,
    isSuccess,
  } = useValidateOTP();

  useEffect(() => {
    // Send OTP to the email address only if coming from a redirect
    const emailToUse = user?.email || email || '';
    if (emailToUse && !OTP) {
      // Only send if no OTP exists yet
      const initialSend = async () => {
        handleResendAuthOTP({ email: emailToUse });
      };
      initialSend();
    }
    // Empty dependency array since we only want this to run once on mount
  }, []);
  // Handle redirection on success
  useEffect(() => {
    if (isSuccess) {
      // Determine where to redirect based on the type parameter
      if (type === 'reset') {
        // For password reset flow
        router.replace({
          pathname: '/auth/newPassword',
          params: { email: user?.email || email },
        });
      } else if (type === 'verify') {
        // For account verification flow - redirect to main app after verification
        customToast('Account verified successfully!', 'success');
        router.replace('/(protected)/(tabs)');
      } else {
        // Default case (register flow)
        router.replace({
          pathname: '/auth/registerProfileSetup',
          params: { userId: userId || user?._id },
        });
      }
    }
  }, [isSuccess, type, router, user, email, userId]);

  // Timer countdown effect
  useEffect(() => {
    if (timer > 0) {
      timerRef.current = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
    }

    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, [timer]);

  const isResendDisabled = useMemo(() => timer > 0, [timer]);

  return (
    <ScreenTemplate>
      <GoBack />
      <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
        <View className='flex-1 px-3 pb-9'>
          <View className='flex-1 justify-center'>
            <Text className='text-white-50 text-center text-3xl font-poppins font-semibold'>
              {type === 'verify'
                ? 'Verify Account'
                : type === 'reset'
                  ? 'Reset Password'
                  : 'OTP Code'}
            </Text>
            <View className='items-center gap-1 justify-center'>
              <Text className='text-neutral-200 font-poppins text-h4'>
                We sent the OTP code via email
              </Text>
              <Text className='text-white-50 font-poppins font-semibold text-h4'>
                to {user?.email || email}
              </Text>
            </View>
          </View>

          <KeyboardAvoidingView
            behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
            style={{ flex: 1 }}
          >
            <View className='h-[80px]'>
              <OTPInputs
                onTextChange={handleOtpChange}
                isValid={isValid}
                error={otpError || ''}
              />
            </View>
            <View className='flex-row items-center justify-center gap-1'>
              <Text className='text-center font-poppins text-h5 font-light text-neutral-200'>
                Wait for 00:{timer}
              </Text>
              <TouchableOpacity
                onPress={() => {
                  if (!isResendDisabled) {
                    handleResendAuthOTP({ email: user?.email || email || '' });
                    setTimer(60);
                  }
                }}
                disabled={isResendDisabled}
              >
                <Text
                  className={`text-h5 font-semibold font-poppins ${
                    isResendDisabled ? 'text-neutral-400' : 'text-secondary-300'
                  }`}
                >
                  Send Again
                </Text>
              </TouchableOpacity>
            </View>
          </KeyboardAvoidingView>

          {otpError && (
            <Text className='text-center text-danger font-poppins'>
              {otpError}
            </Text>
          )}

          <CustomButton
            loading={resendOTPLoading || loading}
            onPress={() =>
              handleSubmit({
                otp: OTP || '',
                email: user?.email || email || '',
              })
            }
            variant='primary'
          >
            {type === 'reset' ? 'Confirm Password' : 'Verify Account'}
          </CustomButton>
        </View>
      </TouchableWithoutFeedback>
    </ScreenTemplate>
  );
};

export default OtpScreen;
