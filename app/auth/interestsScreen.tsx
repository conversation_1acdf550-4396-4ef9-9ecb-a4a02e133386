import { View, FlatList } from 'react-native';
import React, { useCallback, useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomButton from '@/components/ui/buttons/CustomButton';
import CustomHeader from '@/components/ui/CustomHeader';
import InterestsComponent from '@/components/InterestsComponent';

import useUpdateInterests from '@/hooks/userHooks/useUpdateInterests';
import useGetAllInterests from '@/hooks/interestsHooks/useGetAllInterests';
import { IInterestItem } from '@/types';
import { router } from 'expo-router';

const InterestsScreen = () => {
  const { data } = useGetAllInterests();
  const { mutate, isPending } = useUpdateInterests();

  const [checkForm, setCheckForm] = useState<string[]>([]);

  const handleCheckBoxes = useCallback(
    (item: IInterestItem) => {
      setCheckForm((prev) => {
        const isExists = prev.some((v) => v === item.id);
        return isExists
          ? prev.filter((v) => v !== item.id) // Remove if already exists
          : [...prev, item.id]; // Add if not exists
      });
    },
    [setCheckForm],
  );
  const handleSubmit = useCallback(() => {
    // if (!checkForm.length) return;

    const formData = new FormData();

    formData.append('interest', checkForm.map((v) => v).join(','));
    router.replace('/(protected)/(tabs)');

    mutate(formData);
  }, [checkForm]);

  return (
    <ScreenTemplate>
      <View className='flex-1 px-2'>
        {/* Title and go back */}
        <View className='my-3'>
          <CustomHeader title='What are your interests?' />
        </View>

        {/* Checklist */}
        <FlatList
          className='flex-1'
          showsVerticalScrollIndicator={false}
          data={data?.data}
          keyExtractor={(item) => item.id}
          renderItem={({ item }) => (
            <InterestsComponent
              name={item.name}
              onCheck={() => handleCheckBoxes(item)}
              isChecked={checkForm?.some((v) => v === item.id) || false}
            />
          )}
          contentContainerStyle={{ paddingBottom: 20 }}
        />

        {/* Submit Button */}
        <CustomButton onPress={handleSubmit} loading={isPending}>
          {isPending ? 'Saving...' : 'Finish'}
        </CustomButton>
      </View>
    </ScreenTemplate>
  );
};

export default InterestsScreen;
