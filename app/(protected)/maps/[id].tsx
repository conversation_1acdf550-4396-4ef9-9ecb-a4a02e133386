import { StyleSheet, View } from 'react-native';
import React, { useEffect, useRef } from 'react';
import MapView, { Circle, Marker, PROVIDER_GOOGLE } from 'react-native-maps';
import { COLORS } from '@/constants/Theme';
import { APP_Icons } from '@/constants/Images';
import GoBack from '@/layouts/GoBack';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useLocalSearchParams } from 'expo-router';

/**
 * This takes (id , lat , lng ,radius  ) as string as params
 * user to show pin and radius on map
 * the default radius is 5Km
 */

const ShowLocation = () => {
  const { id, lat, lng, radius } = useLocalSearchParams<{
    id: string;
    lat: string;
    lng: string;
    radius?: string;
  }>();
  const mapRef = useRef<MapView | null>(null);
  const inset = useSafeAreaInsets();

  useEffect(() => {
    if (lat && lng && mapRef.current) {
      const timeOut = setTimeout(() => {
        mapRef.current?.animateCamera(
          {
            center: {
              latitude: Number(lat || 0),
              longitude: Number(lng || 0),
            },
            zoom: 10, // Increased zoom for better focus
          },
          { duration: 500 },
        );
      }, 500); // Delay to allow MapView to load first
      return () => clearTimeout(timeOut);
    }
  }, [id, lat, lng]); // Runs when `location` updates

  return (
    <View className='flex-1'>
      <View className='absolute z-10 px-2'
style={{ marginTop: inset.top }}>
        <GoBack iconColor={COLORS.neutral[800]} />
      </View>

      <MapView
        provider={PROVIDER_GOOGLE}
        ref={mapRef}
        style={StyleSheet.absoluteFill}
        initialRegion={{
          latitude: Number(lat || 0),
          longitude: Number(lng || 0),
          latitudeDelta: 0.05,
          longitudeDelta: 0.05,
        }}
      >
        {lat && lng && (
          <>
            <Circle
              center={{
                latitude: Number(lat || 0),
                longitude: Number(lng || 0),
              }}
              radius={radius ? Number(radius) : 5000} // 5KM radius
              fillColor={COLORS.accent[100]}
              strokeColor={COLORS.accent[200]}
            />
            <Marker
              coordinate={{
                latitude: Number(lat || 0),
                longitude: Number(lng || 0),
              }}
              key={`${Number(lat || 0)}-${Number(lng || 0)}`} // Ensures re-render
            >
              <APP_Icons.MapPin width={35}
height={35} />
            </Marker>
          </>
        )}
      </MapView>
    </View>
  );
};

export default ShowLocation;
