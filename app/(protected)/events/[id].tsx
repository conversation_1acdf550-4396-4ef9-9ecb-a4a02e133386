import React, { useCallback, useMemo, useState } from 'react';
import { Pressable, ScrollView, StyleSheet, View } from 'react-native';

import { router, useLocalSearchParams } from 'expo-router';
import { SCREEN_HEIGHT, SCREEN_WIDTH, SIZES } from '@/constants/Theme';
import { useQuery } from '@tanstack/react-query';
import { APP_Icons } from '@/constants/Images';

// Utils and APIS
import { getEventsByIdAPI } from '@/services/eventsApi';
import { formatDateDayMonthWeek } from '@/utils/formatDates';

// Components
import GoBack from '@/layouts/GoBack';
import CustomText from '@/components/ui/CustomText';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import Loading from '@/layouts/Loading';
import ShowMore from '@/components/showMore';
import SmallLocationMap from '@/components/SmallLocationMap';
import Slider from '@/components/ui/Slider/Slider';
import { useEventsStore } from '@/stores/useEventsStore';

const EventsByID = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { selectedEvent } = useEventsStore();
  const [showFullDescription, setShowFullDescription] = useState(false);

  // To always ensure the data is fresh
  const { data, isLoading } = useQuery({
    queryKey: ['events-by-id', id],
    queryFn: async () => getEventsByIdAPI(id),
    enabled: !selectedEvent && !!id, // Only fetch if the selectedEvent is NOT found and id is available
  });

  const EventData = useMemo(
    () => selectedEvent || data?.data,
    [selectedEvent, data?.data],
  );

  const handleSetMapLocation = useCallback(() => {
    router.push({
      pathname: '/(protected)/maps/[id]',
      params: {
        id: EventData?._id || '',
        lat: String(EventData?.location.latitude),
        lng: String(EventData?.location.longitude),
      },
    });
  }, []);

  if (isLoading) {
    return (
      <ScreenTemplate>
        <View className='flex-1 justify-center items-center'>
          <Loading isLoading size={24} />
        </View>
      </ScreenTemplate>
    );
  }

  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <ScrollView className='flex-1 gap-3' showsVerticalScrollIndicator={false}>
        <View className='my-3'>
          <GoBack />
        </View>

        {/* Header Slider  & Title */}
        <View className='gap-2'>
          <Slider
            itemList={EventData?.images || []}
            imageStyles={styles.image}
            showPagination={false}
            showControls={false}
            autoPlay={true}
            autoPlayInterval={6000}
          />
          <CustomText className='text-white-50 px-2 text-h1 font-semibold mb-3 '>
            {EventData?.name}
          </CustomText>
        </View>

        {/* Second section */}
        <View className='gap-3'>
          {/* Location section */}
          <View className='flex-row items-center gap-3'>
            <APP_Icons.LocationIcon width={20} height={20} />
            <CustomText className='text-neutral-100 text-h4'>
              {EventData?.country || ''} {EventData?.location?.address || ''}{' '}
              {EventData?.city || ''}
            </CustomText>
          </View>
          {/* Date section */}
          <View className='flex-row items-center gap-3'>
            <APP_Icons.CALENDER width={20} height={20} />
            <CustomText className='text-neutral-100 text-h4'>
              {formatDateDayMonthWeek(EventData?.startDate)}
            </CustomText>
          </View>
          {/* Time section */}
          <View className='flex-row items-center gap-3'>
            <APP_Icons.ClockIcon width={20} height={20} />
            <CustomText className='text-neutral-100 text-h4'>
              {EventData?.startTime} : {EventData?.endTime}
            </CustomText>
          </View>
          {/* Price Section */}
          <View className='flex-row items-center gap-3'>
            <APP_Icons.TicketIcon width={20} height={20} />
            <View className='flex-row items-center gap-3'>
              <CustomText className='text-neutral-100 text-h3 font-semibold'>
                {EventData?.price || 0}
              </CustomText>
              <APP_Icons.SARIcon width={20} height={30} />
            </View>
          </View>
        </View>

        {/* Description */}
        <View className='gap-2.5 mt-3'>
          <CustomText className='text-white-50 text-h2 font-medium'>
            Event Details
          </CustomText>
          {EventData && EventData?.description && (
            <>
              <CustomText
                className='text-neutral-200 text-h4'
                numberOfLines={showFullDescription ? undefined : 5}
                ellipsizeMode='tail'
              >
                {EventData.description}
              </CustomText>
              {EventData.description.length > 100 && (
                <ShowMore
                  onPress={() => setShowFullDescription(!showFullDescription)}
                  title={showFullDescription ? 'Show less' : 'Show more'}
                  isActive={showFullDescription}
                  textProps={{ className: 'text-secondary-300 text-h3' }}
                />
              )}
            </>
          )}
        </View>

        {/* Location  */}

        <View className='mt-3 gap-2.5'>
          <CustomText className='text-white-50 font-medium text-h2'>
            Location
          </CustomText>

          <Pressable
            onPress={handleSetMapLocation}
            className='absolute inset-0 h-full w-full bg-transparent'
            style={{ zIndex: 10 }} // Ensure it stays above other elements
          />
          <View style={{ zIndex: 1, height: 123 }}>
            <SmallLocationMap
              latitude={EventData?.location.latitude || 0}
              longitude={EventData?.location.longitude || 0}
            />
          </View>
        </View>

        {/* Tags  */}

        <View className='gap-2.5 my-3'>
          <CustomText className='text-white-50 font-medium text-h2'>
            Tags
          </CustomText>
        </View>
      </ScrollView>
    </ScreenTemplate>
  );
};

export default EventsByID;

const styles = StyleSheet.create({
  image: {
    height: SCREEN_HEIGHT / 4,
    width: SCREEN_WIDTH,
    borderRadius: 15,
    backgroundColor: 'rgba(0,0,0,0.2)',
    objectFit: 'cover',
  },
});
