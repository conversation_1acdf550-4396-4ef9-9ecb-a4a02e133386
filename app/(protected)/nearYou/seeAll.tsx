import React, { useCallback, useEffect, useMemo, useState } from 'react';
import CustomHeader from '@/components/ui/CustomHeader';
import { ActivityIndicator, FlatList, Pressable, View } from 'react-native';

import ScreenTemplate from '@/layouts/ScreenTemplate';
import ActivityCard from '@/components/ActivityCard';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import FilterButton from '@/components/FilterButton';
import CustomText from '@/components/ui/CustomText';

import SearchBar from '@/layouts/SearchBar';
import { Link } from 'expo-router';

import NearYouFilterBottomSheet from '@/components/NearYouComponents/NearYouFilterBottomSheet';
import useGetAllActivities from '@/hooks/activityHooks/useGetAllActivities';

import { getDayNumber, getMonthFromDate } from '@/utils/formatDates';
import { COLORS } from '@/constants/Theme';

const FilterNearYouScreen = () => {
  const [showFilterModal, setShowFilterModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [search, setSearch] = useState({
    activityType: 'online',
    category: [] as string[],
    date: 'all' as 'all' | 'today' | 'tomorrow' | 'this-week',
    prices: '',
  });

  const toggleFilterModal = useCallback(() => {
    setShowFilterModal((prev) => !prev);
  }, []);

  // Handle search input changes with debouncing (built into SearchBar)
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
    setIsSearching(!!text);
  }, []);

  const getEndDate = useCallback(
    (date: typeof search.date) => {
      const now = new Date();
      switch (date) {
        case 'today':
          return now.getTime();
        case 'tomorrow':
          return now.getTime() + 86400000;
        case 'this-week':
          return now.getTime() + 604800000;
        default:
          return undefined;
      }
    },
    [search],
  );

  const searchParams = useMemo(
    () => ({
      limit: 20,
      categories: search.category.join(','),
      endDate:
        search.date === 'all' ? undefined : String(getEndDate(search.date)),
      name: searchQuery, // Add search query parameter
    }),
    [search, getEndDate, searchQuery],
  );

  const { data, isLoading, isFetching } = useGetAllActivities(searchParams);

  // Memoize the list data to prevent unnecessary re-renders
  const listData = useMemo(
    () => data?.pages?.flatMap((page) => page.data) || [],
    [data],
  );

  // Show loading state for initial data fetch
  const isInitialLoading = isLoading && !listData.length;

  const handleApplyFilters = useCallback((filterData: typeof search) => {
    setSearch((prev) => ({
      ...prev,
      ...filterData,
    }));
  }, []);

  // Memoized render item function for better performance
  const renderItem = useCallback(
    ({ item }: any) => (
      <Link href={`/(protected)/activity/${item._id}`} asChild>
        <Pressable>
          <ActivityCard
            cover={item.images?.[0]}
            day={item.startDate && getDayNumber(item.startDate)}
            month={item.startDate && getMonthFromDate(item.startDate)}
            location={item.location?.address || ''}
            title={item.name}
            currency={item.currency}
            price={Number(item.price || 0)}
            members={item.members}
          />
        </Pressable>
      </Link>
    ),
    [], // No dependencies needed as this is a pure render function
  );

  return (
    <ScreenTemplate>
      <View className='px-2 flex-1 gap-4'>
        <CustomHeader title='Near You' />

        <View className='flex-row gap-3 justify-between items-center'>
          <SearchBar
            placeholder='Search activities...'
            containerStyles='flex-1'
            onSearch={handleSearch}
            value={searchQuery}
            debounceTime={500}
          />
          <FilterButton onPress={toggleFilterModal} />
        </View>

        {/* Initial loading state */}
        {isInitialLoading && (
          <View className='flex-1 justify-center items-center'>
            <ActivityIndicator size="large" color={COLORS.secondary[300]} />
            <CustomText style={{ marginTop: 16, color: COLORS.neutral[400] }}>
              Loading activities...
            </CustomText>
          </View>
        )}

        {/* Search status indicator */}
        {searchQuery && !isInitialLoading && (
          <View className='flex-row justify-between items-center px-2'>
            <CustomText style={{ color: COLORS.neutral[400] }}>
              {isLoading || isFetching
                ? 'Searching...'
                : `Found ${listData.length} activities`}
            </CustomText>
            {searchQuery && (
              <Pressable onPress={() => handleSearch('')}>
                <CustomText style={{ color: COLORS.secondary[300] }}>
                  Clear search
                </CustomText>
              </Pressable>
            )}
          </View>
        )}

        {!isInitialLoading && (
          <FlatList
          className='flex-1 py-5'
          contentContainerClassName='gap-4 pb-24'
          data={listData}
          keyExtractor={(item) => item._id ?? Math.random().toString()}
          showsVerticalScrollIndicator={false}

          // Loading indicator
          ListHeaderComponent={
            (isLoading || isFetching) && !isInitialLoading ? (
              <View className='py-4 items-center'>
                <ActivityIndicator color={COLORS.secondary[300]} />
              </View>
            ) : null
          }

          // Empty state
          ListEmptyComponent={
            !isLoading && !isFetching ? (
              <EmptyListComponent
                title={searchQuery ? 'No matching activities found' : 'No Activities Yet!'}
                description={
                  searchQuery
                    ? 'Try adjusting your search or filters'
                    : 'It looks like there are no activities planned right now.'
                }
              />
            ) : null
          }

          // Performance optimizations
          removeClippedSubviews={true}
          maxToRenderPerBatch={8}
          windowSize={10}
          initialNumToRender={6}
          updateCellsBatchingPeriod={50}

          renderItem={renderItem}
        />
        )}
      </View>

      <NearYouFilterBottomSheet
        onApply={handleApplyFilters}
        setShowFilterModal={setShowFilterModal}
        showFilterModal={showFilterModal}
      />
    </ScreenTemplate>
  );
};

export default FilterNearYouScreen;
