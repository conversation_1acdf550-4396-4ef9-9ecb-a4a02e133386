import { Tabs } from 'expo-router';
import { Platform } from 'react-native';
import TabBar from '@/layouts/TabBar';

export default function TabLayout() {
  return (
    <Tabs
      screenOptions={{
        headerShown: false,
        // Enable lazy loading for better performance

        // Android performance optimizations
        ...(Platform.OS === 'android' && {
          // Keep screens mounted for faster switching
          unmountOnBlur: false,
          // Enable animations but keep them fast
          animationEnabled: true,
        }),
      }}
      tabBar={(props) => <TabBar {...props} />}
    >
      <Tabs.Screen name='index' />
      <Tabs.Screen name='activities' />
      <Tabs.Screen name='socialCircles' />
      <Tabs.Screen name='messages' />
      <Tabs.Screen name='profile' />
    </Tabs>
  );
}
