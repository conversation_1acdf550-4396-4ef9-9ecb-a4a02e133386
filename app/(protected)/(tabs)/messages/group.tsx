import React from 'react';
import { View, SectionList } from 'react-native';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import { APP_Icons } from '@/constants/Images';
import ChatListItem from '@/components/chatComponents/ChatListItem';
import useGetGroupRooms from '@/hooks/chatHooks/useGetGroupRooms';
import CustomText from '@/components/ui/CustomText';

const GroupMessagesScreen = () => {
  const bottomHeight = useBottomTabBarHeight();
  const { data } = useGetGroupRooms();

  const sections = [
    {
      title: 'Created Groups',
      data: data?.createdRooms || [],
    },
    {
      title: 'Joined Groups',
      data: data?.joinedRooms || [],
    },
  ].filter((section) => section.data.length > 0); // Remove empty sections

  return (
    <View className='flex-1 gap-3 px-2'>
      {/* Group Chat Section List */}
      <View className='flex-1 pt-4'>
        <SectionList
          sections={sections}
          keyExtractor={(item) => item._id}
          showsVerticalScrollIndicator={false}
          contentContainerClassName='gap-5'
          className={`flex-1 mb-[${bottomHeight}px]`}
          stickySectionHeadersEnabled={false}
          renderSectionHeader={({ section: { title } }) => (
            <CustomText className='text-lg font-semibold text-neutral-700 mb-2'>
              {title}
            </CustomText>
          )}
          ListEmptyComponent={() => (
            <EmptyListComponent
              title='No Group Conversations Yet'
              description='Create or join a group to start chatting'
              emptyIcon={() => (
                <APP_Icons.EmptyMessageIcon width={70} height={70} />
              )}
            />
          )}
          renderItem={({ item }) => (
            <ChatListItem
              createdAt={item.createdAt || new Date()}
              message=''
              isOnline={false}
              title={item.title}
              image={item.image}
              numberOfMessages={item.unseenCount}
              roomId={item._id}
              type='group'
              href={'/(protected)/messages/[id]'}
            />
          )}
        />
      </View>
    </View>
  );
};

export default GroupMessagesScreen;
