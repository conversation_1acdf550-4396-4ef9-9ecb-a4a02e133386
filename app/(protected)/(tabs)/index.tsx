import React, { useCallback, useEffect, useState } from 'react';
import { View } from 'react-native';
import { ScrollView } from 'react-native-virtualized-view';

import { useTranslation } from 'react-i18next';
// Theme
import { COLORS } from '@/constants/Theme';
// Layout
import ScreenTemplate from '@/layouts/ScreenTemplate';
// Components
import HomeHeader from '@/components/HomeHeader';
import SearchBar from '@/layouts/SearchBar';
import TapToFindActivitiesNearBy from '@/components/TapToFindActivitiesNearBy';
import ShortsList from '@/components/shortsComponents/ShortsList';
import CategoriesButtonsList from '@/components/CategoriesButtonsList';
import LeftAndRightTitleLink from '@/components/LeftAndRightTitleLink';
import ExploreAttractionsList from '@/components/ExploreAttractionsList';
import EventsCardsList from '@/components/EventsCardsList';
// Query Hooks
import useGetUserDataByID from '@/hooks/userHooks/useGetUserDataByID';

import { useSession } from '@/context/AuthContext';
import NearYouList from '@/components/NearYouList';
import { RefreshControl } from 'react-native-gesture-handler';
import { router } from 'expo-router';

const HomeScreen = () => {
  const { setUser, userId } = useSession();
  const { t } = useTranslation();
  const [searchQuery, setSearchQuery] = useState('');

  // Data fetching hooks - keep as is since React Query handles caching
  const {
    isSuccess: userDataSuccess,
    data: userData,
    refetch: userRefetch,
    isLoading: userLoading,
  } = useGetUserDataByID(userId || '');

  // Memoize all data transformations
  useEffect(() => {
    if (userDataSuccess && userData.user) {
      setUser(userData.user);
    }
  }, [userDataSuccess, userData, setUser]);

  // Memoize all callbacks
  const handleSearchChange = useCallback(
    (query: string) => setSearchQuery(query),
    [],
  );

  return (
    <ScreenTemplate>
      <ScrollView
        showsVerticalScrollIndicator={false}
        className='flex-1 px-2'
        refreshControl={
          <RefreshControl refreshing={userLoading} onRefresh={userRefetch} />
        }
      >
        {/* Header */}
        <HomeHeader />

        <TapToFindActivitiesNearBy />

        <View className='mb-5'>
          <SearchBar
            editable={false}
            onPress={() => router.push('/(protected)/search/SearchScreen')}
            value={searchQuery}
            onChangeText={handleSearchChange}
            placeholder={`${t('search')}...`}
            placeholderTextColor={COLORS.gray_colors[200]}
          />
        </View>

        {/* Shorts  */}
        <ShortsList />

        {/* Categories */}
        <View className='gap-5 my-8'>
          <LeftAndRightTitleLink
            linkTitle={t('see-all')}
            title={t('Categories')}
            linkHref={'/(protected)/categories/seeAll'}
          />
          <CategoriesButtonsList />
        </View>

        {/* Near You List */}
        <View className='gap-5 my-8 flex-1'>
          <LeftAndRightTitleLink
            linkTitle={t('see-all')}
            title={t('near-you')}
            linkHref={'/(protected)/nearYou/seeAll'}
          />
          <NearYouList />
        </View>

        <View className='gap-5 my-8'>
          <LeftAndRightTitleLink
            linkTitle={t('see-all')}
            title={t('top-events')}
            linkHref={'/(protected)/events/topEvents'}
          />
          <EventsCardsList />
        </View>

        <View className='gap-5 my-8'>
          <LeftAndRightTitleLink
            linkTitle={t('see-all')}
            title={t('Explore Attractions')}
            linkHref={'/(protected)/explore/exploreAttractions'}
          />

          <ExploreAttractionsList />
        </View>
      </ScrollView>
    </ScreenTemplate>
  );
};

export default HomeScreen;
