import React, { memo, useCallback, useMemo } from 'react';
import { View, TouchableOpacity, StyleSheet, Pressable } from 'react-native';
import { useRouter } from 'expo-router';

import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';

import CustomText from '@/components/ui/CustomText';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import TabScreenWrapper from '@/components/TabScreenWrapper';

import CustomDropDownMenu from '@/components/ui/CustomDropDownMenu';
import { useSession } from '@/context/AuthContext';

import useGetSocialCircles from '@/hooks/circlesHooks/useGetSocialCircles';

import CirclesPostsList from '@/components/SocialCirclesComponents/CirclesPostsList';
import CirclesHorizontalList from '@/components/SocialCirclesComponents/CirclesHorizontalList';
import { useTranslation } from 'react-i18next';
import useGetPosts from '@/hooks/profileHooks/useGetPosts';
import { useGetFollowersPosts } from '@/hooks/circlesHooks/useGetFollowersPosts';

const SocialCirclesScreen = memo(() => {
  const { t } = useTranslation();
  const router = useRouter();
  const { userId } = useSession();

  // Fetch social circles
  const { data, isLoading } = useGetSocialCircles(userId || '');

  const {
    data: allPosts,
    isLoading: allPostsLoading,
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  } = useGetFollowersPosts(10);

  // Memoize posts data to prevent unnecessary re-renders
  const posts = useMemo(() =>
    allPosts?.pages?.flatMap((page) => page.data) ?? [],
    [allPosts]
  );

  // Memoize dropdown handler
  const handleSelectDropDown = useCallback((key: string) => {
    if (key === 'add-post') {
      router.push('/(protected)/socialCircles/createPost');
    } else {
      router.push('/(protected)/socialCircles/createSocialCircle');
    }
  }, [router]);

  // Memoize dropdown items
  const dropdownItems = useMemo(() => [
    { title: 'Add New Post', key: 'add-post' },
    { key: 'create-socialCircle', title: 'Create socialCircle' },
  ], []);

  // Memoize see all handler
  const handleSeeAll = useCallback(() => {
    router.push('/(protected)/socialCircles/seeAll');
  }, [router]);

  return (
    <TabScreenWrapper screenName="socialCircles">
      <ScreenTemplate>
        <View className='px-2 flex-1 pb-8'>
          {/* Header */}
          <View className='mb-3'>
            <CustomHeader>
              <CustomDropDownMenu
                onSelect={handleSelectDropDown}
                items={dropdownItems}
                triggerElement={
                  <TouchableOpacity style={styles.addButton}>
                    <Ionicons
                      name='add'
                      size={normalized(24)}
                      color={COLORS.white[50]}
                    />
                  </TouchableOpacity>
                }
              />
            </CustomHeader>
          </View>

          {/* Social Circles */}
          <View className='gap-4'>
            <View style={styles.headerRow}>
              <CustomText style={styles.socialCirclesHeader}>
                My Social Circles
              </CustomText>
              <Pressable onPress={handleSeeAll}>
                <CustomText style={styles.seeAllLink}> {t('see-all')}</CustomText>
              </Pressable>
            </View>

            {/* Social circles Horizontal list */}
            <CirclesHorizontalList
              isLoading={isLoading}
              data={data?.data?.socialCircles || []}
            />
          </View>

          {/* Posts List */}
          <CirclesPostsList
            data={posts}
            isLoading={isLoading || allPostsLoading}
            fetchNextPage={fetchNextPage}
            hasNextPage={hasNextPage}
            isFetchingNextPage={isFetchingNextPage}
          />
        </View>
      </ScreenTemplate>
    </TabScreenWrapper>
  );
});

const styles = StyleSheet.create({
  addButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    padding: 4,
    borderRadius: 30,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.2)',
    position: 'relative',
  },

  headerRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  socialCirclesHeader: {
    fontSize: 13,
    color: COLORS.white[50],
    fontWeight: 'bold',
  },
  seeAllLink: {
    color: COLORS.secondary[300],
    fontSize: 13,
  },
});

export default SocialCirclesScreen;
