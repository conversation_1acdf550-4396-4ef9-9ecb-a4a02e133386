import { FlatList, View } from 'react-native';
import React, { useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import SearchBar from '@/layouts/SearchBar';
import { t } from 'i18next';
import RequestItem from '@/components/ui/RequestItem';
import CustomText from '@/components/ui/CustomText';

// const comments = generateComments(30);

const MessageRequests = () => {
  const [search, setSearch] = useState('');

  // const memoList = useMemo(() => {
  //   return search.length > 0
  //     ? comments.filter((v) =>
  //         v.userId.displayName
  //           .toLocaleLowerCase()
  //           .includes(search.toLocaleLowerCase()),
  //       )
  //     : comments;
  // }, [search]);

  return (
    <ScreenTemplate>
      <View className='px-2 flex-1 gap-3'>
        <CustomHeader
          href='/(protected)/(tabs)/messages'
          title='Message Requests'
        />

        {/* Search */}
        <SearchBar
          value={search}
          onChangeText={setSearch}
          placeholder={t('search') + '...'}
        />

        <View className='flex-1'>
          <FlatList
            className='flex-1'
            contentContainerClassName='pb-10 px-2'
            data={[]}
            showsVerticalScrollIndicator={false}
            initialNumToRender={15}
            maxToRenderPerBatch={20}
            ListEmptyComponent={() => (
              <View className='flex-1 justify-center items-center'>
                <CustomText className='text-neutral-500 text-h5'>
                  No Requests
                </CustomText>
              </View>
            )}
            ItemSeparatorComponent={() => (
              <View className='border-hairline border-neutral-500/20 my-5' />
            )}
            keyExtractor={(_, index) => `Message-${index.toString()}`}
            renderItem={({ item }) => (
              <></>
              // <RequestItem
              //   user={{ displayName: item.user.name, image: item.user.avatar }}
              //   onAccept={() => {}}
              //   onReject={() => {}}
              // >
              //   <CustomText className='text-neutral-500 text-h5'>
              //     {item.comment}
              //   </CustomText>
              // </RequestItem>
            )}
          />
        </View>
      </View>
    </ScreenTemplate>
  );
};

export default MessageRequests;
