import React, { useCallback, useState } from 'react';
import { View, TouchableOpacity, Text } from 'react-native';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { zodResolver } from '@hookform/resolvers/zod';
import { router } from 'expo-router';
import { Image } from 'expo-image';

import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import CustomInput from '@/components/ui/CustomInput';
import CustomButton from '@/components/ui/buttons/CustomButton';
import useCreateGroupChatRoom from '@/hooks/chatHooks/useCreateGroupChatRoom';
import useImagePicker from '@/hooks/useImagePicker';
import { useSession } from '@/context/AuthContext';
import { APP_Icons } from '@/constants/Images';
import UserWithActionButton from '@/components/userWithActionButton';
import CustomText from '@/components/ui/CustomText';
import { COLORS } from '@/constants/Theme';
import { handleError } from '@/utils/errorHandler';
import useGetFollowing from '@/hooks/userHooks/useGetFollowing';
import OptimizedFlatList from '@/components/ui/OptimizedFlatList';

const createGroupSchema = z.object({
  groupName: z
    .string({ message: 'You must enter a group name.' })
    .min(1, { message: 'Group name must be at least 1 character long.' }),
});

type CreateGroupSchema = z.infer<typeof createGroupSchema>;

const CreateGroupMessage = () => {
  const { user } = useSession();
  const { data: following } = useGetFollowing(user?._id!);

  const [selectedMembers, setSelectedMembers] = useState<string[]>([]);

  const { pickImage, result } = useImagePicker({
    imagePickOptions: { allowsEditing: false, selectionLimit: 1 },
  });

  const { mutate, isPending } = useCreateGroupChatRoom();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<CreateGroupSchema>({
    defaultValues: { groupName: '' },
    resolver: zodResolver(createGroupSchema),
  });

  const toggleMember = (id: string) => {
    setSelectedMembers((prev) =>
      prev.includes(id) ? prev.filter((mid) => mid !== id) : [...prev, id],
    );
  };

  const handleCreateGroup = useCallback(
    (data: CreateGroupSchema) => {
      if (!user?._id) return;

      mutate(
        {
          title: data.groupName,
          members: [user._id, ...selectedMembers],
          image: result?.[0]?.uri ?? '',
        },
        {
          onSuccess: () => {
            router.replace('/(protected)/(tabs)/messages/group');
          },
          onError: (err) => {
            handleError(err);
          },
        },
      );
    },
    [user, result, mutate, selectedMembers],
  );

  return (
    <ScreenTemplate>
      <View className='flex-1 px-4 py-6 gap-6'>
        <CustomHeader title='Create Group' />

        {/* Avatar Picker */}
        <View className='items-center'>
          <TouchableOpacity
            className='w-24 h-24 rounded-full bg-neutral-200 items-center justify-center overflow-hidden'
            onPress={pickImage}
          >
            {result?.[0]?.uri ? (
              <Image
                source={{ uri: result[0].uri }}
                style={{ width: '100%', height: '100%' }}
                contentFit='cover'
              />
            ) : (
              <APP_Icons.GalleryIcon width={40} height={40} />
            )}
          </TouchableOpacity>
          <Text className='mt-2 text-sm text-neutral-500'>
            Tap to add image
          </Text>
        </View>

        {/* Group Name Input */}
        <CustomInput
          placeholder='Enter group name'
          name='groupName'
          control={control}
          error={errors.groupName?.message}
        />

        {/* Submit Button */}
        <CustomButton
          onPress={handleSubmit(handleCreateGroup)}
          disabled={isPending}
          className='mt-2'
        >
          {isPending ? 'Creating...' : 'Create Group'}
        </CustomButton>

        <CustomText className='text-neutral-200 text-body1 font-semibold'>
          Add Members
        </CustomText>

        <OptimizedFlatList
          data={(following?.following || []).map((user) => ({
            last_message: null,
            displayName: user.displayName,
            username: user.username,
            profile_picture: user.image,
            unseen_message_count: undefined,
            user_id: user._id,
            is_online: undefined,
            _id: user._id,
          }))}
          keyExtractor={(item) => item._id || ''}
          ItemSeparatorComponent={() => <View className='my-2' />}
          renderItem={({ item }) => {
            const isSelected = selectedMembers.includes(item?._id || '');

            return (
              <UserWithActionButton
                name={item.displayName}
                avatar={item.profile_picture}
                size={35}
              >
                <CustomButton
                  onPress={() => toggleMember(item._id)}
                  variant={isSelected ? 'outline' : 'primary'}
                  style={{
                    borderColor: isSelected
                      ? COLORS.danger
                      : COLORS.secondary[300],
                  }}
                  textStyle={{
                    color: isSelected ? COLORS.danger : COLORS.neutral[100],
                  }}
                >
                  {isSelected ? 'Remove' : 'Add'}
                </CustomButton>
              </UserWithActionButton>
            );
          }}
        />
      </View>
    </ScreenTemplate>
  );
};

export default CreateGroupMessage;
