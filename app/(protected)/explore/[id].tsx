import React, { useCallback, useEffect, useMemo, useState } from 'react';
import { FlatList, Platform, Pressable, View } from 'react-native';
import { useLocalSearchParams, useRouter } from 'expo-router';
import { normalized, SIZES } from '@/constants/Theme';
import * as Sharing from 'expo-sharing';

import CustomText from '@/components/ui/CustomText';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomTag from '@/components/ui/CustomTag';
import CustomBottomSheet from '@/components/ui/CustomBottomSheet';
import ExploreGalleryComponent from '@/components/ExploreGalleryComponent';
import ExploreByIDHeader from '@/components/ExploreByIDHeader';
import SmallLocationMap from '@/components/SmallLocationMap';
import ModalGalleryList from '@/components/ui/ModalGalleryList';
import CustomKeyboardAvoidingView from '@/components/ui/CustomKeyboardAvoidingView';

import useGetExploreReviews from '@/hooks/exploreHooks/useGetExploreReviews';
import useHandleExploreByID from '@/hooks/exploreHooks/useHandleExploreByID';
import useLocation from '@/hooks/useLocation';

import { IReviewItem } from '@/types';
import ExploreReviewsList from '@/components/ExploreComponents/ExploreReviewsList';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import useExploreStore from '@/stores/useExploreStore';

const sectionTitle = 'text-white-50 text-h2 font-medium mb-0.5';

const ExploreItemByID = () => {
  const router = useRouter();
  const { id } = useLocalSearchParams<{ id: string }>();
  const { storeItem: exploreItem } = useExploreStore();

  const { getLocationName } = useLocation();

  const inset = useSafeAreaInsets();

  // Store the comment input separately to prevent unnecessary renders
  const [showBottomGallery, setShowBottomGallery] = useState(false);
  const [address, setAddress] = useState('');
  // list array of images modal
  const [showImageList, setShowImageList] = useState(false);
  const [imageIndex, setImageIndex] = useState(0);

  const [reviews, setReviews] = useState<
    (IReviewItem & {
      user: {
        _id: string;
        displayName: string;
        image?: string;
      };
    })[]
  >([]);

  // Api and mutations
  // doesn't provide api if explore item is stored in redux
  const { data } = useHandleExploreByID({ id: exploreItem ? '' : id });
  const { data: reviewsData } = useGetExploreReviews(id);

  const exploreData = useMemo(
    () => (exploreItem ? exploreItem : data?.data[0]),
    [data?.data, exploreItem],
  );

  const shareLink = async () => {
    await Sharing.shareAsync(`https:/explore/${id}`);
  };

  const handleSetMapLocation = useCallback(() => {
    if (!exploreData) return;

    router.push({
      pathname: '/(protected)/maps/[id]',
      params: {
        id: exploreData?._id || '',
        lat: String(exploreData.location.latitude),
        lng: String(exploreData.location.longitude),
      },
    });
  }, [exploreData]);

  const handleShowListModal = useCallback((index: number) => {
    setShowBottomGallery(false);
    setShowImageList(true);
    setImageIndex(index ?? 0);
  }, []);

  useEffect(() => {
    if (exploreData) {
      getLocationName(
        exploreData.location.latitude,
        exploreData.location.longitude,
      ).then((response) => {
        setAddress(response?.fullAddress || '');
      });
    }
  }, [exploreData]);

  // Set reviews once there is reviews and this is used to make optimistic updates
  useEffect(() => {
    if (reviewsData?.reviews) {
      setReviews(
        reviewsData.reviews?.reviews?.map((review) => ({
          ...review,
          user: review?.userId || {
            _id: '',
            displayName: 'Unknown',
            image: '',
          },
          createdAt: new Date(),
        })),
      );
    }
  }, [reviewsData]);

  return (
    <ScreenTemplate withoutSafeAria style={{ paddingBottom: inset.bottom }}>
      <CustomKeyboardAvoidingView
        keyboardVerticalOffset={Platform.OS === 'ios' ? 32 : 0}
      >
        {/* Background Image */}
        <ExploreByIDHeader
          address={address}
          coverImage={exploreData?.coverImage || ''}
          onGalleryPress={() => setShowBottomGallery(true)}
          onShare={shareLink}
          rating={exploreData?.rating || { average: 0, count: 0 }}
          title={exploreData?.name || ''}
        />

        {/* Body Content  */}
        <View style={{ padding: SIZES.xxSmall, gap: normalized(10) }}>
          {/* Over view */}
          <View>
            <CustomText className={sectionTitle}>Overview</CustomText>
            <CustomText className='text-neutral-100 text-h5'>
              {exploreData?.description}
            </CustomText>
          </View>
          {/* Tags */}
          <View>
            <CustomText className={sectionTitle}>Tags</CustomText>
            <View className='flex-row items-center gap-2 flex-wrap my-2'>
              {exploreData?.tags.map((tag) => (
                <CustomTag title={`#${tag}`} key={tag} />
              ))}
            </View>
          </View>
          {/* Location */}
          <View>
            <CustomText className={sectionTitle}>Location</CustomText>
            <Pressable
              onPress={handleSetMapLocation}
              className='absolute inset-0 h-full w-full bg-transparent'
              style={{ zIndex: 10 }} // Ensure it stays above other elements
            />
            <View style={{ zIndex: 1, maxHeight: 120 }}>
              <SmallLocationMap
                latitude={exploreData?.location.latitude || 0}
                longitude={exploreData?.location.longitude || 0}
              />
            </View>
          </View>

          {/* Reviews List and input the component carries it's logic  */}
          <ExploreReviewsList
            reviews={reviews}
            setReviews={setReviews}
            exploreId={id}
          />
        </View>
      </CustomKeyboardAvoidingView>

      {/* Gallery bottom sheet  */}
      <CustomBottomSheet
        isActive={showBottomGallery}
        onClose={() => setShowBottomGallery(false)}
      >
        <FlatList
          data={['']}
          renderItem={null}
          ListHeaderComponent={() => (
            <ExploreGalleryComponent
              onPress={(index) => {
                handleShowListModal(index);
              }}
              images={exploreData?.images || []}
            />
          )}
        />
      </CustomBottomSheet>

      {/* List of images  */}
      <View
        className={` bg-black absolute inset-0 ${showImageList ? 'flex-1' : 'hidden'}`}
      >
        <ModalGalleryList
          images={exploreData?.images || []}
          onClose={() => setShowImageList(false)}
          visible={showImageList}
          initialIndex={imageIndex}
          counterContainerStyle={{
            position: 'absolute',
            top: 60,
            backgroundColor: 'transparent',
          }}
        />
      </View>
    </ScreenTemplate>
  );
};

export default ExploreItemByID;
