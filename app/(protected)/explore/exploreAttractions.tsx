import {
  ActivityIndicator,
  FlatList,
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { memo, useCallback, useEffect, useMemo, useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { COLORS, MAX_HEIGHT, normalized } from '@/constants/Theme';
import CustomHeader from '@/components/ui/CustomHeader';
import SearchBar from '@/layouts/SearchBar';
import { APP_Icons } from '@/constants/Images';
import useGetExploreQuery from '@/hooks/exploreHooks/useGetExplore';
import ExploreAttractionCard from '@/components/ExploreAttractionCard';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

import CustomBottomSheet from '@/components/ui/CustomBottomSheet';
import Loading from '@/layouts/Loading';
import { generateUniqueCode } from '@/utils/common';
import CustomText from '@/components/ui/CustomText';
import { FontAwesome, Ionicons } from '@expo/vector-icons';
import CheckBox from '@/components/ui/checkbox/CheckBox';
import CheckboxWithTitle from '@/components/ui/checkbox/CheckboxWithTitle';
import { Link } from 'expo-router';
import { IExploreItem } from '@/types';
import useExploreStore from '@/stores/useExploreStore';
import CustomButton from '@/components/ui/buttons/CustomButton';

// Cities data for filtering
const CITIES_DATA = [
  { title: 'Riyadh', _id: 'riyadh' },
  { title: 'Jeddah', _id: 'jeddah' },
  { title: 'Dammam', _id: 'dammam' },
  { title: 'Mecca', _id: 'mecca' },
  { title: 'Medina', _id: 'medina' },
  { title: 'Tabuk', _id: 'tabuk' },
];

// Rating options for filtering
const RATING_OPTIONS = ['4.5+', '4+', '3+', '2+'];

// Tags data for filtering
const TAGS_DATA = [
  'Historical',
  'UNESCO',
  'Nature',
  'Modern',
  'Adventure',
  'Family',
  'Religious',
];

const RatingCheckbox = memo(
  ({
    title,
    onPress,
    checked,
  }: {
    title: string;
    onPress: () => void;
    checked: boolean;
  }) => (
    <Pressable className='flex-row items-center gap-3' onPress={onPress}>
      <CheckBox checked={checked} onPress={onPress} />
      <View className='flex-row items-center'>
        <FontAwesome
          name='star'
          color={COLORS.starColor}
          size={normalized(14)}
        />
        <CustomText className='text-h2 text-neutral-800'>{title}</CustomText>
      </View>
    </Pressable>
  ),
);

const Tag = memo(
  ({
    title,
    onPress,
    isActive,
  }: {
    title: string;
    onPress: () => void;
    isActive: boolean;
  }) => (
    <Pressable
      onPress={onPress}
      style={[styles.tag, isActive ? styles.activeTag : null]}
    >
      <CustomText
        style={{ color: isActive ? COLORS.white[50] : COLORS.secondary[300] }}
      >
        #{title}
      </CustomText>
    </Pressable>
  ),
);

const exploreAttractions = () => {
  const insets = useSafeAreaInsets();
  const [showFilter, setShowFilter] = useState(false);
  const { setItem } = useExploreStore();
  const [searchQuery, setSearchQuery] = useState('');
  const [isSearching, setIsSearching] = useState(false);
  const [seeMore, setSeeMore] = useState(false);
  const [isFiltersApplied, setIsFiltersApplied] = useState(false);

  // Filter states
  const [selectedCities, setSelectedCities] = useState<string[]>([]);
  const [selectedRatings, setSelectedRatings] = useState<string[]>([]);
  const [selectedTags, setSelectedTags] = useState<string[]>([]);

  // Get all explore attractions
  const { data, isLoading, isFetching, refetch } = useGetExploreQuery({
    page: 1,
    limit: 100,
  });

  // Store the original data
  const [explores, setExplores] = useState<IExploreItem[] | undefined>(
    data?.data as IExploreItem[],
  );

  // Handle dispatching explore item to store
  const handleDispatchExploreItem = useCallback((item: IExploreItem) => {
    setItem(item);
  }, []);

  // Handle search input
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
    setIsSearching(!!text);
  }, []);

  // Handle city selection
  const handleCitySelect = useCallback((cityId: string) => {
    setSelectedCities((prev) => {
      if (prev.includes(cityId)) {
        return prev.filter((id) => id !== cityId);
      } else {
        return [...prev, cityId];
      }
    });
  }, []);

  // Handle rating selection
  const handleRatingSelect = useCallback((rating: string) => {
    setSelectedRatings((prev) => {
      if (prev.includes(rating)) {
        return prev.filter((r) => r !== rating);
      } else {
        return [...prev, rating];
      }
    });
  }, []);

  // Handle tag selection
  const handleTagSelect = useCallback((tag: string) => {
    setSelectedTags((prev) => {
      if (prev.includes(tag)) {
        return prev.filter((t) => t !== tag);
      } else {
        return [...prev, tag];
      }
    });
  }, []);

  // Apply filters
  const handleApplyFilters = useCallback(() => {
    setIsFiltersApplied(true);
    setShowFilter(false);
  }, []);

  // Reset filters
  const handleResetFilters = useCallback(() => {
    setSelectedCities([]);
    setSelectedRatings([]);
    setSelectedTags([]);
    setIsFiltersApplied(false);
    setShowFilter(false);
  }, []);

  // Filter and search the data
  const filteredExplores = useMemo(() => {
    if (!data?.data) return [];

    let filtered = [...data.data];

    // Apply search filter
    if (searchQuery) {
      filtered = filtered.filter(
        (item) =>
          item.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
          item.tags.some((tag) =>
            tag.toLowerCase().includes(searchQuery.toLowerCase()),
          ),
      );
    }

    // Apply city filters
    if (selectedCities.length > 0) {
      // This is a mock filter since we don't have city data in the IExploreItem
      // In a real app, you would filter by city property
      filtered = filtered.filter((item) => {
        // Mock implementation - in real app, check item.city
        const itemCity = item._id.substring(0, 3).toLowerCase();
        return selectedCities.some((city) =>
          itemCity.includes(city.substring(0, 3)),
        );
      });
    }

    // Apply rating filters
    if (selectedRatings.length > 0) {
      filtered = filtered.filter((item) => {
        const rating = item.rating.average;
        return selectedRatings.some((r) => {
          const minRating = parseFloat(r.replace('+', ''));
          return rating >= minRating;
        });
      });
    }

    // Apply tag filters
    if (selectedTags.length > 0) {
      filtered = filtered.filter((item) =>
        item.tags.some((tag) => selectedTags.includes(tag)),
      );
    }

    return filtered;
  }, [data?.data, searchQuery, selectedCities, selectedRatings, selectedTags]);

  // Update explores when data changes
  useEffect(() => {
    if (data?.data) {
      setExplores(filteredExplores);
    }
  }, [data?.data, filteredExplores]);

  // Determine if we're in a loading state
  const isLoadingData = isLoading || isFetching;

  // Show loading screen for initial load
  if (isLoading && !explores?.length) {
    return (
      <ScreenTemplate>
        <View
          style={{ flex: 1, justifyContent: 'center', alignItems: 'center' }}
        >
          <Loading isLoading />
        </View>
      </ScreenTemplate>
    );
  }

  return (
    <>
      <ScreenTemplate withoutSafeAria style={{ paddingTop: insets.top }}>
        <CustomHeader title='Explore Attractions' />
        <View className='flex-row items-center gap-x-3 justify-between my-3'>
          <SearchBar
            placeholder='Search attractions...'
            containerStyles='flex-1 border border-white-50/10 rounded-[2rem]'
            onSearch={handleSearch}
            value={searchQuery}
            debounceTime={500}
          />
          <TouchableOpacity
            onPress={() => setShowFilter((prev) => !prev)}
            className='bg-white-50/10 h-[44px] w-[63px] justify-center items-center rounded-full border border-white-50/10'
          >
            <APP_Icons.FilterAltIcon
              width={normalized(24)}
              height={normalized(24)}
              color={
                isFiltersApplied ? COLORS.secondary[300] : COLORS.white[50]
              }
            />
          </TouchableOpacity>
        </View>

        {/* Search status indicator */}
        {(searchQuery || isFiltersApplied) && (
          <View className='flex-row justify-between items-center px-2 mb-2'>
            <CustomText style={{ color: COLORS.neutral[400] }}>
              {isLoadingData
                ? 'Searching...'
                : `Found ${explores?.length || 0} attractions`}
            </CustomText>
            {searchQuery && (
              <TouchableOpacity onPress={() => handleSearch('')}>
                <CustomText style={{ color: COLORS.secondary[300] }}>
                  Clear search
                </CustomText>
              </TouchableOpacity>
            )}
            {isFiltersApplied && !searchQuery && (
              <TouchableOpacity onPress={handleResetFilters}>
                <CustomText style={{ color: COLORS.secondary[300] }}>
                  Clear filters
                </CustomText>
              </TouchableOpacity>
            )}
          </View>
        )}

        {/* Initial loading state */}
        {isLoadingData && (!explores || explores.length === 0) && (
          <View className='flex-1 justify-center items-center'>
            <ActivityIndicator size='large' color={COLORS.secondary[300]} />
            <CustomText style={{ marginTop: 16, color: COLORS.neutral[400] }}>
              Loading attractions...
            </CustomText>
          </View>
        )}

        {/* Attractions grid */}
        {(!isLoadingData || (explores && explores.length > 0)) && (
          <FlatList
            numColumns={2}
            data={explores}
            keyExtractor={(item) => item._id}
            ListEmptyComponent={
              <EmptyListComponent
                title={
                  searchQuery || isFiltersApplied
                    ? 'No matching attractions found'
                    : 'No Attractions Found!'
                }
                description={
                  searchQuery || isFiltersApplied
                    ? 'Try adjusting your search or filters'
                    : "We couldn't find any attractions near you."
                }
              />
            }
            ListHeaderComponent={
              isLoadingData && explores && explores.length > 0 ? (
                <View className='py-4 items-center'>
                  <ActivityIndicator color={COLORS.secondary[300]} />
                </View>
              ) : null
            }
            // Performance optimizations
            removeClippedSubviews={true}
            maxToRenderPerBatch={8}
            windowSize={10}
            initialNumToRender={6}
            updateCellsBatchingPeriod={50}
            renderItem={({ item }) => (
              <View className='mx-3 my-3 flex-1'>
                <Link href={`/(protected)/explore/${item._id}`} asChild>
                  <Pressable onPress={() => handleDispatchExploreItem(item)}>
                    <ExploreAttractionCard
                      title={item.name}
                      image={item.coverImage}
                    />
                  </Pressable>
                </Link>
              </View>
            )}
          />
        )}

        <CustomBottomSheet
          title='Filter'
          onClose={() => setShowFilter(false)}
          isActive={showFilter}
        >
          <FlatList
            data={['']}
            style={{ flexGrow: 0, maxHeight: MAX_HEIGHT - 100 }} // Ensure fixed height
            renderItem={() => (
              <View className='gap-3 py-4'>
                {/* Cities */}
                <View>
                  <CustomText style={styles.bottomSheetSectionTitle}>
                    City or Region
                  </CustomText>
                  <FlatList
                    data={seeMore ? CITIES_DATA : CITIES_DATA.slice(0, 4)}
                    renderItem={({ item }) => (
                      <CheckboxWithTitle
                        checked={selectedCities.includes(item._id)}
                        title={item.title}
                        onPress={() => handleCitySelect(item._id)}
                      />
                    )}
                    keyExtractor={(item) => item._id}
                  />
                  {CITIES_DATA.length > 4 && (
                    <TouchableOpacity
                      className='flex-row items-center gap-3'
                      onPress={() => setSeeMore((prev) => !prev)}
                    >
                      <CustomText className='text-secondary-300 font-medium text-h3 my-3'>
                        {seeMore ? 'See Less' : 'See More'}
                      </CustomText>
                      <Ionicons
                        name={seeMore ? 'chevron-up' : 'chevron-down'}
                        size={normalized(24)}
                        color={COLORS.secondary[300]}
                      />
                    </TouchableOpacity>
                  )}
                </View>

                {/* Rating */}
                <View>
                  <CustomText style={styles.bottomSheetSectionTitle}>
                    Rating
                  </CustomText>
                  <View className='gap-2'>
                    {RATING_OPTIONS.map((rating) => (
                      <RatingCheckbox
                        key={rating}
                        checked={selectedRatings.includes(rating)}
                        onPress={() => handleRatingSelect(rating)}
                        title={rating}
                      />
                    ))}
                  </View>
                </View>

                {/* Tags */}
                <View>
                  <CustomText style={styles.bottomSheetSectionTitle}>
                    Tags
                  </CustomText>
                  <View className='flex-row items-center flex-wrap gap-3'>
                    {TAGS_DATA.map((tag) => (
                      <Tag
                        key={tag}
                        isActive={selectedTags.includes(tag)}
                        onPress={() => handleTagSelect(tag)}
                        title={tag}
                      />
                    ))}
                  </View>
                </View>

                {/* Action buttons */}
                <View
                  className='flex-row items-center mt-4 pb-8'
                  style={{ gap: 6 }}
                >
                  <CustomButton
                    variant='grey'
                    className='flex-1'
                    onPress={handleResetFilters}
                  >
                    Reset
                  </CustomButton>
                  <CustomButton className='flex-1' onPress={handleApplyFilters}>
                    Apply
                  </CustomButton>
                </View>
              </View>
            )}
          />
        </CustomBottomSheet>
      </ScreenTemplate>
    </>
  );
};
const styles = StyleSheet.create({
  bottomSheetSectionTitle: {
    color: COLORS.neutral[1000],
    fontSize: normalized(16),
    fontWeight: '500',
    marginBottom: normalized(7),
  },
  tag: {
    borderWidth: 1,
    borderColor: COLORS.secondary[300],
    paddingHorizontal: normalized(10),
    paddingVertical: normalized(5),
    borderRadius: normalized(10),
  },
  activeTag: {
    backgroundColor: COLORS.secondary[300],
  },
});
export default exploreAttractions;
