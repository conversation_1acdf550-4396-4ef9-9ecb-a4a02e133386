import {
  useSharedValue,
  withTiming,
  Easing,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { useState, useEffect, useCallback, useRef } from 'react';
import { StyleSheet, TouchableOpacity, View, StatusBar } from 'react-native';
import RequestCameraPermission from '@/components/RequestCameraPermission';
import useImagePicker from '@/hooks/useImagePicker';
import useCamera from '@/hooks/useCamera';

import CustomText from '@/components/ui/CustomText';
import GoBack from '@/layouts/GoBack';

import { APP_Icons } from '@/constants/Images';
import { COLORS, normalized } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';

import { Camera } from 'react-native-vision-camera';
import useUploadShort from '@/hooks/shortsHooks/useUploadShort';
import {
  SafeAreaView,
  useSafeAreaInsets,
} from 'react-native-safe-area-context';
import VideoViewComponent from '@/components/shortsComponents/VideoView';
import ImageView from '@/components/shortsComponents/ImageView';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { router } from 'expo-router';
import Animated from 'react-native-reanimated';

// Constants
const MAX_DURATION = 30;

const formatTime = (seconds: number) => {
  const mins = Math.floor(seconds / 60);
  const secs = seconds % 60;
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
};

export default function ShortsCameraScreen() {
  const inset = useSafeAreaInsets();

  // Hook to upload from Library
  const { pickImage, result, setResult } = useImagePicker({
    imagePickOptions: {
      mediaTypes: ['images', 'videos'],
      allowsEditing: true,
      selectionLimit: 1,
      videoMaxDuration: 30,
    },
  });

  // Camera Hook
  const {
    cameraRef,
    device,
    toggleCameraFacing,
    hasPermission,
    startRecording,
    stopRecording,
    takePhoto,
  } = useCamera();

  // State
  const [isRecording, setIsRecording] = useState(false);
  const [secondsElapsed, setSecondsElapsed] = useState(0);
  const [capturedMedia, setCapturedMedia] = useState<string | undefined>(
    undefined,
  );
  const [cameraMode, setCameraMode] = useState<'photo' | 'video'>('video');

  const isStartingRecording = useRef(false);

  // Refs
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  // Upload hook
  const { isSuccess, data } = useUploadShort();

  // Animated values
  const progress = useSharedValue(0);
  const recordButtonScale = useSharedValue(1);
  const recordRingScale = useSharedValue(0);
  const recordRingOpacity = useSharedValue(1);

  // To clear the selected or captured media
  const clearMedia = useCallback(() => {
    setCapturedMedia(undefined);
    setResult([]);
    setSecondsElapsed(0);
  }, [setResult]);

  const handleStartRecording = async () => {
    if (isStartingRecording.current) return;
    isStartingRecording.current = true;
    try {
      setIsRecording(true);
      const videoUri = await startRecording();
      if (videoUri) setCapturedMedia(videoUri);
    } finally {
      isStartingRecording.current = false;
    }
  };

  // Handle taking a photo
  const handleTakePhoto = useCallback(async () => {
    const photo = await takePhoto();
    console.log(photo);
    if (photo) {
      setCapturedMedia(photo);
    }
  }, [takePhoto]);

  // Start recording animation and timer
  useEffect(() => {
    if (isRecording) {
      // Animate the record button
      recordButtonScale.value = withTiming(0.8, { duration: 300 });
      recordRingScale.value = withTiming(1.2, { duration: 300 });

      // Start the timer
      timerRef.current = setInterval(() => {
        setSecondsElapsed((sec) => {
          const newSec = sec + 1;
          progress.value = withTiming(newSec / MAX_DURATION, {
            duration: 300,
            easing: Easing.linear,
          });

          if (newSec >= MAX_DURATION) {
            stopRecording();
            setIsRecording(false);
          }
          return newSec;
        });
      }, 1000);
    } else {
      // Reset animations when not recording
      recordButtonScale.value = withTiming(1, { duration: 300 });
      recordRingScale.value = withTiming(0, { duration: 300 });

      // Clear the timer
      if (timerRef.current) {
        clearInterval(timerRef.current);
        timerRef.current = null;
      }
    }

    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current);
      }
    };
  }, [
    isRecording,
    progress,
    recordButtonScale,
    recordRingScale,
    stopRecording,
  ]);

  // When upload success
  useEffect(() => {
    if (isSuccess && data) {
      setCapturedMedia('');
      router.replace('/(protected)/(tabs)');
    }
  }, [isSuccess, data]);

  // Animated styles for record button
  const recordButtonStyle = useAnimatedStyle(() => ({
    transform: [{ scale: recordButtonScale.value }],
  }));

  // Animated styles for record ring
  const recordRingStyle = useAnimatedStyle(() => ({
    transform: [{ scale: recordRingScale.value }],
    opacity: recordRingOpacity.value,
  }));

  // Check if media is a video
  const isVideo = (uri: string) => {
    return uri?.match(/\.(mp4|mov|avi|mkv)$/i);
  };

  // Return view to request camera permission
  if (!hasPermission) {
    return (
      <RequestCameraPermission
        buttonTitle='Grant Permission'
        title='You need to grant Camera and Microphone permissions'
      />
    );
  }

  // Show media preview if captured or selected
  if (capturedMedia || result?.[0]?.uri)
    return (
      <SafeAreaView className='flex-1 bg-black'>
        {isVideo(capturedMedia || result?.[0]?.uri) ? (
          <VideoViewComponent
            mediaUri={capturedMedia || result?.[0]?.uri || ''}
            onClear={clearMedia}
          />
        ) : (
          <ImageView
            uploadedImage={result?.[0]}
            onClear={clearMedia}
            mediaUri={capturedMedia}
          />
        )}
      </SafeAreaView>
    );

  // Show message if no camera device is available
  if (!device) {
    return (
      <ScreenTemplate>
        <GoBack />
        <View className='flex-1 justify-center items-center'>
          <CustomText className='font-bold text-white-50 text-h1'>
            Real device is Required
          </CustomText>
        </View>
      </ScreenTemplate>
    );
  }

  // Update the gallery button handler
  const handleGalleryPick = async () => {
    const assets = await pickImage();
    if (assets && assets.length > 0) {
      // Force update the UI with the selected image
      setResult(assets);
    }
  };

  return (
    <View style={styles.container}>
      {/* Status bar with dark content for better visibility */}
      <StatusBar barStyle='light-content' />

      {/* Camera Component */}
      <Camera
        isActive={true}
        style={StyleSheet.absoluteFill}
        device={device}
        ref={cameraRef}
        photo
        video
        audio
        enableZoomGesture
      />

      {/* Top Controls */}
      <SafeAreaView style={[styles.topControls, { top: inset.top }]}>
        <View style={styles.topBar}>
          <TouchableOpacity onPress={() => router.back()}>
            <Ionicons
              name='close'
              size={normalized(28)}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>

          <View style={styles.topBarCenter}>
            {isRecording && (
              <View style={styles.recordingIndicator}>
                <View style={styles.recordingDot} />
                <CustomText style={styles.recordingTimer}>
                  {formatTime(secondsElapsed)}
                </CustomText>
              </View>
            )}
          </View>
        </View>
      </SafeAreaView>

      {/* Camera Mode Selector */}
      <View style={styles.modeSelectorContainer}>
        <TouchableOpacity
          style={[
            styles.modeButton,
            cameraMode === 'photo' && styles.activeModeButton,
          ]}
          onPress={() => setCameraMode('photo')}
        >
          <CustomText
            style={[
              styles.modeButtonText,
              cameraMode === 'photo' && styles.activeModeText,
            ]}
          >
            PHOTO
          </CustomText>
        </TouchableOpacity>

        <TouchableOpacity
          style={[
            styles.modeButton,
            cameraMode === 'video' && styles.activeModeButton,
          ]}
          onPress={() => setCameraMode('video')}
        >
          <CustomText
            style={[
              styles.modeButtonText,
              cameraMode === 'video' && styles.activeModeText,
            ]}
          >
            VIDEO
          </CustomText>
        </TouchableOpacity>
      </View>

      {/* Bottom Controls */}
      <View style={styles.bottomControls}>
        {/* Gallery Button */}
        <TouchableOpacity
          style={styles.galleryButton}
          onPress={handleGalleryPick}
        >
          <APP_Icons.GalleryIcon
            width={normalized(30)}
            height={normalized(30)}
          />
        </TouchableOpacity>

        {/* Record/Capture Button */}
        <View style={styles.captureButtonContainer}>
          <Animated.View style={[styles.recordRing, recordRingStyle]} />

          <TouchableOpacity
            style={styles.captureButton}
            activeOpacity={0.8}
            onPress={cameraMode === 'photo' ? handleTakePhoto : undefined}
            onLongPress={handleStartRecording}
            onPressOut={() => {
              if (isRecording) {
                stopRecording();
                setIsRecording(false);
              }
            }}
          >
            <Animated.View
              style={[styles.captureButtonInner, recordButtonStyle]}
            >
              {isRecording && <View style={styles.recordingSquare} />}
            </Animated.View>
          </TouchableOpacity>
        </View>

        {/* Camera Flip Button */}
        <TouchableOpacity
          style={styles.flipButton}
          onPress={toggleCameraFacing}
        >
          <Ionicons
            name='camera-reverse'
            size={normalized(30)}
            color={COLORS.white[50]}
          />
        </TouchableOpacity>
      </View>

      {/* Progress Bar */}
      {isRecording && (
        <View style={styles.progressBarContainer}>
          <Animated.View
            style={[
              styles.progressBar,
              { width: `${(secondsElapsed / MAX_DURATION) * 100}%` },
            ]}
          />
        </View>
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: 'black',
  },
  camera: {
    flex: 1,
  },
  topControls: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    zIndex: 10,
  },
  topBar: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 10,
  },
  topBarCenter: {
    flex: 1,
    alignItems: 'center',
  },
  topBarRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.3)',
  },
  recordingIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.danger,
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 20,
  },
  recordingDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
    marginRight: 6,
  },
  recordingTimer: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 14,
  },
  modeSelectorContainer: {
    position: 'absolute',
    top: 100,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 10,
  },
  modeButton: {
    paddingHorizontal: 20,
    paddingVertical: 8,
    marginHorizontal: 5,
  },
  activeModeButton: {
    borderBottomWidth: 2,
    borderBottomColor: COLORS.white[50],
  },
  modeButtonText: {
    color: 'rgba(255,255,255,0.7)',
    fontWeight: '600',
    fontSize: 14,
  },
  activeModeText: {
    color: COLORS.white[50],
    fontWeight: 'bold',
  },
  bottomControls: {
    position: 'absolute',
    bottom: 30,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 30,
  },
  galleryButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonContainer: {
    position: 'relative',
    width: 80,
    height: 80,
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordRing: {
    position: 'absolute',
    width: 80,
    height: 80,
    borderRadius: 40,
    borderWidth: 2,
    borderColor: COLORS.danger,
  },
  captureButton: {
    width: 70,
    height: 70,
    borderRadius: 35,
    backgroundColor: 'transparent',
    borderWidth: 4,
    borderColor: COLORS.white[50],
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonInner: {
    width: 54,
    height: 54,
    borderRadius: 27,
    backgroundColor: COLORS.white[50],
    justifyContent: 'center',
    alignItems: 'center',
  },
  recordingSquare: {
    width: 20,
    height: 20,
    borderRadius: 3,
    backgroundColor: COLORS.danger,
  },
  flipButton: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  progressBarContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    height: 4,
    backgroundColor: 'rgba(255,255,255,0.3)',
  },
  progressBar: {
    height: '100%',
    backgroundColor: COLORS.danger,
  },
});
