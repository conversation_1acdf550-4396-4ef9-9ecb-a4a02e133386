import {
  StyleSheet,
  View,
  TouchableOpacity,
  FlatList,
  Pressable,
} from 'react-native';
import { useVideoPlayer, VideoView } from 'expo-video';
import { useCallback, useEffect, useRef, useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { IShortsItem, IUser } from '@/types';

import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  runOnJS,
  Easing,
} from 'react-native-reanimated';
import { TapGestureHandler, TextInput } from 'react-native-gesture-handler';
import { useEvent, useEventListener } from 'expo';
import { APP_Icons } from '@/constants/Images';

import { SafeAreaView } from 'react-native-safe-area-context';
import useUpdateShorts from '@/hooks/shortsHooks/useUpdateShorts';
import { LinearGradient } from 'expo-linear-gradient';
import Avatar from '@/components/ui/Avatar';
import CustomText from '@/components/ui/CustomText';
import BottomSheet, { BottomSheetRef } from '@/components/ui/BottomSheet';

const ICONS_SIZE = 24;

interface IVideoModal {
  onClose: () => void;
  item: IShortsItem;
  user?: IUser;
  onSwipeLeft: () => void;
  onSwipeRight: () => void;
}

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

const ShortsVideoScreen = (props: IVideoModal) => {
  const [videoEnded, setVideoEnded] = useState(false);
  const [isSheetVisible, setIsSheetVisible] = useState(false);
  const [message, setMessage] = useState('');

  const fadeAnim = useSharedValue(0);
  const animatedWidth = useSharedValue(0);
  const doubleTapRe = useRef();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const bottomSheetRef = useRef<BottomSheetRef>(null);

  const { mutate: handleLike, isPending: likePending } =
    useUpdateShorts('like');
  const { mutate: handleView, isPending: viewPending } =
    useUpdateShorts('view');
  const {
    mutate: handleComment,
    isPending: commentPending,
    isSuccess: commentSuccess,
  } = useUpdateShorts('comment');

  // Animated styles
  const AnimatedFadeStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: withTiming(`${animatedWidth.value}%`, { easing: Easing.linear }),
  }));

  const player = useVideoPlayer(props.item?.videoUrl, (player) => {
    player.loop = false;
    player.timeUpdateEventInterval = 0.25; // update data each 0.25 secs
    player.play(); // auto play once video is loaded
  });

  // watch the state of video if it's playing or not to handle the toggle state
  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player.playing,
  });

  // Toggle video play and pause
  const togglePlayer = useCallback(() => {
    if (videoEnded) {
      player.seekBy(0);
      setVideoEnded(false);
      return;
    }

    isPlaying ? player.pause() : player.play();
    showControls();
  }, [videoEnded, isPlaying]);

  // Show controls and overlay
  const showControls = useCallback(() => {
    fadeAnim.value = withTiming(1, { duration: 300 });

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      runOnJS(hideControls)();
    }, 2000);
  }, []);

  // Hide controls and overlay
  const hideControls = useCallback(() => {
    fadeAnim.value = withTiming(0, { duration: 500 });
  }, []);

  const handleMessageChange = useCallback((e: string) => {
    setMessage(e);
  }, []);

  const handleInputSubmit = useCallback(() => {
    if (!message.trim()) return;

    handleComment({
      shortId: props.item._id,
      comment: message,
    });
    if (commentSuccess) {
      setMessage('');
    }
  }, [message, commentSuccess]);

  // Track video progress
  useEventListener(player, 'timeUpdate', (payload) => {
    if (payload.currentTime !== 0) {
      animatedWidth.value = Math.floor(
        (payload.currentTime / player.duration) * 100,
      );
    }
  });

  // Detect video end
  useEventListener(player, 'playToEnd', () => {
    setVideoEnded(true);
    showControls();
    props.onClose();
  });

  // Eye icon click → Toggle Bottom Sheet
  const toggleBottomSheet = useCallback(() => {
    if (isSheetVisible) {
      bottomSheetRef.current?.close();
    } else {
      bottomSheetRef.current?.open();
    }
    setIsSheetVisible(!isSheetVisible);
  }, [bottomSheetRef]);

  useEffect(() => {
    handleView({ shortId: props.item._id });
  }, []);
  return (
    <SafeAreaView style={styles.container}>
      {/* Tap Gesture to Show Controls  & Like the video*/}
      <TapGestureHandler
        numberOfTaps={1}
        waitFor={doubleTapRe}
        onActivated={showControls}
      >
        <TapGestureHandler
          maxDelayMs={250}
          ref={doubleTapRe}
          numberOfTaps={2}
          onActivated={() => handleLike({ shortId: props.item._id })}
        >
          {/* To Full the screen tap area and do actions over all screen */}
          <Pressable style={styles.fullscreenTapArea}>
            <AnimatedLinearGradient
              colors={
                !isPlaying
                  ? ['black', 'transparent', 'black']
                  : ['transparent', 'transparent']
              }
              style={[styles.overlay]}
            >
              {/* Video Player */}
              <VideoView
                style={styles.video}
                player={player}
                allowsFullscreen={false}
                allowsVideoFrameAnalysis={false}
                nativeControls={false}
                contentFit='contain'
              />
              {/* Top Controls */}
              <Animated.View style={[styles.topContainer, AnimatedFadeStyle]}>
                <View className='w-full h-2 rounded-3xl bg-white-50/20 mb-4 '>
                  <Animated.View
                    className='absolute bg-white-50 h-full rounded-3xl'
                    style={progressStyle}
                  />
                </View>

                <View className='flex-row items-center justify-between'>
                  <TouchableOpacity className='flex-row items-center gap-2'>
                    <Avatar
                      source={{ uri: props.user?.image }}
                      text={props.user?.displayName}
                      size={normalized(38)}
                    />
                    <CustomText
                      className='text-white-50 text-h2 font-semibold'
                      numberOfLines={1}
                    >
                      {props.user?.displayName}
                    </CustomText>
                  </TouchableOpacity>

                  {/* Close Button */}
                  <TouchableOpacity
                    onPress={props.onClose}
                    className='bg-white-50/20 w-10 h-10 items-center justify-center rounded-full'
                  >
                    <Ionicons
                      name='close'
                      size={normalized(24)}
                      color={COLORS.white[50]}
                    />
                  </TouchableOpacity>
                </View>
              </Animated.View>

              {/* Play/Pause Button */}
              <Animated.View
                style={[styles.overlayContainer, AnimatedFadeStyle]}
              >
                <TouchableOpacity onPress={togglePlayer}>
                  <View style={styles.playPauseButton}>
                    <Ionicons
                      name={isPlaying ? 'pause' : 'play'}
                      size={normalized(50)}
                      color={COLORS.white[50]}
                    />
                  </View>
                </TouchableOpacity>
              </Animated.View>

              {/* Eye Button */}
              <TouchableOpacity onPress={toggleBottomSheet}>
                <Animated.View style={[styles.eyeContainer, AnimatedFadeStyle]}>
                  <APP_Icons.EyeIcon
                    width={normalized(ICONS_SIZE)}
                    height={normalized(ICONS_SIZE)}
                  />
                  <CustomText className='text-white-50 text-h3 font-medium'>
                    {props.item.viewsCount}
                  </CustomText>
                </Animated.View>
              </TouchableOpacity>

              {/* Comment section  */}
              {!isPlaying && (
                <Animated.View className='bg-white-50 rounded-[2rem]  px-3.5 py-2 flex-row items-center gap-2 absolute bottom-10 w-[96%]'>
                  <TextInput
                    value={message}
                    onChangeText={handleMessageChange}
                    className='px-2 h-full flex-1 font-poppins text-neutral-700 font-semibold '
                    placeholder='Enter your message...'
                    placeholderClassName='font-poppins text-neutral-800'
                  />
                  <TouchableOpacity
                    disabled={commentPending}
                    onPress={handleInputSubmit}
                    className='bg-secondary-300 w-12 h-12 rounded-full justify-center items-center'
                  >
                    <APP_Icons.MessageSend
                      width={ICONS_SIZE}
                      height={ICONS_SIZE}
                    />
                  </TouchableOpacity>
                </Animated.View>
              )}
            </AnimatedLinearGradient>
            {/* Bottom Sheet */}
            <BottomSheet ref={bottomSheetRef}>
              <View className='bg-white-50 rounded-3xl'>
                <View className='border-b border-b-neutral-500 px-3 flex-row items-center justify-between pb-3'>
                  <CustomText className='text-h3 font-semibold text-black'>
                    Viewers
                  </CustomText>
                  <View className='flex-row items-center'>
                    <APP_Icons.EyeIcon
                      width={normalized(ICONS_SIZE)}
                      height={normalized(ICONS_SIZE)}
                      fill={COLORS.neutral[800]}
                    />
                    <CustomText className='text-neutral-800 text-h3'>
                      {props.item.viewsCount}
                    </CustomText>
                  </View>

                  <TouchableOpacity>
                    <APP_Icons.TrashIcon
                      width={normalized(ICONS_SIZE)}
                      height={normalized(ICONS_SIZE)}
                    />
                  </TouchableOpacity>
                </View>
                <FlatList
                  data={Array.from({ length: 10 }).fill('')}
                  ItemSeparatorComponent={() => <View className='mb-3' />}
                  contentContainerClassName='px-3 py-4 pb-10'
                  initialNumToRender={5}
                  renderItem={() => {
                    return (
                      <View className='flex-row items-center justify-between'>
                        <View className='flex-row items-center gap-3'>
                          <Avatar text='A'
size={normalized(32)} />
                          <CustomText className='text-h4 font-semibold'>
                            Ahmed Medhat
                          </CustomText>
                        </View>
                        <APP_Icons.NavigationOutline
                          width={normalized(20)}
                          height={normalized(20)}
                        />
                      </View>
                    );
                  }}
                  keyExtractor={(_, index) => String(index)}
                />
              </View>
            </BottomSheet>
          </Pressable>
        </TapGestureHandler>
      </TapGestureHandler>
    </SafeAreaView>
  );
};

export default ShortsVideoScreen;

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: 'black' },
  video: { flex: 1, width: '100%', height: '100%' },
  fullscreenTapArea: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlayContainer: {
    position: 'absolute',
    top: SCREEN_HEIGHT / 2 - 50,
    left: '50%',
    marginLeft: -50,
    width: 100,
    height: 100,
    justifyContent: 'center',
    alignItems: 'center',
  },
  topContainer: {
    position: 'absolute',
    top: '10%',
    width: '100%',
    paddingHorizontal: 10,
  },
  playPauseButton: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  eyeContainer: {
    position: 'absolute',
    bottom: normalized(90),
    flex: 1,
    alignSelf: 'center',
    flexDirection: 'row',
    alignItems: 'center',
    gap: normalized(5),
  },
});
