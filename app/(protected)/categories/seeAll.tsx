import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import React from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import { t } from 'i18next';
import useCategoriesQuery from '@/hooks/categoryHooks/useGetCategories';
import Loading from '@/layouts/Loading';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import CustomText from '@/components/ui/CustomText';
import useLanguage from '@/hooks/useLanguage';
import { router } from 'expo-router';
import LineSeparator from '@/components/ui/LineSeparator';
import { Image } from 'expo-image';

const CategoriesSeeAll = () => {
  const { isArabic } = useLanguage();
  const { data, isLoading } = useCategoriesQuery();

  if (isLoading) {
    return (
      <ScreenTemplate>
        <View className='flex-1 justify-center items-center'>
          <Loading isLoading />
        </View>
      </ScreenTemplate>
    );
  }

  return (
    <ScreenTemplate>
      <View className='flex-1 px-3'>
        <CustomHeader title={t('categories')} />
        <FlatList
          className='flex-1'
          showsVerticalScrollIndicator={false}
          data={data?.categories || []}
          keyExtractor={(item) => item._id}
          ItemSeparatorComponent={() => <LineSeparator />}
          contentContainerClassName='gap-5 py-3'
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() =>
                router.push({
                  pathname: '/(protected)/categories/filtered/[id]',
                  params: {
                    id: item._id,
                    name: isArabic ? item.arabic : item.english,
                  },
                })
              }
            >
              <View className='flex-row items-center justify-between'>
                <View className='flex-row items-center gap-2'>
                  <Image source={{ uri: item.icon }} style={styles.icon} />
                  <CustomText className='text-h2 text-neutral-100 font-semibold'>
                    {isArabic ? item.arabic : item.english}
                  </CustomText>
                </View>

                <Ionicons
                  name='chevron-forward'
                  size={28}
                  color={COLORS.neutral[100]}
                />
              </View>
            </TouchableOpacity>
          )}
        />
      </View>
    </ScreenTemplate>
  );
};

export default CategoriesSeeAll;

const styles = StyleSheet.create({
  icon: {
    width: 20,
    height: 20,
    resizeMode: 'contain',
  },
});
