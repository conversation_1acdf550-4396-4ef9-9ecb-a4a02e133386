import { StyleSheet, View } from 'react-native';
import React, { useCallback, useState, useMemo } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import GoBack from '@/layouts/GoBack';
import { APP_Icons } from '@/constants/Images';
import { COLORS } from '@/constants/Theme';
import CustomText from '@/components/ui/CustomText';
import CustomButton from '@/components/ui/buttons/CustomButton';
import { useLocalSearchParams, useRouter } from 'expo-router';
import useJoinActivity from '@/hooks/activityHooks/useJoinActivity';
import { handleError } from '@/utils/errorHandler';
import { customToast } from '@/hooks/useCustomToast';
import { useQueryClient } from '@tanstack/react-query';

const _iconSize = 55;

/**
 * Screen for sending a join request to an activity
 * Handles both permission-required and direct join flows
 */
const JoinRequestScreen = () => {
  const { id, requirePermission } = useLocalSearchParams<{
    id: string;
    requirePermission: string;
  }>();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { mutate, isPending } = useJoinActivity();

  // Parse the requirePermission parameter safely
  // Convert string 'true'/'false' to boolean, defaulting to false if invalid
  const needsPermission = useMemo(() => {
    try {
      return (
        requirePermission === 'true' || JSON.parse(requirePermission || 'false')
      );
    } catch (error) {
      console.error('Error parsing requirePermission:', error);
      return false;
    }
  }, [requirePermission]);

  // Handle join request submission
  const handleSubmit = useCallback(() => {
    // Prevent multiple submissions
    if (isSubmitting) return;
    setIsSubmitting(true);

    // Optimistically update UI
    queryClient.setQueryData(['activity', id], (oldData: any) => {
      if (!oldData) return oldData;

      return {
        ...oldData,
        activities: {
          ...oldData.activities,
          pendingParticipants: [
            ...(oldData.activities.pendingParticipants || []),
            'optimistic-pending',
          ],
        },
      };
    });

    // Send the join request with the updated parameter structure
    mutate(
      {
        activityId: id,
        requirePermission: needsPermission,
      },
      {
        onSuccess: (data) => {
          console.log('Join request success:', data);

          // Show success message
          customToast(
            needsPermission
              ? 'Join request sent successfully'
              : 'You have joined the activity',
            'success',
          );

          // Navigate to success screen
          setTimeout(() => {
            router.replace({
              pathname: '/(protected)/activity/permissionSuccess/[id]',
              params: {
                id,
                requirePermission: String(needsPermission),
              },
            });
          }, 300);
        },
        onError: (err) => {
          console.error('Join request error:', err);

          // Revert optimistic update
          queryClient.invalidateQueries({ queryKey: ['activity', id] });

          // Show error message
          handleError(err);
          setIsSubmitting(false);
        },
      },
    );
  }, [
    id,
    requirePermission,
    router,
    mutate,
    queryClient,
    needsPermission,
    isSubmitting,
  ]);

  return (
    <ScreenTemplate>
      <View className='flex-1 px-2'>
        <GoBack />

        <View className='flex-1 justify-between px-2 pb-7 '>
          <View className='  mt-20 gap-9 flex-1'>
            <View
              className='bg-primary-50/20 justify-center items-center rounded-full'
              style={{ width: _iconSize * 1.5, height: _iconSize * 1.5 }}
            >
              <APP_Icons.requestIcon width={_iconSize} height={_iconSize} />
            </View>

            {needsPermission ? (
              <CustomText className='text-white-50 text-h1 font-regular'>
                This activity requires host {'\n'} approval. Do you want to send{' '}
                {'\n'} a join request?
              </CustomText>
            ) : (
              <CustomText className='text-white-50 text-h1 font-regular'>
                Are you sure you want to join {'\n'} this activity?
              </CustomText>
            )}
          </View>

          <View className='flex-row items-center gap-3    '>
            <CustomButton
              onPress={() => router.back()}
              variant='grey'
              className='flex-1'
            >
              Cancel
            </CustomButton>
            <CustomButton
              className='flex-1'
              loading={isPending || isSubmitting}
              onPress={handleSubmit}
            >
              {needsPermission ? 'Send Request' : 'Join Activity'}
            </CustomButton>
          </View>
        </View>
      </View>
    </ScreenTemplate>
  );
};

export default JoinRequestScreen;

const styles = StyleSheet.create({
  grayButton: {
    flex: 1,
    backgroundColor: '#ACACAC',
  },
  cancelText: {
    color: COLORS.neutral[100],
  },
});
