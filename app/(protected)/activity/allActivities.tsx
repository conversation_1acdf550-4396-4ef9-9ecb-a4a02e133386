import {
  Pressable,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { memo, useCallback, useMemo, useState } from 'react';
import { Link } from 'expo-router';
import dayjs from 'dayjs';
import { DateType } from 'react-native-ui-datepicker';

// utils
import { getDayFromDate, getMonthFromDate } from '@/utils/formatDates';
import { APP_Icons } from '@/constants/Images';
import { COLORS, normalized } from '@/constants/Theme';

// api
import useGetAllActivities from '@/hooks/activityHooks/useGetAllActivities';

// components
import ScreenTemplate from '@/layouts/ScreenTemplate';
import ActivityCard from '@/components/ActivityCard';
import CustomHeader from '@/components/ui/CustomHeader';
import CustomRangeDatePicker from '@/components/ui/CustomRangeDatePicker';
import CustomText from '@/components/ui/CustomText';
import { useSession } from '@/context/AuthContext';
import { IActivityItem } from '@/types';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import SearchBar from '@/layouts/SearchBar';
import CardSkelton from '@/components/Skilltons/CardSkelton';
import OptimizedFlatList from '@/components/ui/OptimizedFlatList';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

interface IDates {
  startDate: DateType;
  endDate: DateType;
}
const currentDate = new Date();

// Memoized Item Component
const RenderItem = memo(
  ({ item, userId }: { item: IActivityItem; userId: string }) => {
    const isJoined = useCallback(
      (activity: IActivityItem) => {
        return Array.isArray(activity.participant)
          ? activity.participant.includes(userId)
          : false;
      },
      [userId],
    );

    return (
      <Link href={`/(protected)/activity/${item._id}`} asChild>
        <Pressable>
          <ActivityCard
            cover={item.images?.[0] ?? ''}
            month={getMonthFromDate(item.startDate)}
            day={getDayFromDate(item.startDate)}
            location={item.location.address || ''}
            title={item.name}
            currency={item.currency}
            price={Number(item.price)}
            members={item.members}
          />
        </Pressable>
      </Link>
    );
  },
);

const AllActivities = () => {
  const { userId } = useSession();
  const [search, setSearch] = useState('');
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Performance monitoring
  const { measureSync, runAfterInteractions } = usePerformanceMonitor({
    componentName: 'AllActivities',
    logSlowRenders: true,
  });

  // Date state management
  const [dates, setDates] = useState<IDates>({
    startDate: currentDate,
    endDate: currentDate,
  });

  // Applied dates for API filtering
  const [appliedDates, setAppliedDates] = useState<IDates | null>(null);

  // Display text for selected dates
  const [shownDates, setShownDates] = useState({
    startDate: 'Select Date',
    endDate: '',
  });

  // Format date for API requests
  const formattedDate = useCallback(
    (date: DateType | null) =>
      date ? dayjs(date).format('YYYY-MM-DD') : undefined,
    [],
  );

  // Fetch activities data with filters
  const {
    data,
    isLoading: dataLoading,
    isPending,
    refetch,
  } = useGetAllActivities({
    startDate: appliedDates ? formattedDate(appliedDates.startDate) : undefined,
    endDate: appliedDates ? formattedDate(appliedDates.endDate) : undefined,
    name: search,
  });

  // Combined loading state
  const isLoading = dataLoading || isPending;
  const isFetching = isLoading || isPending;

  // Process and sort activities by newest first with performance monitoring
  const activities = useMemo(() => {
    return measureSync(() => {
      if (!data?.pages) return [];

      // Extract all activities from paginated data
      const allActivities = data.pages.flatMap((page) => page.data);

      // Sort by startDate (newest first)
      return [...allActivities].sort((a, b) => {
        const dateA = new Date(a.startDate).getTime();
        const dateB = new Date(b.startDate).getTime();
        return dateB - dateA; // Descending order (newest first)
      });
    }, 'processActivities').result;
  }, [data?.pages, measureSync]);

  // Handle date selection change
  const handleDateChange = useCallback((params: IDates) => {
    setDates(params);
  }, []);

  // Format date for display
  const formatDate = useCallback((date: DateType) => {
    return dayjs(date).format('D MMMM YYYY');
  }, []);

  // Toggle date picker visibility
  const toggleDatePicker = useCallback(() => {
    setShowDatePicker((prev) => !prev);
  }, []);

  // Close date picker
  const closeDatePicker = useCallback(() => {
    setShowDatePicker(false);
  }, []);

  // Apply selected date range
  const handleApplyDate = useCallback(() => {
    setAppliedDates(dates);
    setShownDates({
      startDate: formatDate(dates.startDate),
      endDate: formatDate(dates.endDate),
    });
    setShowDatePicker(false);
  }, [dates, formatDate]);

  // Clear all filters
  const clearFilters = useCallback(() => {
    setSearch('');
    setAppliedDates(null);
    setShownDates({
      startDate: 'Select Date',
      endDate: '',
    });
  }, []);

  // Handle pull-to-refresh
  const handleRefresh = useCallback(async () => {
    setIsRefreshing(true);
    await refetch();
    setIsRefreshing(false);
  }, [refetch]);

  // Check if filters are applied
  const hasFilters = useMemo(() => {
    return !!search || appliedDates !== null;
  }, [search, appliedDates]);

  return (
    <ScreenTemplate>
      <View style={styles.container}>
        <CustomHeader title='Activities' />

        {/* Search Bar */}
        <View style={styles.searchContainer}>
          <SearchBar
            placeholder='Search activities...'
            onChangeText={setSearch}
            value={search}
            debounceTime={300}
          />
          <TouchableOpacity
            onPress={toggleDatePicker}
            style={[
              styles.iconButton,
              appliedDates && { borderColor: COLORS.secondary[300] },
            ]}
          >
            <APP_Icons.CALENDER
              width={normalized(20)}
              height={normalized(20)}
              color={appliedDates ? COLORS.secondary[300] : undefined}
            />
          </TouchableOpacity>
        </View>

        {/* Date display */}
        <View style={styles.dateDisplayContainer}>
          <TouchableOpacity
            onPress={toggleDatePicker}
            style={styles.dateSelector}
          >
            <CustomText style={styles.dateText}>
              {shownDates.startDate}{' '}
              {shownDates.endDate ? `- ${shownDates.endDate}` : ''}
            </CustomText>
            <APP_Icons.ChevronDownIcon
              width={normalized(15)}
              height={normalized(15)}
            />
          </TouchableOpacity>

          {/* Filter status and clear button */}
          {hasFilters && (
            <TouchableOpacity onPress={clearFilters} style={styles.clearButton}>
              <CustomText style={styles.clearButtonText}>
                Clear filters
              </CustomText>
            </TouchableOpacity>
          )}
        </View>

        {/* Date picker modal */}
        {showDatePicker && (
          <CustomRangeDatePicker
            endDate={dates.endDate}
            startDate={dates.startDate}
            onApply={handleApplyDate}
            onCancel={closeDatePicker}
            onChange={handleDateChange}
          />
        )}

        {/* Activities List with optimized rendering */}
        <OptimizedFlatList
          contentContainerStyle={styles.listContent}
          style={styles.list}
          showsVerticalScrollIndicator={false}
          itemHeight={220} // Known height for better performance
          enableVirtualization={true}
          ListEmptyComponent={
            <EmptyListComponent
              title={
                hasFilters ? 'No Matching Activities' : 'No Activities Yet!'
              }
              description={
                hasFilters
                  ? 'Try changing your search or date filters'
                  : 'Activities you create or join will appear here'
              }
            />
          }
          data={isFetching ? Array(3).fill('') : activities}
          keyExtractor={(item, index) =>
            isFetching ? `skeleton-${index}` : item._id
          }
          onEndReachedThreshold={0.2}
          refreshing={isRefreshing}
          onRefresh={handleRefresh}
          renderItem={({ item, index }) =>
            isFetching ? (
              <CardSkelton key={`skeleton-${index}`} style='h-[220px]' />
            ) : (
              <RenderItem userId={userId || ''} item={item} />
            )
          }
        />
      </View>
    </ScreenTemplate>
  );
};

// Styles for the component
const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 8,
    gap: 8,
  },
  searchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    gap: 12,
    marginVertical: 12,
  },
  iconButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    height: 44,
    width: 44,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 22,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.1)',
  },
  dateDisplayContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  dateText: {
    color: COLORS.white[50],
    fontSize: 16,
  },
  clearButton: {
    paddingVertical: 4,
    paddingHorizontal: 8,
  },
  clearButtonText: {
    color: COLORS.secondary[300],
    fontSize: 14,
  },
  list: {
    flex: 1,
    marginTop: 8,
  },
  listContent: {
    gap: 12,
    paddingBottom: 40,
  },
});

export default AllActivities;
