import React, { useState } from 'react';
import { FlatList, Pressable, View } from 'react-native';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import SearchBar from '@/layouts/SearchBar';
import CustomText from '@/components/ui/CustomText';
import { useSearchUser } from '@/hooks/userHooks/useSearchUser';
import UserWithActionButton from '@/components/userWithActionButton';
import CustomButton from '@/components/ui/buttons/CustomButton';
import { COLORS } from '@/constants/Theme';

const categories = [
  { title: 'All', _id: 'all' },
  { title: 'Activities', _id: 'activities' },
  { title: 'People', _id: 'people' },
  { title: 'Events', _id: 'events' },
];

const SearchScreen = () => {
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [query, setQuery] = useState('');

  const handleCategorySelect = (id: string) => {
    setSelectedCategory(id);
  };

  const { data: usersData, isLoading } = useSearchUser({
    page: 1,
    limit: 10,
    query: selectedCategory === 'people' ? query : '',
  });

  return (
    <ScreenTemplate>
      <View className='flex-1 px-4 pt-2 gap-3'>
        <CustomHeader title='Search' />

        <SearchBar
          onChangeText={setQuery}
          value={query}
          placeholder='Search people, activities, events...'
        />

        <FlatList
          data={categories}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: 12, paddingVertical: 8, maxHeight: 55 }}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <Pressable
              onPress={() => handleCategorySelect(item._id)}
              className={`border border-neutral-100 px-4 py-2.5 rounded-xl ${
                selectedCategory === item._id
                  ? 'bg-primary-50'
                  : 'bg-primary-50/20'
              }`}
            >
              <CustomText className='text-neutral-200 text-h6'>
                {item.title}
              </CustomText>
            </Pressable>
          )}
        />

        {selectedCategory === 'people' && (
          <FlatList
            data={usersData?.data || []}
            keyExtractor={(item) => item.user_id}
            keyboardShouldPersistTaps='handled'
            contentContainerClassName='gap-5'
            ListEmptyComponent={() =>
              !isLoading && (
                <CustomText className='text-center text-neutral-300 mt-6'>
                  No users found.
                </CustomText>
              )
            }
            renderItem={({ item }) => (
              <UserWithActionButton
                name={item.display_name}
                avatar={item.profile_picture}
                size={32}
              >
                <CustomButton
                  variant='outline'
                  className={` !border-secondary-300 ${!item.is_following ? '!bg-secondary-300' : ''}`}
                  textStyle={{
                    color: !item.is_following
                      ? COLORS.neutral[100]
                      : COLORS.secondary[300],
                  }}
                >
                  {item.is_following ? 'following...' : 'Follow'}
                </CustomButton>
              </UserWithActionButton>
            )}
          />
        )}
      </View>
    </ScreenTemplate>
  );
};

export default SearchScreen;
