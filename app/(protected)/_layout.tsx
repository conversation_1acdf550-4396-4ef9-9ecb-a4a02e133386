import { useSession } from '@/context/AuthContext';
import { Redirect, Stack } from 'expo-router';
import SplashScreen from '../splash';
import useAppInitialization from '@/hooks/AppHooks/useAppInitialization';

import * as Notifications from 'expo-notifications';
import { AppStateProvider } from '@/context/AppStateContext';

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    // Shows notification even app is open
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: true,
  }),
});

/**
 * Protected layout that ensures users are authenticated
 * This layout wraps all protected routes
 */

export default function ProtectedLayout() {
  const { session, isPending, user } = useSession();
  const { isReady, isFirstLaunch, isLoading } = useAppInitialization();

  // Show splash screen while loading
  if (!isReady || isLoading || isPending) {
    return <SplashScreen />;
  }

  // Handle first launch - redirect to onboarding
  if (isFirstLaunch) {
    return <Redirect href='/onboarding' />;
  }

  // If no session or userId, redirect to auth
  if (!session && !isLoading) {
    return <Redirect href='/auth' />;
  }

  // User is authenticated, render the protected layout
  return (
    <AppStateProvider>
      <Stack screenOptions={{ headerShown: false }} />
    </AppStateProvider>
  );
}
