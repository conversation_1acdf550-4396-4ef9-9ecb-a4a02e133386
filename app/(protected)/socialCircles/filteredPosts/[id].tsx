import React, { useCallback, useState } from 'react';
import { View } from 'react-native';

import { useLocalSearchParams, useRouter } from 'expo-router';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
// Components
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomActionModal from '@/layouts/CustomActionModal';
import SocialCircleAdminHeader from '@/components/SocialCircleAdminHeader';

import CustomText from '@/components/ui/CustomText';
import Loading from '@/layouts/Loading';

// Hooks
import { useGetPostsBySocialCircleId } from '@/hooks/circlesHooks/useGetPostsBySocialCircleId';

import useDeleteSocialCircle from '@/hooks/circlesHooks/useDeleteSocialCircle';
import CirclesPostsList from '@/components/SocialCirclesComponents/CirclesPostsList';

const dropDownActionsList = [
  { key: 'edit-social-circle', title: 'Edit social circle' },
  { key: 'delete-social-circle', title: 'Delete social circle' },
];

const SocialCircleFilteredPostsScreen = () => {
  const inset = useSafeAreaInsets();
  const router = useRouter();
  const [deleteModal, setDeleteModal] = useState(false);

  const { id, title, image, members } = useLocalSearchParams<{
    id: string;
    title: string;
    image: string;
    members: string;
  }>();

  const { data, isLoading } = useGetPostsBySocialCircleId(id);

  const { mutate, isPending: deletePending } = useDeleteSocialCircle();

  const handleNavigateDropDown = useCallback((key: string) => {
    if (key === 'edit-social-circle') {
      router.push(`/(protected)/socialCircles/updateSocialCircle/${id}`);
      return;
    } else if (key === 'delete-social-circle') {
      setDeleteModal(true);
    }
  }, []);

  return (
    <ScreenTemplate withoutSafeAria>
      {deletePending && (
        <View className='flex-1 bg-black/20 inset-0 absolute   justify-center items-center z-10'>
          <Loading isLoading={deletePending} size={120} />
        </View>
      )}

      <CustomActionModal
        visible={deleteModal}
        onCancel={() => setDeleteModal(false)}
        onAction={() => mutate(id)}
        actionButtonTitle='Delete'
        actionButtonStyle={{ flex: 1 }}
        cancelButtonStyle={{ flex: 1 }}
        actionButtonLoading={deletePending}
        actionButtonDisabled={deletePending}
      >
        <CustomText className='text-black text-h2 text-center'>
          Are you sure you want to delete this social circle?
        </CustomText>
      </CustomActionModal>

      <View style={{ paddingTop: inset.top }}>
        <SocialCircleAdminHeader
          image={image}
          name={title}
          participantsNumber={Number(members || 0)}
          dropDownActionsList={dropDownActionsList}
          onDropDownSelect={(e) => handleNavigateDropDown(e)}
        />
      </View>
      <View className='flex-1 px-2'>
        <CirclesPostsList data={data?.data || []} isLoading={isLoading} />
      </View>
    </ScreenTemplate>
  );
};

export default SocialCircleFilteredPostsScreen;
