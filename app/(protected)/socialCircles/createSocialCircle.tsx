import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo, useMemo, useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import { APP_Icons } from '@/constants/Images';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, SIZES } from '@/constants/Theme';
import CustomText from '@/components/ui/CustomText';
import CustomInput from '@/components/ui/CustomInput';
import SearchBar from '@/layouts/SearchBar';
import { Image } from 'expo-image';
import CustomButton from '@/components/ui/buttons/CustomButton';
import UserWithActionButton from '@/components/userWithActionButton';
import useGetFollowers from '@/hooks/userHooks/useGetFollowers';
import { useSession } from '@/context/AuthContext';
import EmptyListComponent from '@/components/ui/EmptyListComponent';

import useCreateSocialCircle from '@/hooks/circlesHooks/useCreateSocialCircle';

const _size = 42;

const AddButton = memo(
  ({ isAMember, onPress }: { isAMember: boolean; onPress: () => void }) => (
    <TouchableOpacity
      className={`flex-row items-center gap-1 justify-center border border-secondary-300 px-2 rounded-xl h-10 w-[90px] ${
        isAMember ? 'bg-transparent' : 'bg-secondary-300'
      }`}
      onPress={onPress}
    >
      <Ionicons
        name={isAMember ? 'checkmark' : 'add'}
        color={isAMember ? COLORS.secondary[300] : COLORS.white[50]}
        size={20}
      />
      <CustomText
        className={` text-body1 ${isAMember ? 'text-secondary-300' : 'text-white-50'}`}
      >
        {isAMember ? 'Added' : 'Add'}
      </CustomText>
    </TouchableOpacity>
  ),
);

const CreateSocialCircle = () => {
  const { user } = useSession();
  const [search, setSearch] = useState('');
  const { data } = useGetFollowers(user?._id || '');

  const filteredFollowers = useMemo(() => {
    if (!search) return data?.followers || [];
    return (data?.followers || []).filter((follower) =>
      follower.username?.toLowerCase().includes(search.toLowerCase()),
    );
  }, [search]);

  const {
    control,
    formState: { errors },
    result,
    handleImagePick,
    onSubmit,
    isPending,
    handleAddMembers,
    watch,
  } = useCreateSocialCircle();

  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <View className='mb-4'>
        <CustomHeader
          title='Create Social Circle'
          href={'/(protected)/(tabs)/socialCircles'}
        />
      </View>

      <View className='flex-row gap-2 items-center'>
        <TouchableOpacity
          onPress={handleImagePick}
          className='bg-[#BDBDBD] w-20 h-20 rounded-full justify-center items-center relative'
        >
          {!result?.[0]?.uri ? (
            <APP_Icons.GalleryIcon width={_size} height={_size} />
          ) : (
            <Image source={{ uri: result[0].uri }} style={styles.image} />
          )}
          <View className='bg-secondary-300 p-1 rounded-full absolute bottom-0 right-0'>
            <Ionicons name='add' size={_size / 2.5} color={COLORS.white[50]} />
          </View>
        </TouchableOpacity>

        <View className='flex-1 px-3 gap-1'>
          <CustomText className='text-white-50 text-h5 font-medium'>
            Social Circle Name
          </CustomText>
          <CustomInput
            control={control}
            name='name'
            error={errors.name?.message}
            placeholder='Circle name'
          />
        </View>
      </View>
      {errors?.image && (
        <CustomText className='my-1.5 text-danger'>
          {errors.image.message}
        </CustomText>
      )}

      <View className='mt-6'>
        <SearchBar
          placeholder='Search for people to add'
          value={search}
          onChangeText={setSearch}
        />
      </View>

      <FlatList
        showsVerticalScrollIndicator={false}
        className='mt-10'
        contentContainerClassName='gap-7 px-1'
        ListEmptyComponent={
          <EmptyListComponent
            title='No Followers'
            description='You have 0 followers to add'
          />
        }
        data={filteredFollowers}
        keyExtractor={(item) => item._id}
        renderItem={({ item }) => {
          const isAMember = watch('members').includes(item._id);

          return (
            <UserWithActionButton
              name={item.username}
              avatar={item.image}
              size={32}
            >
              <AddButton
                isAMember={isAMember}
                onPress={() => handleAddMembers(item._id)}
              />
            </UserWithActionButton>
          );
        }}
      />

      <View className='flex-row justify-between items-center'>
        <CustomText className='text-h2 text-white-50 px-2'>
          {watch('members')?.length || 0} available
        </CustomText>
        <CustomButton
          onPress={onSubmit}
          disabled={isPending}
          loading={isPending}
        >
          Create
        </CustomButton>
      </View>
    </ScreenTemplate>
  );
};

export default CreateSocialCircle;

const styles = StyleSheet.create({
  image: {
    width: '100%',
    height: '100%',
    objectFit: 'contain',
    borderRadius: 50,
  },
});
