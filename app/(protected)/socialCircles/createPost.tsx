import {
  <PERSON><PERSON><PERSON>,
  ScrollView,
  StyleSheet,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useCallback, useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import CustomHeader from '@/components/ui/CustomHeader';
import Avatar from '@/components/ui/Avatar';
import CustomText from '@/components/ui/CustomText';
import { useSession } from '@/context/AuthContext';
import { Ionicons } from '@expo/vector-icons';
import CustomTextArea from '@/components/ui/CustomTextArea';
import { APP_Icons } from '@/constants/Images';
import { Image } from 'expo-image';
import CustomButton from '@/components/ui/buttons/CustomButton';
import { ImagePickerAsset } from 'expo-image-picker';
import CustomBottomSheet from '@/components/ui/CustomBottomSheet';
import useGetSocialCircles from '@/hooks/circlesHooks/useGetSocialCircles';
import { ISocialCircle } from '@/types';
import useCreatePostForm from '@/hooks/circlesHooks/useCreatePostForm';
import { router } from 'expo-router';
const _iconSize = 24;

const CreatePost = () => {
  const { user } = useSession();
  const { data } = useGetSocialCircles(user?._id || '');

  const {
    onSubmit,
    formState: { errors },
    reset,
    control,
    watch,
    setValue,
    result,
    setResult,
    pickImage,
    isPending,
  } = useCreatePostForm();

  const [bottomSheet, setBottomSheet] = useState(false);
  const [socialBottomSheet, setSocialBottomSheet] = useState(false);

  const [postType, setPostType] = useState<'Public' | 'SocialCircles'>(
    'Public',
  );

  const toggleBottomSheet = useCallback(() => {
    setBottomSheet((prev) => !prev);
  }, []);

  const handleDeleteImage = (image: ImagePickerAsset) => {
    setResult((prev) => prev.filter((v) => v.uri !== image.uri));
  };

  const handleSelectSocialCircle = useCallback(
    (circle: ISocialCircle) => {
      setValue('socialCircle', circle);
      setSocialBottomSheet(false);
    },
    [setValue, setSocialBottomSheet],
  );

  const handleSelectPostType = useCallback(
    (type: 'Public' | 'SocialCircles') => {
      setPostType(type);
      setBottomSheet(false);
      if (type === 'SocialCircles') {
        setSocialBottomSheet(true);
      }
      if (type === 'Public') {
        setValue('socialCircle', null);
      }
    },
    [setPostType, setBottomSheet, setValue],
  );

  const handleDeleteSelectedCircle = () => {
    setValue('socialCircle', null);
  };

  return (
    <ScreenTemplate>
      <CustomHeader title='Add New Post' />

      <ScrollView
        contentContainerClassName='flex-1 justify-between px-2.5 '
        showsVerticalScrollIndicator={false}
        keyboardDismissMode='on-drag'
      >
        {/* User */}
        <View className='flex-row  gap-3 mt-3'>
          <Avatar
            text={user?.displayName}
            source={{ uri: user?.image }}
            size={normalized(55)}
          />
          <View className='gap-1.5'>
            <CustomText className='text-white-50 font-medium text-h5'>
              {user?.displayName}
            </CustomText>
            <TouchableOpacity
              onPress={toggleBottomSheet}
              className='bg-secondary-300  py-1.5 gap-1 rounded-3xl justify-center items-center flex-row'
            >
              <CustomText className='text-white-50 font-medium text-body1'>
                {postType}
              </CustomText>
              <Ionicons
                name={bottomSheet ? 'chevron-up' : 'chevron-down'}
                size={normalized(18)}
                color={COLORS.white[50]}
              />
            </TouchableOpacity>
          </View>
        </View>

        {/* Selected social circle  */}

        {watch('socialCircle') && (
          <View className='gap-2 relative w-20 '>
            <Avatar
              source={{ uri: watch('socialCircle')?.image }}
              text={watch('socialCircle')?.name}
            />
            <CustomText className='text-neutral-100 text-start text-h6'>
              {watch('socialCircle')?.name}
            </CustomText>
            <TouchableOpacity
              onPress={handleDeleteSelectedCircle}
              className='absolute -top-2 right-0 bg-white-50 rounded-full p-2'
            >
              <Ionicons name='close' color={COLORS.neutral[800]} size={12} />
            </TouchableOpacity>
          </View>
        )}

        {/* Form */}
        <View className='gap-10'>
          <CustomTextArea
            label='Share something with your circle...'
            labelStyles={styles.label}
            containerStyles={styles.descriptionContainer}
            inputProps={{ placeholder: 'Description' }}
            control={control}
            name='content'
            error={errors.content?.message}
          />

          <View>
            <CustomText style={styles.label}>Photo/Video Upload</CustomText>

            <TouchableOpacity
              onPress={pickImage}
              className='bg-white-50/10 h-24 rounded-xl justify-evenly items-center'
            >
              <APP_Icons.GalleryIcon width={_iconSize} height={_iconSize} />
            </TouchableOpacity>
            <FlatList
              data={result}
              horizontal
              showsHorizontalScrollIndicator={false}
              style={{ marginTop: 20 }}
              contentContainerStyle={{ gap: 10 }}
              keyExtractor={(item) => item.uri}
              renderItem={({ item }) => (
                <TouchableOpacity onPress={() => handleDeleteImage(item)}>
                  <Image source={{ uri: item.uri }} style={styles.image} />
                </TouchableOpacity>
              )}
            />
          </View>
        </View>

        {/* Buttons */}
        <View className='gap-3'>
          <CustomButton
            disabled={isPending}
            loading={isPending}
            onPress={onSubmit}
          >
            Post
          </CustomButton>
          <CustomButton
            disabled={isPending}
            variant={'outline'}
            style={{ backgroundColor: 'transparent' }}
            onPress={() => {
              reset();
              router.replace('/(protected)/(tabs)/socialCircles');
            }}
          >
            Cancel
          </CustomButton>
        </View>
      </ScrollView>

      {/* Bottom Sheet */}
      <CustomBottomSheet
        isActive={bottomSheet}
        onClose={toggleBottomSheet}
        maxHeight={SCREEN_HEIGHT / 3}
      >
        <CustomText className='text-[#1B1B1B] text-h3 mb-4 font-semibold'>
          Who Can See This Post?
        </CustomText>
        {/* Bottom sheet item */}
        <View className='flex-1 gap-5'>
          <TouchableOpacity
            onPress={() => handleSelectPostType('Public')}
            className='flex-row items-center justify-between px-3'
          >
            <CustomText>Public</CustomText>
            <Ionicons
              name='chevron-forward'
              size={24}
              color={COLORS.neutral[300]}
            />
          </TouchableOpacity>
          <View className='border-hairline  border-neutral-400' />
          <TouchableOpacity
            onPress={() => handleSelectPostType('SocialCircles')}
            className='flex-row items-center justify-between px-3'
          >
            <CustomText>Social Circles</CustomText>
            <Ionicons
              name='chevron-forward'
              size={24}
              color={COLORS.neutral[300]}
            />
          </TouchableOpacity>
        </View>
      </CustomBottomSheet>

      {/* Social Circle Bottom SHeet */}
      <CustomBottomSheet
        isActive={socialBottomSheet}
        onClose={() => setSocialBottomSheet(false)}
        maxHeight={SCREEN_HEIGHT / 1.1}
      >
        <CustomText className='text-[#1B1B1B] text-h3 mb-4 font-semibold'>
          Choose Social Circle
        </CustomText>

        <FlatList
          data={data?.data.socialCircles || []}
          keyExtractor={(item) => item._id}
          ItemSeparatorComponent={() => (
            <View className='border-hairline border-neutral-100 my-5' />
          )}
          contentContainerClassName=' pb-20'
          showsVerticalScrollIndicator={false}
          initialNumToRender={5}
          renderItem={({ item }) => (
            <TouchableOpacity
              onPress={() => handleSelectSocialCircle(item)}
              className='flex-row items-center gap-3'
            >
              <Avatar source={{ uri: item.image }} text={item.name} />
              <CustomText>{item.name}</CustomText>
            </TouchableOpacity>
          )}
        />
      </CustomBottomSheet>
    </ScreenTemplate>
  );
};

export default CreatePost;

const styles = StyleSheet.create({
  label: {
    color: COLORS.white[50],
    fontWeight: 'medium',
    fontSize: 16,
    marginBottom: normalized(6),
  },
  descriptionContainer: {
    minHeight: 120,
  },
  image: {
    width: normalized(80),
    height: normalized(80),
    borderRadius: normalized(10),
  },
});
