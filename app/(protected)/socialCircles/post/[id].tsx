import React, { memo } from 'react';
import { View } from 'react-native';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomKeyboardAvoidingView from '@/components/ui/CustomKeyboardAvoidingView';

// Custom hooks for separated logic
import { usePostData } from '@/hooks/postsHooks/usePostData';
import { usePostModal } from '@/hooks/postsHooks/usePostModal';
import { usePostParams } from '@/hooks/postsHooks/usePostParams';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

// Optimized components
import PostHeader from '@/components/PostComponents/PostHeader';
import PostCard from '@/components/PostComponents/PostCard';
import PostModal from '@/components/PostComponents/PostModal';

/**
 * Optimized SocialCirclePost component with separated concerns
 * Uses custom hooks for logic separation and memoized components for performance
 */

const SocialCirclePost = () => {
  // Extract route parameters using custom hook
  const { params, isValidParams } = usePostParams();

  // Performance monitoring
  const { measureSync } = usePerformanceMonitor({
    componentName: 'SocialCirclePost',
    logSlowRenders: true,
  });

  // Extract post data and logic using custom hook
  const {
    post,
    isLiked,
    isPostOwner,
    isLoading,
    handleToggleLike,
  } = usePostData({ postId: params.id });

  // Extract modal logic using custom hook
  const { showModal, toggleModal, closeModal } = usePostModal();

  // Early return if invalid params or no post data
  if (!isValidParams || (!post && !isLoading)) {
    return null;
  }

  // Show loading state
  if (isLoading || !post) {
    return (
      <ScreenTemplate withoutSafeAria>
        <View className='flex-1 justify-center items-center'>
          {/* Add loading component here */}
        </View>
      </ScreenTemplate>
    );
  }

  return (
    <ScreenTemplate withoutSafeAria>
      {/* Modal for image gallery */}
      <PostModal
        visible={showModal}
        images={post.image || []}
        onClose={closeModal}
      />

      {/* Header section */}
      <PostHeader
        isPostOwner={isPostOwner}
        circleImage={params.circleImage}
        circleName={params.circleName}
      />

      {/* Main content */}
      <CustomKeyboardAvoidingView>
        <View className='px-2'>
          <PostCard
            post={post}
            isLiked={isLiked}
            handleToggleLike={handleToggleLike}
            showImageModal={toggleModal}
            postId={post._id}
          />
        </View>
      </CustomKeyboardAvoidingView>
    </ScreenTemplate>
  );
};

export default SocialCirclePost;
