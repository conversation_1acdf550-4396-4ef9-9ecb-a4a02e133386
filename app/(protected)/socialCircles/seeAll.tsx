import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo, useMemo, useState } from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized, SCREEN_WIDTH, SIZES } from '@/constants/Theme';
import SearchBar from '@/layouts/SearchBar';
import Avatar from '@/components/ui/Avatar';
import CustomText from '@/components/ui/CustomText';
import useGetSocialCircles from '@/hooks/circlesHooks/useGetSocialCircles';
import { useSession } from '@/context/AuthContext';
import { Link } from 'expo-router';
import CircleSkeleton from '@/components/Skilltons/CircleSkelton';
import { ISocialCircle } from '@/types';

const _circleSize = 104;
const _spacing = 30;
const _strokeWidth = 4;

const RenderItem = memo(({ item }: { item: ISocialCircle }) => {
  return (
    <Link
      href={{
        pathname: `/(protected)/socialCircles/filteredPosts/[id]`,
        params: {
          id: item._id,
          title: item.name,
          image: item.image,
          numberOfParticipants: String(item.members?.length),
        },
      }}
      asChild
    >
      <TouchableOpacity>
        <View style={styles.circlesMainContainer}>
          <View style={styles.circleContainer}>
            <Avatar
              source={{ uri: item.image }}
              text={item.name}
              size={_circleSize}
            />
          </View>
          <CustomText
            numberOfLines={1}
            className='text-h4 text-white-50 font-medium'
          >
            {item.name}
          </CustomText>
        </View>
      </TouchableOpacity>
    </Link>
  );
});

const EmptyCirclePlaceholder = () => (
  <View style={[styles.circlesMainContainer, { opacity: 0 }]} />
);

const SocialCirclesSeeAllScreen = () => {
  const [search, setSearch] = useState('');
  const { user } = useSession();
  const { data, isLoading } = useGetSocialCircles(user?._id || '');

  const filteredData = useMemo(() => {
    const circles = search.length
      ? data?.data.socialCircles?.filter((v) =>
          v.name.toLowerCase().includes(search.toLowerCase()),
        )
      : data?.data.socialCircles;

    // Ensure an even number of items by adding an empty placeholder if necessary
    return circles && circles?.length % 2 !== 0
      ? [
          ...circles,
          {
            _id: 'placeholder',
            name: '',
            image: '',
            description: '',
            userId: '',
            createdAt: new Date(),
            members: [],
          },
        ]
      : circles;
  }, [search, data]);

  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <View className='mb-4'>
        <CustomHeader
          title='My Social Circles'
          href='/(protected)/(tabs)/socialCircles'
        >
          <Link
            href='/(protected)/socialCircles/createSocialCircle'
            className='bg-white-50/10 p-2 rounded-3xl border border-white-50/20'
          >
            <Ionicons
              name='add'
              size={normalized(24)}
              color={COLORS.white[50]}
            />
          </Link>
        </CustomHeader>
      </View>
      <SearchBar
        placeholder='Search...'
        onChangeText={setSearch}
        value={search}
      />

      {isLoading ? (
        <FlatList
          style={styles.listContainer}
          contentContainerStyle={styles.listContent}
          data={Array(10).fill('')}
          showsVerticalScrollIndicator={false}
          numColumns={2}
          keyExtractor={(item, index) => String(index)}
          renderItem={() => <CircleSkeleton />}
        />
      ) : (
        <FlatList
          style={styles.listContainer}
          contentContainerStyle={styles.listContent}
          data={filteredData}
          showsVerticalScrollIndicator={false}
          numColumns={2}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) =>
            item._id === 'placeholder' ? (
              <EmptyCirclePlaceholder />
            ) : (
              <RenderItem item={item} />
            )
          }
        />
      )}
    </ScreenTemplate>
  );
};

export default SocialCirclesSeeAllScreen;

const styles = StyleSheet.create({
  listContainer: {
    paddingVertical: normalized(30),
  },
  circlesMainContainer: {
    gap: 5,
    justifyContent: 'center',
    alignItems: 'center',
    width: SCREEN_WIDTH / 2 - _spacing,
  },
  listContent: {
    gap: _spacing,
    alignSelf: 'center',
    justifyContent: 'center',
    alignItems: 'center',
    flexGrow: 1,
  },
  circleContainer: {
    width: _circleSize + _strokeWidth,
    height: _circleSize + _strokeWidth,
    borderRadius: _circleSize / 2,
    borderColor: COLORS.secondary[300],
    borderWidth: _strokeWidth,
    justifyContent: 'center',
    alignItems: 'center',
  },
});
