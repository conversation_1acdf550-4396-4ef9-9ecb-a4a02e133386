import React, { memo } from 'react';
import { View } from 'react-native';
import { ScrollView } from 'react-native-virtualized-view';

// Layouts
import ScreenTemplate from '@/layouts/ScreenTemplate';

// Custom hooks for separated logic
import { useProfilePostData } from '@/hooks/postsHooks/useProfilePostData';
import { usePostModal } from '@/hooks/postsHooks/usePostModal';
import { usePostParams } from '@/hooks/postsHooks/usePostParams';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

// Optimized components
import ProfilePostHeader from '@/components/PostComponents/ProfilePostHeader';
import ProfilePostCard from '@/components/PostComponents/ProfilePostCard';
import PostModal from '@/components/PostComponents/PostModal';

/**
 * Optimized ProfilePost component with separated concerns
 * Uses custom hooks for logic separation and memoized components for performance
 */
const PostByID = () => {
  // Extract route parameters using custom hook
  const { params, isValidParams } = usePostParams();

  // Performance monitoring
  const { measureSync } = usePerformanceMonitor({
    componentName: 'ProfilePost',
    logSlowRenders: true,
  });

  // Extract post data and logic using custom hook
  const {
    post,
    isLiked,
    isPostOwner,
    isLoading,
    handleToggleLike,
    handlePostActions,
  } = useProfilePostData({ postId: params.id });

  // Extract modal logic using custom hook
  const { showModal, toggleModal, closeModal } = usePostModal();

  // Early return if invalid params or no post data
  if (!isValidParams || (!post && !isLoading)) {
    return null;
  }

  // Show loading state
  if (isLoading || !post) {
    return (
      <ScreenTemplate>
        <View className='flex-1 justify-center items-center'>
          {/* Add loading component here */}
        </View>
      </ScreenTemplate>
    );
  }

  return (
    <ScreenTemplate>
      {/* Modal for image gallery */}
      <PostModal
        visible={showModal}
        images={post.image || []}
        onClose={closeModal}
      />

      <View className='px-2 gap-3 flex-1'>
        {/* Header section */}
        <ProfilePostHeader />

        {/* Main content */}
        <ScrollView showsVerticalScrollIndicator={false} className='flex-1'>
          <View className='flex-1 rounded-3xl'>
            <ProfilePostCard
              post={post}
              isLiked={isLiked}
              isPostOwner={isPostOwner}
              userId={post.postedByUserId._id}
              handleToggleLike={handleToggleLike}
              handlePostActions={handlePostActions}
              showImageModal={toggleModal}
              postId={post._id}
            />
          </View>
        </ScrollView>
      </View>
    </ScreenTemplate>
  );
};

// Export memoized component for better performance
export default memo(PostByID);
