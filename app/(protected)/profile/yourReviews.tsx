import { View, Text, FlatList } from 'react-native';
import React from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { SIZES } from '@/constants/Theme';
import CustomHeader from '@/components/ui/CustomHeader';
import ReviewsComponent from '@/components/ReviewsComponent';
import { FontAwesome } from '@expo/vector-icons';
import CustomText from '@/components/ui/CustomText';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import EmptyReviewIcon from '@/assets/icons/empty-review.svg';

const YourReviews = () => {
  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <CustomHeader title='Your Reviews' />
      {/* Header and review  */}
      <View className='flex-row gap-3 items-center mt-2 mb-8'>
        <FontAwesome
          name='star'
          size={24}
          color='#FBBC05' // Yellow for the filled part
        />
        <CustomText className='text-white-50 text-h1 font-semibold'>
          4.7
        </CustomText>
        <CustomText className='text-neutral-100 text-h4'>
          ( 23 Reviews )
        </CustomText>
      </View>

      <FlatList
        data={['', '', '', '', '']}
        ListEmptyComponent={() => (
          <EmptyListComponent
            buttonTitle='Create Activity'
            emptyIcon={EmptyReviewIcon}
            title='No Reviews Yet'
            description='Host activities to connect with others and start receiving reviews!'
          />
        )}
        showsVerticalScrollIndicator={false}
        ItemSeparatorComponent={() => (
          <View className='bg-neutral-700/20 w-full h-[1px] my-5' />
        )}
        keyExtractor={(item, index) => String(index)}
        renderItem={({ item }) => {
          return (
            <ReviewsComponent
              description='Sarah was an incredible host! Super friendly, responsive, and made sure we had everything we needed. Would definitely join her activities again!'
              rate={4.5}
              username='Ali Bassem'
              avatar='https://encrypted-tbn0.gstatic.com/images?q=tbn:ANd9GcQkAJEkJQ1WumU0hXNpXdgBt9NUKc0QDVIiaw&s'
            />
          );
        }}
      />
    </ScreenTemplate>
  );
};

export default YourReviews;
