import { TouchableOpacity, View } from 'react-native';
import React from 'react';

import { COLORS, normalized, SIZES } from '@/constants/Theme';
import { Link } from 'expo-router';

import TrashIcon from '@/assets/icons/trash.svg';
import MasterCardIcon from '@/assets/icons/master-card.svg';
import Visa from '@/assets/icons/visa.svg';

import { Ionicons } from '@expo/vector-icons';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomHeader from '@/components/ui/CustomHeader';
import CustomText from '@/components/ui/CustomText';
import CustomButton from '@/components/ui/buttons/CustomButton';

interface IPaymentInfoItemTypes {
  cardName: string;
  cardNumber: string;
  cardIcon: React.ReactNode;
}
export const PaymentInfoItem = (props: IPaymentInfoItemTypes) => {
  return (
    <View className='flex-row justify-between items-center bg-primary-50/40 px-4 py-2 rounded-xl'>
      {/* Card icon and card name container */}
      <View className='flex-row items-center gap-6'>
        {props.cardIcon}
        <View className=' gap-2'>
          <CustomText className='text-white-50 text-h2 '>
            {props.cardName}
          </CustomText>

          <CustomText className='text-white-50 text-h4'>
            *** *** *** *** 237
          </CustomText>
        </View>
      </View>
      {/* Delete icon container */}
      <TouchableOpacity>
        <TrashIcon width={normalized(24)} />
      </TouchableOpacity>
    </View>
  );
};

const paymentInfo = () => {
  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <CustomHeader title='Payment Info' />
      <View className='gap-4 mt-8'>
        <PaymentInfoItem
          cardIcon={<MasterCardIcon width={normalized(24)} />}
          cardName='Master Card'
          cardNumber='237'
        />
        <PaymentInfoItem
          cardIcon={<Visa width={normalized(24)} />}
          cardName='Visa'
          cardNumber='237'
        />
      </View>
      <Link href='/(protected)/addPaymentInfo' asChild>
        <CustomButton
          variant='secondary'
          style={{
            borderRadius: normalized(5),
            marginVertical: normalized(15),
          }}
          icon={<Ionicons name='add' size={24} color={COLORS.white[50]} />}
        >
          Add Payment Method
        </CustomButton>
      </Link>
    </ScreenTemplate>
  );
};

export default paymentInfo;
