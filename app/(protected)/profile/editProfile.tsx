import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomText from '@/components/ui/CustomText';
import { ScrollView, TouchableOpacity, View } from 'react-native';
import { COLORS, normalized, SIZES } from '@/constants/Theme';
import Avatar from '@/components/ui/Avatar';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import CustomInput from '@/components/ui/CustomInput';
import CustomDatePicker from '@/components/CustomDatePicker';
import CustomPhoneInput from '@/components/ui/CustomPhoneInput';
import RadioDot from '@/components/ui/Radio/RadioDot';
import CustomButton from '@/components/ui/buttons/CustomButton';
import useUpdateUser from '@/hooks/userHooks/useUpadteUser';
import CustomHeader from '@/components/ui/CustomHeader';
import { useSession } from '@/context/AuthContext';
import CustomKeyboardAvoidingView from '@/components/ui/CustomKeyboardAvoidingView';
import { Controller } from 'react-hook-form';
import { useEffect, useCallback, useMemo } from 'react';
import { useRouter } from 'expo-router';
import { GENDER_ENUM } from '@/types';

const EditProfile = () => {
  const { user, setUser } = useSession();
  const router = useRouter();
  const {
    control,
    watch,
    isSuccess,
    data,
    handleSaveProfile,
    setValue,
    errors,
    isLoading,
    pickImage,
  } = useUpdateUser(user);

  // Memoize handlers to prevent re-renders
  const handleCountryCodeChange = useCallback(
    (code: string) => {
      setValue('countryCode', code);
    },
    [setValue],
  );

  const handleGenderChange = useCallback(
    (gender: GENDER_ENUM) => {
      setValue('gender', gender);
    },
    [setValue],
  );

  const handleSavePress = useCallback(() => {
    handleSaveProfile({ userId: user?._id || '' });
  }, [handleSaveProfile, user?._id]);

  // Memoize form values that are used in multiple places
  const watchedValues = useMemo(
    () => ({
      displayImage: watch('displayImage') || '',
      username: watch('username') || '',
      countryCode: watch('countryCode') || '',
      gender: watch('gender'),
    }),
    [
      watch('displayImage'),
      watch('username'),
      watch('countryCode'),
      watch('gender'),
    ],
  );

  // Handle navigation after successful update
  useEffect(() => {
    if (isSuccess && data?.user) {
      setUser(data.user);
      router.replace('/(protected)/(tabs)/profile');
    }
  }, [isSuccess, data?.user, setUser, router]);

  // Memoize UI components for better performance
  const GenderSelector = useMemo(
    () => (
      <View className='gap-4 flex-row'>
        <TouchableOpacity
          onPress={() => handleGenderChange(GENDER_ENUM.MALE)}
          className='flex items-center flex-1 justify-between flex-row bg-white-50/10 px-4 py-3 rounded-xl'
        >
          <CustomText className='text-white-50 font-semibold text-h5'>
            Male
          </CustomText>
          <RadioDot checked={watchedValues.gender === GENDER_ENUM.MALE} />
        </TouchableOpacity>
        <TouchableOpacity
          onPress={() => handleGenderChange(GENDER_ENUM.FEMALE)}
          className='flex items-center flex-1 justify-between flex-row bg-white-50/10 px-4 py-3 rounded-xl'
        >
          <CustomText className='text-white-50 font-semibold text-h5'>
            Female
          </CustomText>
          <RadioDot checked={watchedValues.gender === GENDER_ENUM.FEMALE} />
        </TouchableOpacity>
      </View>
    ),
    [handleGenderChange, watchedValues.gender],
  );

  return (
    <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
      <CustomHeader title='Edit Profile' />

      <CustomKeyboardAvoidingView>
        <ScrollView
          keyboardDismissMode='on-drag'
          showsVerticalScrollIndicator={false}
        >
          <View className='flex-1 pb-5'>
            {/* Avatar Section */}
            <TouchableOpacity
              className='relative mx-auto mt-12'
              onPress={pickImage}
            >
              <Avatar
                source={{ uri: watchedValues.displayImage }}
                text={user?.displayName}
                size={normalized(80)}
              />
              <View className='absolute -bottom-3 right-5 bg-secondary-300 rounded-full w-10 h-10 items-center justify-center'>
                <MaterialCommunityIcons
                  name='pencil'
                  size={normalized(12)}
                  color={COLORS.white[50]}
                />
              </View>
            </TouchableOpacity>

            {/* Form Fields */}
            <View className='flex-1 gap-3 mt-6'>
              <CustomInput
                editable={false}
                title='Username'
                control={control}
                name='username'
                value={`@${watchedValues.username}`}
              />

              <CustomInput
                error={errors.displayName?.message}
                title='Full Name'
                control={control}
                name='displayName'
              />

              <CustomInput
                error={errors.email?.message}
                title='Email'
                control={control}
                name='email'
              />

              <Controller
                name='dateOfBirth'
                control={control}
                render={({ field: { value, onChange } }) => (
                  <CustomDatePicker
                    minimumDate={new Date(1960, 0, 1)}
                    maximumDate={new Date()}
                    onDateChange={onChange}
                    title='BirthDate'
                    value={new Date(value || '')}
                  />
                )}
              />

              <Controller
                control={control}
                name='phoneNumber'
                render={({ field: { onChange, value } }) => (
                  <CustomPhoneInput
                    isOptional
                    title='Phone Number'
                    countryCode={watchedValues.countryCode}
                    onCountryCodeChange={handleCountryCodeChange}
                    phoneNumber={value || ''}
                    onPhoneNumberChange={onChange}
                    error={
                      errors.phoneNumber?.message || errors.countryCode?.message
                    }
                  />
                )}
              />

              <CustomText className='text-white-50 -mb-2 text-h4'>
                Gender
              </CustomText>

              {GenderSelector}
            </View>

            <CustomButton
              className='mb-3 mt-8'
              loading={isLoading}
              onPress={handleSavePress}
            >
              Save
            </CustomButton>
          </View>
        </ScrollView>
      </CustomKeyboardAvoidingView>
    </ScreenTemplate>
  );
};

export default EditProfile;
