import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Fragment, memo, useCallback, useMemo, useState } from 'react';
import { Href, Link } from 'expo-router';
import { AntDesign } from '@expo/vector-icons';
import { COLORS, normalized, SIZES } from '@/constants/Theme';
import { useSession } from '@/context/AuthContext';

import ScreenTemplate from '@/layouts/ScreenTemplate';
import CustomText from '@/components/ui/CustomText';
import CustomSwitch from '@/components/ui/CustomSwitch';
import CustomHeader from '@/components/ui/CustomHeader';
import LineSeparator from '@/components/ui/LineSeparator';

import CustomActionModal from '@/layouts/CustomActionModal';
import useToggleUserNotifications from '@/hooks/NotificationHooks/UseToggleUserNotifications';
import useDeleteUserAccount from '@/hooks/userHooks/UseDeleteUserAccount';

type ISettingsTypes = {
  title: string;
  active: boolean;
  onToggle: () => void;
};

type ISettingsRouteItemTypes = {
  title: string;
  href: Href;
};

const SettingsRouteItem = memo((props: ISettingsRouteItemTypes) => (
  <Link href={props.href} asChild>
    <TouchableOpacity style={styles.item}>
      <CustomText className='text-white-50 text-h4'>{props.title}</CustomText>
      <AntDesign name='right' size={normalized(18)} color={COLORS.white[50]} />
    </TouchableOpacity>
  </Link>
));

// TODO see the state from backend and activate or deactivate the switch depends on the state

const SettingsSwitchItem = memo((props: ISettingsTypes) => (
  <View style={styles.item}>
    <CustomText className='text-white-50 text-h4'>{props.title}</CustomText>
    <CustomSwitch active={props.active} onPress={props.onToggle} />
  </View>
));

const ProfileSettingsScreen = () => {
  const { signOut, user } = useSession();

  const [activeNotifications, setActiveNotifications] = useState(false);
  const [logoutModal, setLogoutModal] = useState(false);
  const [deleteAccountModal, setDeleteAccountModal] = useState(false);

  const { mutate: toggleNotifications } = useToggleUserNotifications();
  const { mutate: deleteMutation, isPending } = useDeleteUserAccount();

  const handleToggleUserNotifications = useCallback(() => {
    toggleNotifications(activeNotifications);
    setActiveNotifications((prev) => !prev);
  }, [activeNotifications, toggleNotifications]);

  const settings = useMemo(
    () => [
      { title: 'Edit Profile', href: '/(protected)/profile/editProfile' },
      { title: 'Change Password', href: '/(protected)/profile/changePassword' },
      { isSwitch: true, title: 'Notifications' },
      { title: 'Language', href: '/(protected)/profile/language' },
      { title: 'Interests', href: '/(protected)/profile/profileInterests' },
      { title: 'My Reviews', href: '/(protected)/profile/yourReviews' },
    ],
    [],
  );

  return (
    <Fragment>
      {/* Logout Modal */}
      <CustomActionModal
        position='center'
        visible={logoutModal}
        actionButtonTitle='Logout'
        cancelButtonTitle='Cancel'
        onAction={signOut}
        onCancel={() => setLogoutModal(false)}
        animationType='fade'
        actionButtonStyle={styles.modalButtonSecondary}
        cancelButtonStyle={styles.modalButtonNeutral}
        contentStyle={styles.modalContent}
      >
        <CustomText className='text-h3 font-medium text-[#260B41] text-center'>
          Are You Sure You Want to Log Out?
        </CustomText>
      </CustomActionModal>

      {/* Delete Account Modal */}
      <CustomActionModal
        position='center'
        visible={deleteAccountModal}
        actionButtonTitle='Delete'
        cancelButtonTitle='Cancel'
        onAction={deleteMutation}
        onCancel={() => setDeleteAccountModal(false)}
        actionButtonDisabled={isPending}
        actionButtonLoading={isPending}
        animationType='fade'
        actionButtonStyle={styles.modalButtonSecondary}
        cancelButtonStyle={styles.modalButtonNeutral}
        contentStyle={styles.modalContent}
      >
        <CustomText className='text-[24px] font-semibold mb-4 text-[#260B41] text-center'>
          Delete Account
        </CustomText>
        <CustomText className='text-[20px] text-[#1C1C1C] text-center'>
          Are you sure you want to delete this account?
        </CustomText>
      </CustomActionModal>

      <ScreenTemplate style={{ paddingHorizontal: SIZES.xxSmall }}>
        <CustomHeader title='Profile' href='/(protected)/(tabs)/profile' />

        <ScrollView
          style={styles.scrollContainer}
          showsVerticalScrollIndicator={false}
        >
          {settings.map((item, index) => (
            <Fragment key={index}>
              {item.isSwitch ? (
                <SettingsSwitchItem
                  title={item.title}
                  active={
                    user?.preferences?.receiveNotifications ||
                    activeNotifications
                  }
                  onToggle={handleToggleUserNotifications}
                />
              ) : (
                <SettingsRouteItem
                  title={item.title}
                  href={item.href as Href}
                />
              )}
              <LineSeparator />
            </Fragment>
          ))}

          <TouchableOpacity
            style={styles.item}
            onPress={() => setDeleteAccountModal(true)}
          >
            <CustomText className='text-white-50 text-h4'>
              Delete Account
            </CustomText>
            <AntDesign
              name='right'
              size={normalized(18)}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>

          <LineSeparator />

          <TouchableOpacity
            style={{ paddingHorizontal: normalized(12) }}
            onPress={() => setLogoutModal(true)}
          >
            <CustomText className='text-secondary-300 text-h4'>
              Logout
            </CustomText>
          </TouchableOpacity>
        </ScrollView>
      </ScreenTemplate>
    </Fragment>
  );
};

export default ProfileSettingsScreen;

const styles = StyleSheet.create({
  scrollContainer: {
    flex: 1,
    marginTop: normalized(20),
    paddingBottom: normalized(20),
  },
  item: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: normalized(12),
    paddingVertical: normalized(8),
  },
  modalButtonSecondary: {
    backgroundColor: COLORS.secondary[300],
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: normalized(20),
    padding: normalized(10),
  },
  modalButtonNeutral: {
    backgroundColor: COLORS.neutral[100],
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
    padding: normalized(10),
  },
  modalContent: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
