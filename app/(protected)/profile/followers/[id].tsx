import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  ActivityIndicator,
  Animated,
} from 'react-native';
import React, {
  memo,
  useEffect,
  useState,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import { useLocalSearchParams } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';

// Components
import ScreenTemplate from '@/layouts/ScreenTemplate';
import SearchBar from '@/layouts/SearchBar';
import UserWithActionButton from '@/components/userWithActionButton';
import CustomText from '@/components/ui/CustomText';
import CustomHeader from '@/components/ui/CustomHeader';
import Loading from '@/layouts/Loading';

// Hooks
import { useSession } from '@/context/AuthContext';
import useGetFollowers from '@/hooks/userHooks/useGetFollowers';
import useFollowAndUnfollowUser from '@/hooks/userHooks/useFolllowAndUnfollowUser';

// Types & Constants
import { IFollower, IUserInfo } from '@/types';
import { COLORS, normalized } from '@/constants/Theme';

interface AddButtonProps {
  isFollowed: boolean;
  onPress: () => void;
  isLoading?: boolean;
  disabled?: boolean;
}

const AddButton: React.FC<AddButtonProps> = memo(
  ({ isFollowed, onPress, isLoading, disabled }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePress = useCallback(() => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      onPress();
    }, [onPress, scaleAnim]);

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          onPress={handlePress}
          disabled={isLoading || disabled}
          className='text-h5 flex-row items-center gap-2 py-2 px-2 rounded-lg'
          style={[
            {
              backgroundColor: isFollowed
                ? 'transparent'
                : COLORS.secondary[300],
              opacity: isLoading || disabled ? 0.7 : 1,
              borderWidth: isFollowed ? 1 : 0,
              borderColor: isFollowed ? COLORS.secondary[300] : 'transparent',
            },
          ]}
          activeOpacity={0.8}
        >
          {isLoading ? (
            <Loading isLoading />
          ) : (
            <>
              <Ionicons
                name={isFollowed ? 'checkmark' : 'add'}
                color={isFollowed ? COLORS.secondary[300] : COLORS.white[50]}
                size={normalized(18)}
              />
              <CustomText
                className='text-h6'
                style={[
                  {
                    color: isFollowed
                      ? COLORS.secondary[300]
                      : COLORS.white[50],
                  },
                ]}
              >
                {isFollowed ? 'Following' : 'Follow'}
              </CustomText>
            </>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  },
);

const UserFollowers: React.FC = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, setUser } = useSession();
  const { data, refetch } = useGetFollowers(id);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [followers, setFollowers] = useState<IFollower[]>([]);

  useEffect(() => {
    if (data?.followers) {
      setFollowers(
        data.followers.map((follower) => ({
          ...follower,
          isFollowed: user?.following?.some((v) => v._id === follower._id),
        })),
      );
    }
  }, [data, user]);

  const { handleToggleFollow, isLoading } = useFollowAndUnfollowUser();

  const handleFollow = useCallback(
    (userId: string, isCurrentlyFollowed: boolean) => {
      // Optimistic UI update
      setFollowers((prev) =>
        prev.map((follower) =>
          follower._id === userId
            ? { ...follower, isFollowed: !isCurrentlyFollowed }
            : follower,
        ),
      );

      // Update user's following list optimistically
      if (user && setUser) {
        setUser((prev) => {
          if (!prev) return prev;

          const updatedFollowing = isCurrentlyFollowed
            ? prev.following?.filter((follower) => follower._id !== userId)
            : [...(prev.following || []), { _id: userId } as IUserInfo];

          return {
            ...prev,
            following: updatedFollowing,
          };
        });
      }

      // Call API
      handleToggleFollow(userId, isCurrentlyFollowed);
    },
    [handleToggleFollow, setUser, user],
  );

  // Handle refresh action with a custom loading indicator
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    refetch().finally(() => setIsRefreshing(false)); // Stop loading when done
  }, [refetch]);

  // Add search functionality
  const [searchQuery, setSearchQuery] = useState('');
  const { isLoading: isLoadingFollowers, isError } = useGetFollowers(id);

  // Filter followers based on search query
  const filteredFollowers = useMemo(() => {
    if (!searchQuery.trim()) return followers;

    return followers.filter(
      (follower) =>
        follower.displayName
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        (follower.username &&
          follower.username.toLowerCase().includes(searchQuery.toLowerCase())),
    );
  }, [followers, searchQuery]);

  // Handle search
  const handleSearch = useCallback((text: string) => {
    setSearchQuery(text);
  }, []);

  // Render empty state
  const renderEmptyComponent = useCallback(() => {
    if (isLoadingFollowers && !isRefreshing) return null;

    return (
      <View style={styles.emptyContainer}>
        <View style={styles.emptyIconContainer}>
          <Ionicons
            name='people-outline'
            size={normalized(50)}
            color={COLORS.neutral[600]}
          />
        </View>
        <CustomText style={styles.emptyTitle}>
          {searchQuery ? 'No results found' : 'No followers yet'}
        </CustomText>
        <CustomText style={styles.emptyDescription}>
          {searchQuery
            ? 'Try a different search term'
            : "When people follow you, they'll appear here"}
        </CustomText>
      </View>
    );
  }, [isLoadingFollowers, isRefreshing, searchQuery]);

  // Memoized render item function for better performance
  const renderFollowerItem = useCallback(
    ({ item }: { item: IFollower }) => (
      <View style={styles.listItemContainer}>
        <UserWithActionButton
          avatar={item.image}
          size={38}
          name={item.displayName}
          href={{
            pathname: `/(protected)/(tabs)/profile`,
            params: { id: item._id },
          }}
        >
          {user && user._id !== item._id && (
            <AddButton
              isFollowed={item.isFollowed || false}
              isLoading={isLoading && item._id === user?._id}
              onPress={() => handleFollow(item._id, item.isFollowed || false)}
            />
          )}
        </UserWithActionButton>
      </View>
    ),
    [user, isLoading, handleFollow],
  );

  return (
    <ScreenTemplate>
      <View style={styles.container}>
        <View style={styles.header}>
          <CustomHeader
            href={`/(protected)/(tabs)/profile`}
            title='Followers'
          />
          <SearchBar
            placeholder='Search followers...'
            onChangeText={handleSearch}
            value={searchQuery}
            containerStyles=' rounded-3xl h-14'
          />
        </View>

        {isError ? (
          <View style={styles.errorContainer}>
            <Ionicons
              name='alert-circle-outline'
              size={normalized(40)}
              color={COLORS.neutral[400]}
            />
            <CustomText style={styles.errorText}>
              Something went wrong. Please try again.
            </CustomText>
            <TouchableOpacity
              style={styles.retryButton}
              onPress={() => refetch()}
              activeOpacity={0.8}
            >
              <CustomText style={styles.retryText}>Retry</CustomText>
            </TouchableOpacity>
          </View>
        ) : (
          <FlatList
            ListHeaderComponent={
              isLoadingFollowers && !isRefreshing ? (
                <View style={styles.loadingContainer}>
                  <ActivityIndicator
                    size='small'
                    color={COLORS.secondary[300]}
                  />
                </View>
              ) : isRefreshing ? (
                <View style={styles.refreshContainer}>
                  <Loading isLoading />
                </View>
              ) : null
            }
            onRefresh={handleRefresh}
            refreshing={isRefreshing}
            data={filteredFollowers}
            keyExtractor={(item) => item?._id}
            contentContainerStyle={[
              styles.listContainer,
              filteredFollowers.length === 0 && styles.emptyListContainer,
            ]}
            ListEmptyComponent={renderEmptyComponent}
            renderItem={renderFollowerItem}
            removeClippedSubviews={true}
            maxToRenderPerBatch={10}
            windowSize={10}
            initialNumToRender={10}
            updateCellsBatchingPeriod={50}
            getItemLayout={(_data, index) => ({
              length: normalized(80),
              offset: normalized(80) * index,
              index,
            })}
          />
        )}
      </View>
    </ScreenTemplate>
  );
};

export default UserFollowers;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: normalized(16),
    gap: normalized(16),
  },
  header: {
    gap: normalized(12),
  },
  searchBar: {
    backgroundColor: COLORS.neutral[800],
    borderRadius: normalized(12),
  },
  listContainer: {
    paddingVertical: normalized(20),
    gap: normalized(20),
  },
  listItemContainer: {
    marginBottom: normalized(12),
    backgroundColor: 'transparent',
    borderRadius: normalized(12),
    padding: normalized(8),
  },

  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: normalized(40),
    gap: normalized(12),
  },
  emptyIconContainer: {
    width: normalized(80),
    height: normalized(80),
    borderRadius: normalized(40),
    backgroundColor: COLORS.neutral[800],
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: normalized(8),
  },
  emptyTitle: {
    fontSize: normalized(18),
    fontWeight: '600',
    color: COLORS.white[50],
    marginTop: normalized(12),
  },
  emptyDescription: {
    fontSize: normalized(14),
    color: COLORS.neutral[400],
    textAlign: 'center',
    paddingHorizontal: normalized(20),
    lineHeight: normalized(20),
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: normalized(40),
    gap: normalized(16),
  },
  errorText: {
    fontSize: normalized(16),
    color: COLORS.white[50],
    textAlign: 'center',
    paddingHorizontal: normalized(20),
  },
  retryButton: {
    backgroundColor: COLORS.secondary[300],
    paddingVertical: normalized(12),
    paddingHorizontal: normalized(24),
    borderRadius: normalized(12),
    marginTop: normalized(8),
  },
  retryText: {
    color: COLORS.white[50],
    fontWeight: '600',
    fontSize: normalized(14),
  },
  loadingContainer: {
    paddingVertical: normalized(40),
    justifyContent: 'center',
    alignItems: 'center',
  },
  refreshContainer: {
    paddingVertical: normalized(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
  emptyListContainer: {
    flexGrow: 1,
    justifyContent: 'center',
  },
});
