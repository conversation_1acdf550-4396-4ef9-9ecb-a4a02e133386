import {
  ActivityIndicator,
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  Animated,
} from 'react-native';
import React, {
  memo,
  useState,
  useEffect,
  useCallback,
  useMemo,
  useRef,
} from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import { COLORS, SIZES, normalized } from '@/constants/Theme';
import { useLocalSearchParams } from 'expo-router';
import useGetFollowing from '@/hooks/userHooks/useGetFollowing';
import SearchBar from '@/layouts/SearchBar';
import UserWithActionButton from '@/components/userWithActionButton';
import { Ionicons } from '@expo/vector-icons';
import CustomText from '@/components/ui/CustomText';
import { useSession } from '@/context/AuthContext';
import CustomHeader from '@/components/ui/CustomHeader';
// Removed unused import
import useUnFollowUser from '@/hooks/userHooks/useUnfollowUser';
import { IFollower } from '@/types';
import Loading from '@/layouts/Loading';
import { customToast } from '@/hooks/useCustomToast';

interface UnFollowButtonProps {
  onPress: () => void;
  isLoading?: boolean;
}

const UnFollowButton: React.FC<UnFollowButtonProps> = memo(
  ({ onPress, isLoading = false }) => {
    const scaleAnim = useRef(new Animated.Value(1)).current;

    const handlePress = useCallback(() => {
      Animated.sequence([
        Animated.timing(scaleAnim, {
          toValue: 0.95,
          duration: 100,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 1,
          duration: 100,
          useNativeDriver: true,
        }),
      ]).start();
      onPress();
    }, [onPress, scaleAnim]);

    return (
      <Animated.View style={{ transform: [{ scale: scaleAnim }] }}>
        <TouchableOpacity
          onPress={handlePress}
          style={styles.unfollowButton}
          disabled={isLoading}
          activeOpacity={0.8}
        >
          {isLoading ? (
            <ActivityIndicator size='small' color={COLORS.white[50]} />
          ) : (
            <>
              <Ionicons
                name='close'
                color={COLORS.white[50]}
                size={normalized(16)}
              />
              <CustomText style={styles.unfollowText}>Unfollow</CustomText>
            </>
          )}
        </TouchableOpacity>
      </Animated.View>
    );
  },
);

const UserFollowing: React.FC = () => {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { user, setUser } = useSession();
  const { data, refetch, isLoading: fetchLoading } = useGetFollowing(id);
  const [following, setFollowing] = useState<IFollower[]>([]);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [pendingUnfollows, setPendingUnfollows] = useState<
    Record<string, boolean>
  >({});

  // Handle the data fetching and refreshing
  useEffect(() => {
    if (data?.following) {
      setFollowing(data.following);
    }
  }, [data]);

  // Use direct unfollow hook for better control
  const { mutate: unfollowUser } = useUnFollowUser();

  // Handle the unfollow action with proper error handling
  const handleUnFollow = useCallback(
    (userId: string) => {
      // Prevent multiple clicks
      if (pendingUnfollows[userId]) {
        return;
      }

      // Mark this user as being unfollowed
      setPendingUnfollows((prev) => ({
        ...prev,
        [userId]: true,
      }));

      // Optimistic UI update: remove from list immediately
      setFollowing((prevFollowing) =>
        prevFollowing.filter((user) => user._id !== userId),
      );

      // Update user's following list in context
      if (user && setUser) {
        setUser((prev) => {
          if (!prev) return prev;
          return {
            ...prev,
            following:
              prev.following?.filter((followId) => followId._id !== userId) ||
              [],
            followingCount: Math.max(0, (prev.followingCount || 0) - 1),
          };
        });
      }

      // Call API to actually unfollow
      unfollowUser(userId, {
        onSuccess: () => {
          // Show success message
          customToast('Unfollowed successfully', 'success');

          // Clear pending state
          setPendingUnfollows((prev) => ({
            ...prev,
            [userId]: false,
          }));
        },
        onError: () => {
          // Show error message
          customToast('Failed to unfollow', 'error');

          // Revert optimistic updates
          setFollowing((prev) => {
            // Find the user in the original data
            const userToRestore = data?.following?.find(
              (u) => u._id === userId,
            );
            if (userToRestore) {
              return [...prev, userToRestore];
            }
            return prev;
          });

          // Revert user context update
          if (user && setUser) {
            setUser((prev) => {
              if (!prev) return prev;
              return {
                ...prev,
                following: [
                  ...(prev.following || []),
                  { _id: userId, username: '', displayName: '' },
                ],
                followingCount: (prev.followingCount || 0) + 1,
              };
            });
          }

          // Clear pending state
          setPendingUnfollows((prev) => ({
            ...prev,
            [userId]: false,
          }));
        },
      });
    },
    [user, setUser, unfollowUser, data, pendingUnfollows],
  );

  // Handle refresh action with a custom loading indicator
  const handleRefresh = useCallback(() => {
    setIsRefreshing(true);
    refetch().finally(() => setIsRefreshing(false)); // Stop loading when done
  }, [refetch]);

  return (
    <ScreenTemplate style={{ paddingHorizontal: normalized(16) }}>
      <View style={styles.headerContainer}>
        <CustomHeader title='Following' />
        <SearchBar placeholder='Search...' containerStyles='rounded-3xl h-14' />
        {fetchLoading && <Loading isLoading />}
      </View>
      <FlatList
        ListHeaderComponent={() =>
          isRefreshing && (
            <View style={styles.refreshContainer}>
              <Loading isLoading />
            </View>
          )
        }
        onRefresh={handleRefresh}
        refreshing={isRefreshing}
        style={styles.list}
        contentContainerStyle={styles.listContent}
        data={following}
        keyExtractor={(item) => item?._id}
        renderItem={({ item }) => (
          <View style={styles.listItemContainer}>
            <UserWithActionButton
              avatar={item.image}
              name={item.displayName}
              size={44}
              href={{
                pathname: `/(protected)/(tabs)/profile`,
                params: { id: item._id },
              }}
            >
              {user && user._id !== item._id && user._id === id && (
                <UnFollowButton
                  onPress={() => handleUnFollow(item._id)}
                  isLoading={pendingUnfollows[item._id] || false}
                />
              )}
            </UserWithActionButton>
          </View>
        )}
      />
    </ScreenTemplate>
  );
};

export default UserFollowing;

const styles = StyleSheet.create({
  headerContainer: {
    gap: normalized(12),
  },
  list: {
    marginTop: normalized(12),
    paddingTop: normalized(12),
  },
  listContent: {
    gap: normalized(10),
  },
  listItemContainer: {
    marginBottom: normalized(12),
    backgroundColor: 'transparent',
    borderRadius: normalized(12),
    padding: normalized(8),
  },
  unfollowButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 1,
    borderColor: COLORS.secondary[300],
    backgroundColor: 'transparent',
    paddingHorizontal: normalized(10),
    borderRadius: normalized(8),
    height: normalized(32),
    minWidth: normalized(80),
    gap: normalized(4),
  },
  unfollowText: {
    color: COLORS.white[50],
    fontSize: normalized(12),
    fontWeight: '500',
  },
  refreshContainer: {
    paddingVertical: normalized(20),
    justifyContent: 'center',
    alignItems: 'center',
  },
});
