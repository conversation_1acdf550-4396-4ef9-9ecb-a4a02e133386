import { View } from 'react-native';
import React from 'react';
import ScreenTemplate from '@/layouts/ScreenTemplate';
import Animated, { FadeInUp } from 'react-native-reanimated';

const SplashScreen = () => {
  return (
    <ScreenTemplate>
      <View className='flex-1 justify-center items-center'>
        <Animated.Image
          fadeDuration={500}
          entering={FadeInUp.duration(1000).springify()}
          source={require('../assets/full-logo.png')}
          className='object-contain w-40 h-40 justify-center items-center'
        />
      </View>
    </ScreenTemplate>
  );
};

export default SplashScreen;
