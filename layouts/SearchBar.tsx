import React, { useCallback, useState, useEffect, memo } from 'react';
import {
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View,
  StyleSheet,
} from 'react-native';
import EvilIcons from '@expo/vector-icons/EvilIcons';
import { COLORS, normalized } from '@/constants/Theme';
import { useDebounce } from '@/hooks/useDebounce';

interface SearchBarProps extends TextInputProps {
  iconColor?: string;
  textColor?: string;
  containerStyles?: string;
  debounceTime?: number;
  onSearch?: (text: string) => void; // New prop for search callback
}

/**
 * Optimized SearchBar component with debouncing to reduce API calls
 * Only triggers search after user stops typing
 */
const SearchBar: React.FC<SearchBarProps> = memo(
  ({
    iconColor = COLORS.gray_colors[100],
    textColor = COLORS.white[50],
    debounceTime = 500,
    onSearch,
    ...props
  }) => {
    const [inputValue, setInputValue] = useState<string>(props.value || '');
    const debouncedValue = useDebounce(inputValue, debounceTime);

    // Handle text input changes
    const handleChangeText = useCallback((text: string) => {
      setInputValue(text);
    }, []);

    // Update `onChangeText` only when the debounced value changes
    useEffect(() => {
      // Use onSearch if provided, otherwise fall back to onChangeText
      if (onSearch) {
        onSearch(debouncedValue);
      } else if (props.onChangeText) {
        props.onChangeText(debouncedValue);
      }
    }, [debouncedValue, props.onChangeText, onSearch]);

    // Clear the search input
    const handleClear = useCallback(() => {
      setInputValue('');
      // Immediately trigger search with empty string
      if (onSearch) {
        onSearch('');
      } else if (props.onChangeText) {
        props.onChangeText('');
      }
    }, [props.onChangeText, onSearch]);

    return (
      <View
        className={`px-2 py-1 items-center border border-neutral-50/20 flex-row bg-neutral-300/20 rounded-2xl ${props.containerStyles}`}
      >
        <EvilIcons name='search' size={normalized(24)} color={iconColor} />
        <TextInput
          className='font-poppins py-2.5 text-body1'
          {...props}
          placeholderTextColor={COLORS.neutral[300]}
          value={inputValue}
          onChangeText={handleChangeText}
          style={[styles.textInput, { color: textColor }]}
          numberOfLines={props.numberOfLines || 1}
          returnKeyType='search'
        />
        {inputValue.length > 0 && (
          <TouchableOpacity
            onPress={handleClear}
            hitSlop={{ top: 10, bottom: 10, left: 10, right: 10 }}
          >
            <EvilIcons name='close' size={normalized(14)} color={iconColor} />
          </TouchableOpacity>
        )}
      </View>
    );
  },
);

const styles = StyleSheet.create({
  textInput: {
    flex: 1,
    backgroundColor: 'transparent',
    marginLeft: 8,
  },
});

export default SearchBar;
