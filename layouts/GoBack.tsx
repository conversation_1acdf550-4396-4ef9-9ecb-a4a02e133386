import { View, TouchableOpacity, ViewStyle } from 'react-native';
import React, { memo } from 'react';
import { AntDesign } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import { Href, useRouter } from 'expo-router';

type IGoBackTypes = {
  href?: Href;
  iconColor?: string;
  iconSize?: number;
  styles?: ViewStyle;
  onPress?: () => void;
};
const GoBack = (props: IGoBackTypes) => {
  const router = useRouter();
  // Handle navigation with custom handler or default behavior
  const handlePress = () => {
    if (props.onPress) {
      // Use custom handler if provided
      props.onPress();
    } else if (router.canGoBack()) {
      // Default back navigation
      router.back();
    }
  };

  return (
    <View style={props.styles}>
      {!props.href && (
        <TouchableOpacity onPress={handlePress}>
          <AntDesign
            name='left'
            size={normalized(props.iconSize || 20)}
            color={props.iconColor?.length ? props.iconColor : COLORS.white[50]}
          />
        </TouchableOpacity>
      )}
      {props.href && (
        <TouchableOpacity
          onPress={() => props.href && router.replace(props.href)}
        >
          <AntDesign
            name='left'
            size={normalized(props.iconSize || 20)}
            color={props.iconColor?.length ? props.iconColor : COLORS.white[50]}
          />
        </TouchableOpacity>
      )}
    </View>
  );
};

export default memo(GoBack);
