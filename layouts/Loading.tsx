import React, { memo, useRef } from 'react';
import LottieView, { LottieViewProps } from 'lottie-react-native';

interface ICustomLoadingTypes extends Omit<LottieViewProps, 'source'> {
  isLoading: boolean;
  size?: number;
}

const Loading = ({ isLoading, size = 40, ...rest }: ICustomLoadingTypes) => {
  const animation = useRef<LottieView | null>(null);

  if (!isLoading) return null; // Don't render when not loading

  return (
    <LottieView
      ref={animation}
      source={require('@/assets/lottie/loading.json')}
      autoPlay
      loop
      style={{ width: size, height: size }}
      {...rest} // Spread remaining props
    />
  );
};

export default memo(Loading);
