import React, { memo } from 'react';
import { LinearGradient } from 'expo-linear-gradient';
import { gradients } from '@/constants/Theme';
import { SafeAreaView } from 'react-native-safe-area-context';
import { StyleProp, ViewStyle } from 'react-native';

// Screen wrapper template for each screen to show the gradient
const ScreenWrapper = ({
  children,
  style,
  withoutSafeAria,
}: {
  children: React.ReactNode;
  style?: StyleProp<ViewStyle>;
  withoutSafeAria?: boolean;
}) => {
  return (
    <LinearGradient colors={gradients.primary} style={[{ flex: 1 }, style]}>
      {withoutSafeAria && children}
      {!withoutSafeAria && (
        <SafeAreaView style={{ flex: 1 }}>{children}</SafeAreaView>
      )}
    </LinearGradient>
  );
};

export default memo(ScreenWrapper);
