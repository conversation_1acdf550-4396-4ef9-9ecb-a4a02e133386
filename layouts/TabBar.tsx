import { COLORS, normalized } from '@/constants/Theme';
import { BottomTabBarProps } from '@react-navigation/bottom-tabs';
import React, { forwardRef, memo, useCallback, useMemo } from 'react';
import { TouchableOpacity, View, StyleSheet, Platform } from 'react-native';
// Tab bar icons
import { TAB_ICONS } from '@/constants/Images';
// Performance monitoring
import {
  useTabNavigationPerformance,
  useAndroidTabOptimizations,
} from '@/hooks/useTabNavigationPerformance';

// Route names
type Icons = 'index' | 'socialCircles' | 'activities' | 'messages' | 'profile';
const _ICON_SIZE = Platform.OS === 'ios' ? 22.2 : 21;

// Memoized icon size to prevent recalculation
const ICON_SIZE = normalized(_ICON_SIZE);

// Memoized icon render function for better performance
const IconsRender = memo(
  ({ title, isFocused }: { title: Icons; isFocused: boolean }) => {
    const iconProps = useMemo(
      () => ({
        width: ICON_SIZE,
        height: ICON_SIZE,
      }),
      [],
    );

    switch (title) {
      case 'index':
        return isFocused ? (
          <TAB_ICONS.HOME.filled {...iconProps} />
        ) : (
          <TAB_ICONS.HOME.outLined {...iconProps} />
        );

      case 'activities':
        return isFocused ? (
          <TAB_ICONS.ACTIVITIES.filled {...iconProps} />
        ) : (
          <TAB_ICONS.ACTIVITIES.outLined {...iconProps} />
        );
      case 'messages':
        return isFocused ? (
          <TAB_ICONS.MYCIRCLES.filled {...iconProps} />
        ) : (
          <TAB_ICONS.MYCIRCLES.outLined {...iconProps} />
        );
      case 'socialCircles':
        return isFocused ? (
          <TAB_ICONS.CLUBS.filled {...iconProps} />
        ) : (
          <TAB_ICONS.CLUBS.outLined {...iconProps} />
        );
      case 'profile':
        return isFocused ? (
          <TAB_ICONS.PROFILE.filled {...iconProps} />
        ) : (
          <TAB_ICONS.PROFILE.outLined {...iconProps} />
        );
      default:
        return null;
    }
  },
);

const TAB_ORDER: Icons[] = [
  'index',
  'activities',
  'socialCircles',
  'messages',
  'profile',
];

// Memoized tab button component with Android optimizations
const TabButton = memo(
  ({
    tab,
    route,
    options,
    isFocused,
    onPress,
    androidOptimizations,
  }: {
    tab: Icons;
    route: any;
    options: any;
    isFocused: boolean;
    onPress: () => void;
    androidOptimizations?: any;
  }) => {
    const buttonStyle = useMemo(
      () => ({
        backgroundColor: isFocused ? COLORS.primary[50] : COLORS.black,
        borderRadius: normalized(20),
        paddingHorizontal: normalized(4.2),
      }),
      [isFocused],
    );

    // Apply Android-specific touch optimizations
    const touchProps = useMemo(
      () => ({
        activeOpacity:
          androidOptimizations?.touchOptimization?.activeOpacity || 0.7,
        delayPressIn:
          androidOptimizations?.touchOptimization?.delayPressIn || 0,
        delayPressOut:
          androidOptimizations?.touchOptimization?.delayPressOut || 0,
      }),
      [androidOptimizations],
    );

    return (
      <TouchableOpacity
        key={route.name}
        accessibilityState={isFocused ? { selected: true } : {}}
        accessibilityLabel={options.tabBarAccessibilityLabel}
        testID={options.tabBarButtonTestID}
        onPress={onPress}
        {...touchProps}
      >
        <View style={buttonStyle}>
          <View
            className={` px-4 py-2 justify-center items-center rounded-2xl `}
          >
            <IconsRender title={tab} isFocused={isFocused} />
          </View>
        </View>
      </TouchableOpacity>
    );
  },
);

const TabBar = forwardRef<any, BottomTabBarProps>((props, _ref) => {
  // Performance monitoring hooks
  const { trackTabSwitch } = useTabNavigationPerformance();
  const androidOptimizations = useAndroidTabOptimizations();

  // Memoize container style
  const containerStyle = useMemo(
    () => (Platform.OS === 'ios' ? styles.tabBar : styles.androidTabBar),
    [],
  );

  // Memoize navigation handler with performance tracking
  const handleNavigation = useCallback(
    (routeName: string) => {
      // Track tab switch performance
      trackTabSwitch(routeName);

      // Navigate to the route
      props.navigation.navigate(routeName);
    },
    [props.navigation, trackTabSwitch],
  );

  // Memoize tab buttons
  const tabButtons = useMemo(() => {
    return TAB_ORDER.map((tab) => {
      const routeIndex = props.state.routes.findIndex(
        (route) => route.name === tab,
      );
      if (routeIndex === -1) return null; // Skip if route is not found

      const route = props.state.routes[routeIndex];
      const { options } = props.descriptors[route.key];
      const isFocused = props.state.index === routeIndex;

      return (
        <TabButton
          key={route.name}
          tab={tab}
          route={route}
          options={options}
          isFocused={isFocused}
          onPress={() => handleNavigation(route.name)}
          androidOptimizations={androidOptimizations}
        />
      );
    });
  }, [
    props.state.routes,
    props.state.index,
    props.descriptors,
    handleNavigation,
    androidOptimizations,
  ]);

  return <View style={containerStyle}>{tabButtons}</View>;
});

const styles = StyleSheet.create({
  tabBar: {
    marginHorizontal: 8,
    paddingHorizontal: 12,
    paddingVertical: 8,
    bottom: normalized(14),
    position: 'absolute',
    backgroundColor: COLORS.black,
    borderRadius: 24,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'center',
  },
  androidTabBar: {
    height: normalized(55),
    width: '100%',
    marginHorizontal: normalized(8),
    paddingHorizontal: normalized(15),
    backgroundColor: COLORS.black,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    alignSelf: 'center',
  },
});

// Export memoized TabBar for better performance
export default memo(TabBar);
