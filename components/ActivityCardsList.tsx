import { FlatList, View } from 'react-native';
import React, { memo, useCallback } from 'react';
import ActivityCard from './ActivityCard';
import EmptyList from './ui/EmptyListComponent';
import { normalized, SCREEN_WIDTH } from '@/constants/Theme';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { ViewToken } from 'react-native';
import { IGetCalenderActivitiesResponse } from '@/services/HomeScreenAPI';

interface ICardsList {
  data: IGetCalenderActivitiesResponse;
  isError?: boolean;
  isLoading?: boolean;
  onRefreshPress?: () => void;
}
interface ListItemProps {
  item: any;
  viewableItemsSharedValues: Animated.SharedValue<ViewToken[]>;
}
// Screen and card widthSnap = SCREEN_WIDTH - 10
const cardWidth = SCREEN_WIDTH - 8;

const ActivityCardsList = (props: ICardsList) => {
  const viewableItemsSharedValues = useSharedValue<ViewToken[]>([]);

  const ListItem = memo(
    ({ item, viewableItemsSharedValues }: ListItemProps) => {
      // animate the item when it is visible
      const animatedStyle = useAnimatedStyle(() => {
        const isVisible = Boolean(
          viewableItemsSharedValues.value.find(
            (viewableItem) => viewableItem.item.id === item.id,
          ),
        );
        return {
          opacity: withTiming(isVisible ? 1 : 0),
          transform: [{ scale: withTiming(isVisible ? 1 : 0.6) }],
        };
      });

      return (
        <Animated.View style={[animatedStyle, { width: cardWidth }]}>
          <ActivityCard
            {...item}
            day={formatDate(item.date).day}
            month={formatDate(item.date).month}
          />
        </Animated.View>
      );
    },
  );

  // format date before render
  const formatDate = useCallback((date: Date) => {
    const month = date.toLocaleString('default', { month: 'short' }); // e.g., "May"
    const day = date.getDate(); // e.g., 16
    return { month, day };
  }, []);

  return (
    <FlatList
      style={{
        height:
          props.data.calendar && !props.isError
            ? normalized(290)
            : normalized(150),
      }}
      data={props.data.calendar}
      ListEmptyComponent={
        <EmptyList
          title='No Activities Yet!'
          description='It looks like there are no activities planned right now.'
          isError={props.isError}
          errorTitle='Something Went Wrong'
          errorDescription='It seems something didn’t load properly. Hit the refresh button to fix it!'
          onRefreshPress={props.onRefreshPress}
        />
      }
      keyExtractor={(item) => `Activates-${item._id.toString()}`} // Ensure unique key
      showsHorizontalScrollIndicator={false}
      renderItem={({ item }) => (
        <ListItem
          item={item}
          viewableItemsSharedValues={viewableItemsSharedValues}
        />
      )}
      horizontal={props.data.success && props.data.calendar.length > 0}
      ItemSeparatorComponent={() => <View className='px-2' />}
      initialNumToRender={5} // Render only a few items initially
      snapToAlignment='center'
      snapToInterval={cardWidth}
      removeClippedSubviews={true} // Improve performance by removing off-screen items
      onViewableItemsChanged={({ viewableItems }) => {
        viewableItemsSharedValues.value = viewableItems;
      }}
    />
  );
};

export default memo(ActivityCardsList);
