import { StyleSheet, View, TouchableOpacity } from 'react-native';
import React, { useCallback, useState } from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormWatch,
} from 'react-hook-form';

import CustomText from '../ui/CustomText';
import useGetSocialCircles from '@/hooks/circlesHooks/useGetSocialCircles';
import { IForm } from '@/utils/validations/validateActivitySchema';
import { Dropdown } from 'react-native-element-dropdown';
import { COLORS } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';

interface Props {
  control: Control<IForm>;
  errors: FieldErrors<IForm>;
  watch: UseFormWatch<IForm>;
  userId: string;
}

const ActivitySocialCircleDropDown: React.FC<Props> = ({
  control,
  watch,
  errors,
  userId,
}) => {
  const [search, setSearch] = useState('');

  const { data: circlesData } = useGetSocialCircles(userId);

  const handleSearch = useCallback((e: string) => {
    setSearch(e);
  }, []);

  return (
    <View>
      <Controller
        control={control}
        name='socialCircles'
        render={({ field: { onChange } }) => {
          const socialCircles = watch('socialCircles') || [];

          return (
            <View>
              <Dropdown
                style={styles.dropDownContainer}
                data={
                  circlesData?.data?.socialCircles?.filter((circle) =>
                    circle.name.toLowerCase().includes(search.toLowerCase()),
                  ) || []
                }
                placeholderStyle={{ color: COLORS.neutral[400] }}
                containerStyle={styles.itemsContainer}
                itemTextStyle={styles.textStyles}
                onChange={(item) => {
                  const isSelected = socialCircles.some(
                    (circle) => circle._id === item._id,
                  );

                  if (!isSelected) {
                    onChange([...socialCircles, item]);
                  }
                }}
                labelField='name'
                valueField='_id'
                placeholder='Select social circle'
                search={false}
                searchPlaceholder='Search social circles...'
                onChangeText={handleSearch}
                maxHeight={250}
              />

              {/* Display selected social circles as tags */}
              {socialCircles.length > 0 && (
                <View style={styles.tagsContainer}>
                  {socialCircles.map((circle) => (
                    <View key={circle._id} style={styles.tag}>
                      <CustomText style={styles.tagText}>
                        {circle.name}
                      </CustomText>
                      <TouchableOpacity
                        onPress={() => {
                          const updated = socialCircles.filter(
                            (c) => c._id !== circle._id,
                          );
                          onChange(updated);
                        }}
                      >
                        <Ionicons
                          name='close-circle'
                          size={18}
                          color={COLORS.neutral[300]}
                        />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </View>
          );
        }}
      />

      {errors.socialCircles?.message && (
        <CustomText className='text-danger my-2'>
          {errors.socialCircles.message}
        </CustomText>
      )}
    </View>
  );
};

export default ActivitySocialCircleDropDown;

const styles = StyleSheet.create({
  dropDownContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: COLORS.neutral[300],
    borderRadius: 20,
    padding: 20,
    backdropFilter: 'blur(10px)',
  },
  itemsContainer: {
    backgroundColor: 'rgba(255,255,255,1)',
    borderRadius: 15,
    zIndex: 100,
  },
  textStyles: {
    color: COLORS.neutral[800],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 10,
  },
  tag: {
    backgroundColor: COLORS.primary[50],
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagText: {
    color: COLORS.white[50],
    marginRight: 8,
  },
});
