import { StyleSheet, View } from 'react-native';
import React from 'react';
import { Control, Controller, FieldErrors, useWatch } from 'react-hook-form';

import CustomText from '../ui/CustomText';
import CustomDatePicker from '../CustomDatePicker';
import { COLORS, normalized } from '@/constants/Theme';
import { IForm } from '@/utils/validations/validateActivitySchema';

interface Props {
  control: Control<IForm>;
  errors: FieldErrors<IForm>;
}

const ActivityDateTime: React.FC<Props> = ({ control, errors }) => {
  const start = useWatch({ control, name: 'start.date' });
  const end = useWatch({ control, name: 'end.date' });

  return (
    <View>
      <View className='gap-3'>
        <CustomText style={styles.sectionTitle}>Date & Time</CustomText>

        {/* Start Date & Time */}
        <View className='flex-row items-center gap-2 justify-between'>
          <CustomText className='text-white-50 text-h3 w-[15%]'>
            Start:
          </CustomText>
          <View className='flex-row items-center gap-2 w-[85%]'>
            <View className='flex-1'>
              <Controller
                control={control}
                name='start.date'
                render={({ field: { onChange, value } }) => (
                  <CustomDatePicker onDateChange={onChange} value={value} />
                )}
              />
            </View>
            <View className='flex-1'>
              <Controller
                control={control}
                name='start.time'
                render={({ field: { onChange, value } }) => (
                  <CustomDatePicker
                    mode='time'
                    placeholder='-- --'
                    onDateChange={onChange}
                    value={value}
                  />
                )}
              />
            </View>
          </View>
        </View>

        {/* End Date & Time */}
        <View className='flex-row items-center gap-2 justify-between'>
          <CustomText className='text-white-50 text-h3 w-[15%]'>
            End:
          </CustomText>
          <View className='flex-row items-center gap-2 w-[85%]'>
            <View className='flex-1'>
              <Controller
                control={control}
                name='end.date'
                render={({ field: { onChange, value } }) => (
                  <CustomDatePicker
                    minimumDate={start}
                    onDateChange={onChange}
                    value={value}
                  />
                )}
              />
            </View>
            <View className='flex-1'>
              <Controller
                control={control}
                name='end.time'
                render={({ field: { onChange, value } }) => (
                  <CustomDatePicker
                    mode='time'
                    placeholder='-- --'
                    onDateChange={onChange}
                    value={value}
                  />
                )}
              />
            </View>
          </View>
        </View>
      </View>

      {/* Errors */}
      {errors.start?.date && (
        <CustomText className='text-danger my-2'>
          {errors.start.date.message}
        </CustomText>
      )}
      {errors.end?.date && (
        <CustomText className='text-danger my-2'>
          {errors.end.date.message}
        </CustomText>
      )}
    </View>
  );
};

export default ActivityDateTime;

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: normalized(16),
    color: COLORS.white[50],
  },
});
