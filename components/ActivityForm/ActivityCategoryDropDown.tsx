import { StyleSheet, View, TouchableOpacity } from 'react-native';
import React from 'react';
import {
  Control,
  Controller,
  FieldErrors,
  UseFormWatch,
} from 'react-hook-form';
import { useCategoriesStore } from '@/stores/useCategoriesStore';
import { IForm } from '@/utils/validations/validateActivitySchema';
import CustomText from '../ui/CustomText';

import { Dropdown } from 'react-native-element-dropdown';
import { COLORS } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import CustomTag from '../ui/CustomTag';

interface Props {
  control: Control<IForm>;
  errors: FieldErrors<IForm>;
  watch: UseFormWatch<IForm>;
}

const ActivityCategoryDropDown: React.FC<Props> = ({
  control,
  errors,
  watch,
}) => {
  const { categories } = useCategoriesStore();
  return (
    <View>
      <Controller
        control={control}
        name='category'
        render={({ field: { onChange } }) => {
          const selectedCategories = watch('category') || [];
          return (
            <View>
              <Dropdown
                style={styles.dropDownContainer}
                data={categories}
                placeholderStyle={{ color: COLORS.neutral[400] }}
                containerStyle={styles.itemsContainer}
                itemTextStyle={styles.textStyles}
                onChange={(item) => {
                  // Check if the selected category already exists
                  const isCategorySelected = selectedCategories.some(
                    (category) => category._id === item._id,
                  );

                  if (!isCategorySelected) {
                    // Only add if the category is not already selected
                    const updatedCategories = [...selectedCategories, item];
                    onChange(updatedCategories);
                  }
                }}
                labelField='english'
                valueField='_id'
                placeholder='Select category'
                search={false}
                maxHeight={250}
                searchPlaceholder='Search...'
              />

              {/* Display selected categories as tags */}
              {selectedCategories.length > 0 && (
                <View style={styles.tagsContainer}>
                  {selectedCategories.map((item) => (
                    <View key={item._id} style={styles.tag}>
                      <CustomText style={styles.tagText}>
                        {item.english}
                      </CustomText>
                      <TouchableOpacity
                        onPress={() => {
                          // Handle deletion by filtering out this category
                          const updatedCategories = selectedCategories.filter(
                            (cat) => cat._id !== item._id,
                          );
                          onChange(updatedCategories);
                        }}
                      >
                        <Ionicons
                          name='close-circle'
                          size={18}
                          color={COLORS.neutral[300]}
                        />
                      </TouchableOpacity>
                    </View>
                  ))}
                </View>
              )}
            </View>
          );
        }}
      />
      <CustomText className='text-danger'>
        {errors.category?.message}
      </CustomText>
    </View>
  );
};

export default ActivityCategoryDropDown;

const styles = StyleSheet.create({
  dropDownContainer: {
    backgroundColor: 'rgba(255,255,255,0.1)',
    borderWidth: 1,
    borderColor: COLORS.neutral[300],
    borderRadius: 20,
    padding: 20,
    backdropFilter: 'blur(10px)',
  },
  itemsContainer: {
    backgroundColor: 'rgba(255,255,255,1)',
    borderRadius: 15,

    zIndex: 100,
  },
  textStyles: {
    color: COLORS.neutral[800],
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
    marginTop: 10,
  },
  tag: {
    backgroundColor: COLORS.primary[50],
    borderRadius: 16,
    paddingVertical: 6,
    paddingHorizontal: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  tagText: {
    color: COLORS.white[50],
    marginRight: 8,
  },
});
