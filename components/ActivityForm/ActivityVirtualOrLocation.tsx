import { View, Text, TouchableOpacity, StyleSheet } from 'react-native';
import React, { memo } from 'react';
import CustomText from '../ui/CustomText';
import { Control, Controller, FieldErrors, useWatch } from 'react-hook-form';
import CustomInput from '../ui/CustomInput';
import { Link } from 'expo-router';
import { APP_Icons } from '@/constants/Images';
import { COLORS, normalized } from '@/constants/Theme';
import GooglePlacesInput from '../ui/GooglePlacesInput';
import CustomSwitch from '../ui/CustomSwitch';
import { IForm } from '@/utils/validations/validateActivitySchema';

interface Props {
  control: Control<IForm>;
  errors: FieldErrors<IForm>;
}
const ActivityVirtualOrLocation: React.FC<Props> = ({ control, errors }) => {
  const virtual = useWatch({ control, name: 'virtual' });

  return (
    <View className='gap-3'>
      <View className='flex-row items-center justify-between'>
        <CustomText style={styles.sectionTitle}>
          Make this online activity
        </CustomText>
        <Controller
          control={control}
          name='virtual'
          render={({ field: { value, onChange } }) => (
            <CustomSwitch
              active={value || false}
              onPress={() => onChange(!value)}
            />
          )}
        />
      </View>

      {/* Online link input */}
      {virtual && (
        <View>
          <CustomText style={styles.sectionTitle}>
            Add link to online activity
          </CustomText>
          <CustomInput
            control={control}
            name='link'
            error={errors.link?.message}
            placeholder='Link'
          />
        </View>
      )}

      {/* Location only if not online */}
      {!virtual && (
        <View className='w-100% h-24 gap-2'>
          <View className='flex-row justify-between items-center  '>
            <CustomText style={styles.sectionTitle}>Location</CustomText>
            {/* <Link href='/(protected)/activity/addActivityLocationMap' asChild>
              <TouchableOpacity className='flex-row items-center gap-2'>
                <APP_Icons.LocationIcon
                  width={normalized(14)}
                  height={normalized(14)}
                />
                <CustomText className='text-secondary-300'>
                  Select on Map
                </CustomText>
              </TouchableOpacity>
            </Link> */}
          </View>

          <View className='w-[100%] bg-white-50/10 flex-1 py-2 px-3 rounded-xl absolute  flex-row items-center  mt-10'>
            <View className='flex-1 relative px-2 '>
              <Controller
                name='location'
                control={control}
                render={({ field: { onChange } }) => (
                  <GooglePlacesInput
                    fetchDetails={true}
                    keyboardShouldPersistTaps='always'
                    enablePoweredByContainer={false}
                    placeholder='Enter your location'
                    textInputProps={{
                      placeholderTextColor: COLORS.neutral[100],
                    }}
                    styles={{
                      textInput: {
                        padding: 20,
                        backgroundColor: 'transparent',
                        color: COLORS.neutral[100],
                      },
                      listView: {
                        width: '100%',
                        backgroundColor: COLORS.white[50],
                      },
                    }}
                    onPress={(data, details) => {
                      if (!details) return;
                      onChange({
                        address: data.description,
                        latitude: details.geometry.location.lat,
                        longitude: details.geometry.location.lng,
                      });
                    }}
                    debounce={250}
                  />
                )}
              />
            </View>
          </View>
        </View>
      )}
      {
        <CustomText className='my-2 text-danger'>
          {errors.location?.message}
        </CustomText>
      }
    </View>
  );
};

export default memo(ActivityVirtualOrLocation);

const styles = StyleSheet.create({
  sectionTitle: {
    fontSize: normalized(16),
    color: COLORS.white[50],
  },
});
