import { View, Text } from 'react-native';
import React, { memo } from 'react';
import CustomInput from '../ui/CustomInput';
import { Control, FieldErrors, useWatch } from 'react-hook-form';
import CustomText from '../ui/CustomText';
import { IForm } from '@/utils/validations/validateActivitySchema';

interface Props {
  control: Control<IForm>;
  errors: FieldErrors<IForm>;
  MAX_DESCRIPTION_LETTERS: number;
}

const ActivityDescription: React.FC<Props> = ({
  control,
  errors,
  MAX_DESCRIPTION_LETTERS,
}) => {
  const descriptionValue = useWatch({
    control,
    name: 'description',
  });
  return (
    <View>
      <CustomInput
        control={control}
        name='description'
        error={errors.description?.message}
        multiline
        numberOfLines={5}
        maxLength={MAX_DESCRIPTION_LETTERS}
        smartInsertDelete
        placeholder='Add description'
        otherStyles='!bg-transparent'
        className='h-32 flex-1  text-white-50 rounded-xl px-4 py-2'
        textAlignVertical='top'
      />

      <CustomText className='text-neutral-400 ml-auto mt-2'>
        {descriptionValue.length}/{MAX_DESCRIPTION_LETTERS} letters
      </CustomText>
    </View>
  );
};

export default memo(ActivityDescription);
