import { Pressable, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText';
import { AntDesign } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
interface IActivityComment {
  image: string;
  user: { avatar: string; username: string };
  commentDate?: string;
  rate?: React.ReactNode;
  comment: string;
  liked: boolean;
  onLikePress: () => void;
  onReply: () => void;
}
const ActivityComment = (props: IActivityComment) => {
  return (
    <View className='gap-2'>
      <View className='flex-row items-start gap-2'>
        <Avatar
          source={{ uri: props.user.avatar }}
          text={props.user.username}
        />
        <View>
          <CustomText className='text-white-50 font-medium text-h4 '>
            {props.user.username}
          </CustomText>
          {props.commentDate && (
            <CustomText className='text-neutral-300 text-h6 '>
              {props.commentDate}
            </CustomText>
          )}
          {props.rate && props.rate}
        </View>
      </View>
      <CustomText className='text-neutral-200 text-h4'>
        {props.comment}
      </CustomText>
      <View className='flex-row gap-4 items-center'>
        <TouchableOpacity onPress={props.onLikePress}>
          <AntDesign
            name={props.liked ? 'heart' : 'hearto'}
            size={24}
            color={props.liked ? COLORS.accent[300] : COLORS.neutral[100]}
          />
        </TouchableOpacity>
        <Pressable onPress={props.onReply}>
          <CustomText className='text-neutral-100 text-h5 font-medium'>
            Reply
          </CustomText>
        </Pressable>
      </View>
    </View>
  );
};

export default memo(ActivityComment);
