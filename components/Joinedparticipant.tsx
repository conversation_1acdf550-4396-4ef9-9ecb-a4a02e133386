import { Pressable, View } from 'react-native';
import React, { memo } from 'react';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText'; // Ensure this component exists
import { normalized } from '@/constants/Theme';
import { IActivityItem } from '@/types';

interface JoinedParticipantProps {
  participants: IActivityItem['participant'];
  maxVisible?: number;
  avatarSize?: number;
}

const JoinedParticipant: React.FC<JoinedParticipantProps> = ({
  participants,
  maxVisible = 7,
  avatarSize = normalized(30),
}) => {
  return (
    <Pressable className='flex-row '>
      {participants.participants?.slice(0, maxVisible).map((p, index) => (
        <View
          key={`participants-${p._id}-${index}`}
          className={`${index !== 0 ? '-mx-2' : ''}`}
        >
          <Avatar
            source={{ uri: p.image }}
            text={p.displayName}
            size={avatarSize}
          />
        </View>
      ))}
      {participants?.participants.length > maxVisible && (
        <View
          className='rounded-full bg-secondary-300 justify-center items-center '
          style={{ width: avatarSize, height: avatarSize }}
        >
          <CustomText className='text-white-50'>
            +{participants.participants?.length - participants.total || 0}
          </CustomText>
        </View>
      )}
    </Pressable>
  );
};

export default memo(JoinedParticipant);
