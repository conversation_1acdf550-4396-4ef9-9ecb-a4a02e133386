import { Text, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import FontAwesome5 from '@expo/vector-icons/FontAwesome5';
import { COLORS, normalized } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import { Link } from 'expo-router';
/**
    hint to remember normalized function to render spaces and sizes based on pixel density and screen size (responsive)
 */

const TapToFindActivitiesNearBy = () => {
  return (
    <Link href='/(protected)/maps/homeScreenMap' asChild>
      <TouchableOpacity className='bg-primary-50/20 mb-5 flex-row  items-center justify-between  px-2 mt-5 py-3 rounded-3xl '>
        <CustomText className={`text-white-50 text-h6 max-w-[70%]`}>
          Tap the map to find activities and events nearby
        </CustomText>
        <View className='flex-row items-center justify-center gap-2 bg-tertiary/20 p-3 rounded-3xl  bg-secondary-300/20'>
          <FontAwesome5
            name='map-marked-alt'
            size={normalized(20)}
            color={COLORS.secondary[300]}
          />
          <CustomText className='text-secondary-300 font-poppins text-body1'>
            Map
          </CustomText>
        </View>
      </TouchableOpacity>
    </Link>
  );
};

export default memo(TapToFindActivitiesNearBy);
