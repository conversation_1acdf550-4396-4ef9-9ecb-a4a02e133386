import React, { memo, useEffect, ReactNode } from 'react';
import { Platform } from 'react-native';
import { useTabScreenOptimization } from '@/hooks/useTabNavigationPerformance';

interface TabScreenWrapperProps {
  children: ReactNode;
  screenName: string;
  enablePerformanceMonitoring?: boolean;
}

/**
 * Wrapper component for tab screens to add performance monitoring
 * and Android-specific optimizations
 */
const TabScreenWrapper = memo(({ 
  children, 
  screenName, 
  enablePerformanceMonitoring = true 
}: TabScreenWrapperProps) => {
  const { markScreenReady, isFirstRender } = useTabScreenOptimization(screenName);

  useEffect(() => {
    if (enablePerformanceMonitoring) {
      // Mark screen as ready after initial render
      const timer = setTimeout(() => {
        markScreenReady();
      }, 100); // Small delay to ensure screen is fully rendered

      return () => clearTimeout(timer);
    }
  }, [markScreenReady, enablePerformanceMonitoring]);

  // Log first render for debugging
  useEffect(() => {
    if (isFirstRender && __DEV__) {
      console.log(`📱 Tab screen "${screenName}" first render on ${Platform.OS}`);
    }
  }, [isFirstRender, screenName]);

  return <>{children}</>;
});

TabScreenWrapper.displayName = 'TabScreenWrapper';

export default TabScreenWrapper;
