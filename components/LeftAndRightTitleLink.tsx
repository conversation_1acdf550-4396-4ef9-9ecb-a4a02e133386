import { Platform, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import { LinkProps, router } from 'expo-router';
import CustomText from './ui/CustomText';

interface LeftAndRightTitleLink {
  title?: string;
  linkTitle?: string;
  linkHref: LinkProps['href'];
}
const LeftAndRightTitleLink = (props: LeftAndRightTitleLink) => {
  return (
    <View className='flex-row items-center justify-between'>
      <CustomText
        className='text-white-50 font-bold'
        style={{ fontSize: Platform.OS === 'ios' ? 18 : 14 }}
      >
        {props.title}
      </CustomText>
      {props.linkHref && (
        <TouchableOpacity onPress={() => router.push(props.linkHref)}>
          <CustomText className='text-secondary-300 font-semibold text-h6'>
            {props.linkTitle}
          </CustomText>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default memo(LeftAndRightTitleLink);
