import { View } from 'react-native';
import React, { memo } from 'react';
import { Image } from 'expo-image';
import CustomText from './ui/CustomText';

interface ExploreAttractionCardProps {
  image: string;
  title: string;
}
const ExploreAttractionCard = (props: ExploreAttractionCardProps) => {
  return (
    <View className='flex-1 h-[202px] min-w-[150px]  relative'>
      <Image
        source={{ uri: props.image }}
        style={{
          width: '100%',
          height: '100%',
          objectFit: 'cover',
          borderRadius: 10,
          backgroundColor: 'rgba(0,0,0,0.5)',
        }}
      />
      <View className='absolute bg-black/20 w-full h-full   rounded-xl' />
      <CustomText
        className='text-white-50 text-h4 font-bold absolute bottom-0 left-0 right-0 p-2'
        numberOfLines={1}
      >
        {props.title}
      </CustomText>
    </View>
  );
};

export default memo(ExploreAttractionCard);
