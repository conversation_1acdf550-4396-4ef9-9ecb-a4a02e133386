import React, { memo } from 'react';
import { StyleSheet, View, Platform } from 'react-native';
import { Marker } from 'react-native-maps';
import { Image } from 'expo-image';
import CustomText from '../ui/CustomText';
import { isValidCoordinate } from '@/utils/mapClusterUtils';
import { router } from 'expo-router';

interface ActivityMarkerProps {
  id: string;
  latitude: number;
  longitude: number;
  name: string;
  image: string;
  onPress?: (id: string) => void;
}

// Constants for marker styling to match Figma design
const MARKER_SIZE = Platform.OS === 'android' ? 58 : 60; // Slightly smaller on Android for consistency
const ARROW_SIZE = Platform.OS === 'android' ? 9 : 10;
const PIN_COLOR = '#7B2CBF'; // Purple color from design

const ActivityMarker = memo(
  ({ id, latitude, longitude, name, image, onPress }: ActivityMarkerProps) => {
    // Validate coordinates
    if (!isValidCoordinate(latitude, longitude)) {
      console.warn(
        `❌ Invalid coordinates for activity ${id}: lat=${latitude}, lng=${longitude}`,
      );
      return null;
    }

    const handlePress = () => {
      onPress?.(id);
      router.push(`/(protected)/activity/${id}`);
    };

    return (
      <Marker
        key={`activity-${id}`}
        onPress={handlePress}
        coordinate={{ latitude, longitude }}
        tracksViewChanges={false}
        anchor={{ x: 0.5, y: 1 }} // Anchor at bottom center for pin effect
        zIndex={100}
      >
        <View style={styles.markerContainer}>
          {/* Pin-style marker with image */}
          <View style={styles.pinContainer}>
            {/* Main circular image container */}
            <View style={styles.markerCircle}>
              {image ? (
                <Image
                  source={{ uri: image }}
                  style={styles.markerImage}
                  contentFit='cover'
                  transition={200}
                />
              ) : (
                <View style={styles.placeholderImage}>
                  <CustomText style={styles.placeholderText}>📍</CustomText>
                </View>
              )}
            </View>
            {/* Pin pointer */}
            <View style={styles.pinPointer} />
          </View>

          {/* Activity name callout bubble */}
          {name && (
            <View style={styles.calloutBubble}>
              <CustomText style={styles.calloutText} numberOfLines={2}>
                {name}
              </CustomText>
              {/* Callout arrow pointing to pin */}
              <View style={styles.calloutArrow} />
            </View>
          )}
        </View>
      </Marker>
    );
  },
);

ActivityMarker.displayName = 'ActivityMarker';

export default ActivityMarker;

// Platform-specific shadow styles for consistent appearance
const shadowStyle = Platform.select({
  ios: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 6,
  },
  android: {
    elevation: 8,
  },
});

const calloutShadowStyle = Platform.select({
  ios: {
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.25,
    shadowRadius: 5,
  },
  android: {
    elevation: 10,
  },
});

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'flex-end',
    position: 'relative',
  },
  pinContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerCircle: {
    width: MARKER_SIZE,
    height: MARKER_SIZE,
    borderRadius: MARKER_SIZE / 2,
    backgroundColor: PIN_COLOR,
    borderWidth: 4,
    borderColor: 'white',
    overflow: 'hidden',
    justifyContent: 'center',
    alignItems: 'center',
    ...shadowStyle,
  },
  markerImage: {
    width: '100%',
    height: '100%',
    borderRadius: (MARKER_SIZE - 8) / 2, // Account for border
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: PIN_COLOR,
  },
  placeholderText: {
    fontSize: 24,
    color: 'white',
    fontWeight: 'bold',
  },
  pinPointer: {
    width: 0,
    height: 0,
    borderLeftWidth: ARROW_SIZE,
    borderRightWidth: ARROW_SIZE,
    borderTopWidth: ARROW_SIZE * 1.2,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: PIN_COLOR,
    marginTop: -2,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.2,
        shadowRadius: 3,
      },
      android: {
        elevation: 5,
      },
    }),
  },
  calloutBubble: {
    position: 'absolute',
    bottom: MARKER_SIZE + 20, // Position above the pin
    backgroundColor: 'white',
    borderRadius: 12,
    paddingHorizontal: 12,
    paddingVertical: 8,
    minWidth: 120,
    maxWidth: 180,
    alignItems: 'center',
    // Add border for Android consistency
    ...Platform.select({
      android: {
        borderWidth: 1,
        borderColor: 'rgba(0, 0, 0, 0.1)',
      },
    }),
    ...calloutShadowStyle,
  },
  calloutText: {
    fontSize: 13,
    fontWeight: '600',
    color: '#333',
    textAlign: 'center',
    lineHeight: 16,
  },
  calloutArrow: {
    position: 'absolute',
    bottom: -6,
    width: 0,
    height: 0,
    borderLeftWidth: 8,
    borderRightWidth: 8,
    borderTopWidth: 8,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'white',
  },
});
