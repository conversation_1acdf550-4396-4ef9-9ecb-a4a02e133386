import React, { memo } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Marker, Callout } from 'react-native-maps';
import { Image } from 'expo-image';
import CustomText from '../ui/CustomText';
import { COLORS } from '@/constants/Theme';
import { isValidCoordinate } from '@/utils/mapClusterUtils';

interface ActivityMarkerProps {
  id: string;
  latitude: number;
  longitude: number;
  name: string;
  image: string;
  onPress?: (id: string) => void;
}

// Constants for marker styling
const MARKER_SIZE = 55;
const ARROW_SIZE = 8;

const ActivityMarker = memo(
  ({ id, latitude, longitude, name, image, onPress }: ActivityMarkerProps) => {
    console.log(`🔍 ActivityMarker rendering: id=${id}, lat=${latitude}, lng=${longitude}`);

    // Validate coordinates
    if (!isValidCoordinate(latitude, longitude)) {
      console.warn(
        `❌ Invalid coordinates for activity ${id}: lat=${latitude}, lng=${longitude}`,
      );
      return null;
    }

    console.log(`✅ ActivityMarker valid coordinates: ${name}`);

    const handlePress = () => {
      onPress?.(id);
    };

    return (
      <Marker
        key={`activity-${id}`}
        onPress={handlePress}
        coordinate={{ latitude, longitude }}
        tracksViewChanges={false}
        anchor={{ x: 0.5, y: 1 }} // Anchor at bottom center for pin effect
        zIndex={100}
      >
        <TouchableOpacity
          style={styles.markerContainer}
          onPress={handlePress}
          activeOpacity={0.8}
        >
          {/* Main marker circle */}
          <View style={styles.markerCircle}>
            {image ? (
              <Image
                source={{ uri: image }}
                style={styles.markerImage}
                contentFit='cover'
                transition={200}
              />
            ) : (
              <View style={styles.placeholderImage}>
                <CustomText style={styles.placeholderText}>📍</CustomText>
              </View>
            )}
          </View>

          {/* Pointer arrow */}
          <View style={styles.markerArrow} />

          {/* Shadow */}
          <View style={styles.markerShadow} />
        </TouchableOpacity>

        {/* Callout for activity name */}
        {name && (
          <Callout tooltip>
            <View style={styles.calloutContainer}>
              <CustomText style={styles.calloutText} numberOfLines={2}>
                {name}
              </CustomText>
            </View>
          </Callout>
        )}
      </Marker>
    );
  },
);

ActivityMarker.displayName = 'ActivityMarker';

export default ActivityMarker;

const styles = StyleSheet.create({
  markerContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  markerCircle: {
    width: MARKER_SIZE,
    height: MARKER_SIZE,
    borderRadius: MARKER_SIZE / 2,
    backgroundColor: COLORS.primary[200],
    borderWidth: 3,
    borderColor: 'white',
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 3 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    elevation: 8,
    justifyContent: 'center',
    alignItems: 'center',
  },
  markerImage: {
    width: '100%',
    height: '100%',
    borderRadius: (MARKER_SIZE - 6) / 2, // Account for border
  },
  placeholderImage: {
    width: '100%',
    height: '100%',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.primary[50],
  },
  placeholderText: {
    fontSize: 20,
    color: COLORS.primary[200],
  },
  markerArrow: {
    width: 0,
    height: 0,
    borderLeftWidth: ARROW_SIZE,
    borderRightWidth: ARROW_SIZE,
    borderTopWidth: ARROW_SIZE * 1.5,
    borderLeftColor: 'transparent',
    borderRightColor: 'transparent',
    borderTopColor: 'white',
    marginTop: -2,
  },
  markerShadow: {
    position: 'absolute',
    bottom: -5,
    width: MARKER_SIZE * 0.6,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(0, 0, 0, 0.2)',
    zIndex: -1,
  },
  calloutContainer: {
    backgroundColor: 'white',
    borderRadius: 8,
    padding: 10,
    maxWidth: 150,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 4,
    elevation: 5,
  },
  calloutText: {
    fontSize: 14,
    fontWeight: '600',
    color: COLORS.neutral[800],
    textAlign: 'center',
  },
});
