import React, { memo } from 'react';
import { StyleSheet, View, TouchableOpacity } from 'react-native';
import { Marker } from 'react-native-maps';
import { Image } from 'expo-image';
import CustomText from '../ui/CustomText';
import { COLORS } from '@/constants/Theme';
import {
  getClusterSize,
  getClusterColor,
  extractCoordinates,
  getClusterImages,
} from '@/utils/mapClusterUtils';
import { router } from 'expo-router';

// Types - Use flexible typing to work with clusterer library
interface ClusterMarkerProps {
  point: any; // Accept the actual clusterer point type
  index: number;
  mapRef: React.RefObject<any>;
}

const ClusterMarker = memo(({ point, index, mapRef }: ClusterMarkerProps) => {
  const pointCount = point.properties.point_count || 0;

  console.log(
    `🔍 ClusterMarker rendering: index=${index}, pointCount=${pointCount}`,
  );

  // Extract coordinates safely
  const coordinates = extractCoordinates(point);
  if (!coordinates) {
    console.warn(
      `❌ Invalid coordinates for cluster ${index}:`,
      point.geometry?.coordinates,
    );
    return null;
  }

  const { lat, lng } = coordinates;

  // Get cluster styling based on count
  const clusterSize = getClusterSize(pointCount);
  const clusterColor = getClusterColor(pointCount);

  // Get images for cluster preview
  const clusterImages = getClusterImages(point, 4);
  const hasImages = clusterImages.length > 0;

  const handleClusterPress = () => {
    try {
      console.log('📍 Cluster pressed, expanding...');
      const region = point.properties.getExpansionRegion();
      mapRef.current?.animateToRegion(region, 500);
    } catch (error) {
      console.error('📍 Error expanding cluster:', error);
    }
  };

  return (
    <Marker
      key={`cluster-${point.id || index}`}
      onPress={handleClusterPress}
      coordinate={{ latitude: lat, longitude: lng }}
      tracksViewChanges={false}
      anchor={{ x: 0.5, y: 0.5 }}
      zIndex={1000}
    >
      <TouchableOpacity
        style={[
          styles.clusterContainer,
          {
            width: clusterSize.size,
            height: clusterSize.size,
            borderRadius: clusterSize.size / 2,
            backgroundColor: clusterColor,
            borderWidth: clusterSize.borderWidth,
          },
        ]}
        onPress={handleClusterPress}
        activeOpacity={0.8}
      >
        {/* Background images preview */}
        <View style={styles.imagesContainer}>
          <Image
            source={{ uri: point?.[0]?.properties?.images?.[0] }}
            style={[styles.clusterImage]}
            contentFit='cover'
            transition={200}
          />
        </View>

        {/* Overlay with count */}
        <View
          style={[
            styles.countOverlay,
            {
              backgroundColor: hasImages
                ? 'rgba(0, 0, 0, 0.8)'
                : 'rgba(255, 255, 255, 0.9)',
            },
          ]}
        >
          <CustomText
            style={[
              styles.countText,
              {
                fontSize: clusterSize.textSize,
                color: hasImages ? 'white' : clusterColor,
                fontWeight: 'bold',
              },
            ]}
          >
            {pointCount}
          </CustomText>
        </View>
      </TouchableOpacity>
    </Marker>
  );
});

ClusterMarker.displayName = 'ClusterMarker';

export default ClusterMarker;

const styles = StyleSheet.create({
  clusterContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.4,
    shadowRadius: 6,
    elevation: 10,
  },
  imagesContainer: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height: '100%',
    overflow: 'hidden',
  },
  clusterImage: {
    position: 'absolute',
    borderWidth: 2,
    borderColor: 'white',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
    elevation: 5,
  },
  countOverlay: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    paddingHorizontal: 6,
    paddingVertical: 2,
    minWidth: 24,
    minHeight: 18,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.3,
    shadowRadius: 2,
    elevation: 3,
  },
  countText: {
    textAlign: 'center',
    fontWeight: '700',
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 0.5, height: 0.5 },
    textShadowRadius: 1,
  },
  pulseRing: {
    position: 'absolute',
    borderWidth: 2,
    backgroundColor: 'transparent',
    opacity: 0.4,
    zIndex: -1,
  },
});
