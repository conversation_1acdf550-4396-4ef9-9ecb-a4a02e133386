import React, { useMemo } from 'react';
import ActivityMarker from './ActivityMarker';

// Use the actual types from the clusterer library
interface ClustersListProps {
  points: any[]; // Accept the actual clusterer types
  mapRef: React.RefObject<any>;
  onIndividualMarkerPress?: (id: string) => void;
}

// Individual markers list component for Figma design
const ClustersList = ({
  points,
  mapRef,
  onIndividualMarkerPress,
}: ClustersListProps) => {
  const renderedMarkers = useMemo(() => {
    if (!points || points.length === 0) {
      console.log('📍 No points to render');
      return null;
    }

    console.log(`📍 Rendering ${points.length} individual markers`);

    return points
      .map((point, index) => {
        console.log(point.properties.images?.[0]);
        try {
          // Since we're not clustering, all points are individual activities
          const coords = point.geometry.coordinates;
          const lat = coords[1];
          const lng = coords[0];

          // Validate coordinates
          if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
            console.warn(`📍 Invalid coordinates for point ${index}:`, coords);
            return null;
          }

          // Render individual activity marker
          return (
            <ActivityMarker
              key={`activity-${point.properties._id || index}`}
              id={point.properties._id || `activity-${index}`}
              latitude={lat}
              longitude={lng}
              name={point.properties.name || 'Activity'}
              image={point.properties.images?.[0] || ''}
              onPress={onIndividualMarkerPress}
            />
          );
        } catch (error) {
          console.error(`📍 Error rendering point ${index}:`, error);
          return null;
        }
      })
      .filter(Boolean); // Remove null entries
  }, [points, mapRef, onIndividualMarkerPress]);

  return <>{renderedMarkers}</>;
};

export default ClustersList;
