import { StyleSheet, View } from 'react-native';
import React, { memo, useMemo } from 'react';
import { isPointCluster } from 'react-native-clusterer';
import { Callout, Marker } from 'react-native-maps';
import CustomText from '../ui/CustomText';
import { Image } from 'expo-image';

// Types
interface ClusterPoint {
  id?: string;
  type: 'Feature';
  geometry: {
    type: 'Point';
    coordinates: [number, number];
  };
  properties: {
    point_count?: number;
    getExpansionRegion: () => any;
    _id?: string;
    name?: string;
    images?: string[];
    [key: string]: any;
  };
}

interface ClustersListProps {
  points: ClusterPoint[];
  mapRef: React.RefObject<any>;
  onIndividualMarkerPress?: (id: string) => void;
}

// Individual marker component
const IndividualMarker = memo(
  ({
    id,
    latitude,
    longitude,
    name,
    image,
    onPress,
  }: {
    id: string;
    latitude: number;
    longitude: number;
    name: string;
    image: string;
    onPress?: (id: string) => void;
  }) => {
    return (
      <Marker
        key={`point-${id}`}
        onPress={() => onPress?.(id)}
        coordinate={{ latitude, longitude }}
        tracksViewChanges={false}
        anchor={{ x: 0.5, y: 0.5 }}
      >
        <View>
          <View>
            {/* You can add image here if needed */}
            <Image source={{ uri: image }} style={{ width: 50, height: 50 }} />
          </View>
          <View />
        </View>
      </Marker>
    );
  },
);

// Cluster marker component
const ClusterMarker = memo(
  ({
    point,
    index,
    mapRef,
  }: {
    point: ClusterPoint;
    index: number;
    mapRef: React.RefObject<any>;
  }) => {
    const pointCount = point.properties.point_count || 0;
    const coords = point.geometry.coordinates;
    const lat = coords[1];
    const lng = coords[0];

    // Get the first image from the cluster if available
    const clusterImage = point.properties.images?.[0] || '';

    const handleClusterPress = () => {
      try {
        console.log('📍 Cluster pressed, expanding...');
        const region = point.properties.getExpansionRegion();
        mapRef.current?.animateToRegion(region, 500);
      } catch (error) {
        console.error('📍 Error expanding cluster:', error);
      }
    };

    return (
      <Marker
        key={`cluster-${point.id || index}`}
        onPress={handleClusterPress}
        coordinate={{ latitude: lat, longitude: lng }}
        tracksViewChanges={false}
        anchor={{ x: 0.5, y: 0.5 }}
        zIndex={1000}
      >
        <View>
          {/* Cluster icon/image */}
          <View>
            <Image source={{ uri: clusterImage }} style={styles.image} />
          </View>

          {/* Count badge */}
          <View>
            <CustomText>{pointCount}</CustomText>
          </View>
        </View>
      </Marker>
    );
  },
);

// Main clusters list component
const ClustersList = ({
  points,
  mapRef,
  onIndividualMarkerPress,
}: ClustersListProps) => {
  const renderedMarkers = useMemo(() => {
    // console.log(`📍 Rendering ${points.length} points/clusters`);

    if (!points || points.length === 0) {
      console.log('📍 No points to render');
      return null;
    }

    return points
      .map((point, index) => {
        try {
          const isCluster = isPointCluster(point);
          const coords = point.geometry.coordinates;
          const lat = coords[1];
          const lng = coords[0];

          // console.log(
          //   `📍 Point ${index}: isCluster=${isCluster}, lat=${lat}, lng=${lng}`,
          // );

          // Validate coordinates
          if (!lat || !lng || isNaN(lat) || isNaN(lng)) {
            // console.warn(`📍 Invalid coordinates for point ${index}:`, coords);
            return null;
          }

          if (isCluster) {
            return (
              <ClusterMarker
                key={`cluster-${point.id || index}`}
                point={point}
                index={index}
                mapRef={mapRef}
              />
            );
          } else {
            // Individual activity marker
            // console.log(
            //   `📍 Rendering individual marker: ${point.properties.name}`,
            // );
            return (
              <IndividualMarker
                key={`point-${point.properties._id || index}`}
                id={point.properties._id || `activity-${index}`}
                latitude={lat}
                longitude={lng}
                name={point.properties.name || 'Activity'}
                image={point.properties.images?.[0] || ''}
                onPress={onIndividualMarkerPress}
              />
            );
          }
        } catch (error) {
          console.error(`📍 Error rendering point ${index}:`, error);
          return null;
        }
      })
      .filter(Boolean); // Remove null entries
  }, [points, mapRef, onIndividualMarkerPress]);

  return <>{renderedMarkers}</>;
};

export default ClustersList;

const styles = StyleSheet.create({
  image: {
    width: 300,
    height: 300,
    borderRadius: 100,
  },
});
