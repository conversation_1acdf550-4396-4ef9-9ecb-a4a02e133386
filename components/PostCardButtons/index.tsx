import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React from 'react';
import { AntDesign, Entypo, Feather, Ionicons } from '@expo/vector-icons';
import CustomDropDownMenu from '../ui/CustomDropDownMenu';
import CustomText from '../ui/CustomText';
import { COLORS } from '@/constants/Theme';
const _iconsSize = 20;

const DropDownMenu: DropDownItem[] = [
  { key: 'report', title: 'Report Post' },
  { key: 'delete', title: 'Delete Post' },
];

// TODO make the functions inside this component and only pass the id required for the logic making this component handle his own state

interface DropDownItem {
  key: string;
  title: string;
}

const index = () => {
  const onLikePress = () => {};
  const onCommentPress = () => {};
  const onSharePress = () => {};
  const onSelect = (e: string) => {};
  const likesCount = 0;
  const commentsCount = 0;
  const isLiked = false;
  const isPostOwner = false;

  return (
    <View>
      <View className='flex-row justify-between items-center p-3'>
        <View className='flex-row items-center gap-x-4'>
          {/* Likes */}
          <TouchableOpacity
            onPress={onLikePress}
            className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
          >
            {likesCount !== undefined && (
              <CustomText style={styles.circleCounters}>
                {likesCount}
              </CustomText>
            )}
            <AntDesign
              name={isLiked ? 'heart' : 'hearto'}
              size={_iconsSize}
              color={isLiked ? COLORS.danger : COLORS.white[50]}
            />
          </TouchableOpacity>

          {/* Comments */}
          <TouchableOpacity
            onPress={onCommentPress}
            className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
          >
            {commentsCount !== undefined && (
              <CustomText style={styles.circleCounters}>
                {commentsCount}
              </CustomText>
            )}
            <Feather
              name='message-circle'
              size={_iconsSize}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>

          {/* Share */}
          <TouchableOpacity
            className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'
            onPress={onSharePress}
          >
            <Ionicons
              name='paper-plane-outline'
              size={_iconsSize}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>
        </View>

        {/* Dropdown menu */}
        <CustomDropDownMenu
          items={
            !isPostOwner
              ? DropDownMenu.filter((v) => v.key !== 'delete')
              : DropDownMenu
          }
          onSelect={(e: string) => onSelect && onSelect(e)}
          triggerElement={
            <TouchableOpacity className='flex-row gap-x-2 items-center bg-primary-50/50 p-2 rounded-lg'>
              <Entypo
                name='dots-three-vertical'
                size={_iconsSize / 1.5}
                color={COLORS.white[50]}
              />
            </TouchableOpacity>
          }
        />
      </View>
    </View>
  );
};

export default index;

const styles = StyleSheet.create({
  circleCounters: {
    color: COLORS.white[50],
    fontWeight: '500',
  },
});
