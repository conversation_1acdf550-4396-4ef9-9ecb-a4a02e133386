import { View, TextStyle, TouchableOpacity } from 'react-native';
import React, { useState } from 'react';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText';
import { COLORS, normalized } from '@/constants/Theme';
import { Ionicons } from '@expo/vector-icons';
import CommentInput, { commentInputProps } from './ui/CommentInput';

interface ICommentComponentTypes {
  avatar?: string;
  username: string;
  Date: string;
  description: string;
  userTextStyles?: TextStyle;
  dateStyles?: TextStyle;
  commentTextStyles?: TextStyle;
  imageSize?: number;
  commentInputProps: commentInputProps;
  inputIcon?: React.ReactNode;
}

const PostComment = (props: ICommentComponentTypes) => {
  const [showCommentInput, setShowCommentInput] = useState<boolean>(false);

  const toggleCommentInput = () => setShowCommentInput((prev) => !prev);
  return (
    <View className='px-3 py-2 gap-3'>
      {/* Avatar */}
      <View className='flex-row gap-3'>
        <Avatar
          source={{ uri: props.avatar || '' }}
          text={props.username}
          size={normalized(props.imageSize || 35)}
        />
        <View>
          <CustomText
            className='text-[#0B0B0B] font-semibold text-h2 -mb-1'
            style={props.userTextStyles}
          >
            {props.username}
          </CustomText>

          <CustomText className='text-neutral-900'
style={props.dateStyles}>
            {props.Date}
          </CustomText>
        </View>
      </View>
      <CustomText
        className='text-h4 font-light text-[#0B0B0B] leading-8'
        style={props.commentTextStyles}
      >
        {props.description}
      </CustomText>

      <View className='flex-row items-center gap-5'>
        <TouchableOpacity>
          {props.inputIcon ? (
            props.inputIcon
          ) : (
            <Ionicons name='heart-outline'
color={COLORS.white[50]}
size={35} />
          )}
        </TouchableOpacity>
        <TouchableOpacity onPress={toggleCommentInput}>
          <CustomText className='text-secondary-300 text-h4'>replay</CustomText>
        </TouchableOpacity>
      </View>

      {showCommentInput && <CommentInput {...props.commentInputProps} />}
    </View>
  );
};

export default PostComment;
