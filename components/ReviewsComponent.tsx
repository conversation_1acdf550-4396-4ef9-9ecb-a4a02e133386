import { TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import Avatar from './ui/Avatar';
import { normalized } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import { useTranslation } from 'react-i18next';

interface IReviewsComponentTypes {
  avatar?: string;
  username: string;
  rate?: number; // e.g., 4.2, 4.3, etc.
  description: string;
  createdAt?: string;
  isCreator?: boolean;
  onDelete?: () => void;
}

const ReviewsComponent = (props: IReviewsComponentTypes) => {
  // const { rate } = props;

  // // Function to render stars based on the rating
  // const renderStars = useCallback(() => {
  //   const stars = [];
  //   const fullStars = Math.floor(rate); // Number of fully filled stars
  //   const partialStar = rate - fullStars; // Decimal part for the partially filled star

  //   // Render full stars
  //   for (let i = 0; i < fullStars; i++) {
  //     stars.push(
  //       <FontAwesome
  //         key={`full-${i}`}
  //         name="star"
  //         size={24}
  //         color="#FBBC05" // Yellow for filled stars
  //       />
  //     );
  //   }

  //   // Render partially filled star (if applicable)
  //   if (partialStar > 0) {
  //     stars.push(
  //       <View key="partial-star" style={{ position: "relative" }}>
  //         <FontAwesome
  //           name="star"
  //           size={24}
  //           color="#969696" // Gray for the empty part
  //         />
  //         <View
  //           style={{
  //             position: "absolute",
  //             width: `${partialStar * 100}%`, // Calculate the width of the filled part
  //             overflow: "hidden",
  //           }}
  //         >
  //           <FontAwesome
  //             name="star"
  //             size={24}
  //             color="#FBBC05" // Yellow for the filled part
  //           />
  //         </View>
  //       </View>
  //     );
  //   }

  //   // Render remaining empty stars
  //   const remainingStars = 5 - Math.ceil(rate);
  //   for (let i = 0; i < remainingStars; i++) {
  //     stars.push(
  //       <FontAwesome
  //         key={`empty-${i}`}
  //         name="star"
  //         size={24}
  //         color="#969696" // Gray for empty stars
  //       />
  //     );
  //   }

  //   return stars;
  // }, [rate]);
  const { t } = useTranslation();

  return (
    <View className='gap-1.5'>
      <View className='flex-row gap-3 items-center'>
        <Avatar size={normalized(42)}
source={{ uri: props.avatar }} />
        {/* Username and rate */}
        <View className=''>
          <CustomText className='text-white-50 font-semibold text-h4'>
            {props.username}
          </CustomText>
          {/* <View className="flex-row gap-0.5">{renderStars()}</View> */}
          <CustomText className='text-neutral-200'>
            {props.createdAt}
          </CustomText>
        </View>
      </View>

      <CustomText className='text-neutral-200 text-h2 px-2  py-5'>
        {props.description}
      </CustomText>

      {props.isCreator && (
        <TouchableOpacity>
          <CustomText className='text-neutral-300'>{t('delete')}</CustomText>
        </TouchableOpacity>
      )}
    </View>
  );
};

export default memo(ReviewsComponent);
