import { View, Text } from 'react-native';
import React, { memo } from 'react';
import { COLORS } from '@/constants/Theme';
import { FontAwesome5, Ionicons } from '@expo/vector-icons';
import { PasswordErrors } from '@/hooks/AuthHooks/useNewPassword';

interface IPasswordValidationListProps {
  passwordErrors: PasswordErrors;
}

const PasswordValidationList = ({
  passwordErrors,
}: IPasswordValidationListProps) => {
  return (
    <View className='gap-2'>
      {Object.entries(passwordErrors).map(([key, { isError, message }]) => (
        <View key={key}
className='flex-row items-center gap-3'>
          {isError ? (
            <Ionicons
              name='close-circle-sharp'
              size={20}
              color={COLORS.danger}
            />
          ) : (
            <FontAwesome5
              name='check-circle'
              size={20}
              color={COLORS.success[400]}
            />
          )}
          <Text className='text-white-50 text-body1'>{message}</Text>
        </View>
      ))}
    </View>
  );
};

export default memo(PasswordValidationList);
