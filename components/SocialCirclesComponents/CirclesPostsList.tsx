import { Pressable, View } from 'react-native';
import { memo, useEffect, useState, useCallback, useMemo } from 'react';
import { router } from 'expo-router';

import EmptyListComponent from '../ui/EmptyListComponent';
import { APP_Icons } from '@/constants/Images';
import { normalized } from '@/constants/Theme';
import { IPost } from '@/types';
import { timeAgo } from '@/utils/formatDates';
import CardSkelton from '../Skilltons/CardSkelton';
import { useSession } from '@/context/AuthContext';
import { useDeletePost } from '@/hooks/postsHooks/useDeletePost';
import useTogglePostsLikes from '@/hooks/postsHooks/useTogglePostsLikes';
import Loading from '@/layouts/Loading';
import PostsCard from '../PostsCard';
import OptimizedFlatList from '../ui/OptimizedFlatList';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

// Memoized empty component to prevent unnecessary re-renders
const EmptyComponent = memo(() => (
  <EmptyListComponent
    title='No Posts Yet'
    description='Share your first post and spark a conversation!'
    emptyIcon={() => (
      <APP_Icons.EmptyFileIcon width={normalized(40)} height={normalized(40)} />
    )}
  />
));
type IRenderItem = {
  item: IPost;
  handleReportPost: (e: string) => void;
  isPostOwner: boolean;
  handleToggleLike: () => void;
  isLiked: boolean;
};

// Optimized RenderItem with memoized navigation and better performance
const RenderItem = memo((props: IRenderItem) => {
  // Memoize navigation params to prevent object recreation
  const navigationParams = useMemo(() => ({
    id: props.item?._id,
    circleId: props.item?.socialCircle?._id,
    circleImage: props.item?.socialCircle?.image,
    circleName: props.item?.socialCircle?.name,
  }), [
    props.item?._id,
    props.item?.socialCircle?._id,
    props.item?.socialCircle?.image,
    props.item?.socialCircle?.name,
  ]);

  // Memoize navigation handlers to prevent function recreation
  const handlePress = useCallback(() => {
    router.push({
      pathname: `/(protected)/socialCircles/post/[id]`,
      params: navigationParams,
    });
  }, [navigationParams]);

  const handleCommentPress = useCallback(() => {
    router.push({
      pathname: `/(protected)/socialCircles/post/[id]`,
      params: navigationParams,
    });
  }, [navigationParams]);

  // Memoize computed values
  const computedValues = useMemo(() => ({
    location: props.item.socialCircle?.name || 'Public',
    socialCircleName: props.item.socialCircle?.name || '',
    createdAt: timeAgo(props.item.createdAt),
    cover: props.item.image?.[0],
  }), [
    props.item.socialCircle?.name,
    props.item.createdAt,
    props.item.image,
  ]);

  return (
    <Pressable onPress={handlePress}>
      <PostsCard
        onLikePress={props.handleToggleLike}
        onSelect={props.handleReportPost}
        isPostOwner={props.isPostOwner}
        userId={props.item.postedByUserId._id}
        onCommentPress={handleCommentPress}
        location={computedValues.location}
        variant='circle'
        title={props.item.content}
        createdAt={computedValues.createdAt}
        likesCount={props.item.likes.likesCount}
        cover={computedValues.cover}
        avatar={props.item.postedByUserId.image}
        name={props.item.postedByUserId.displayName}
        socialCircle={computedValues.socialCircleName}
        isLiked={props.isLiked}
      />
    </Pressable>
  );
});

interface CirclesPostsListProps {
  data: IPost[];
  isLoading?: boolean;
  isFetchingNextPage?: boolean;
  fetchNextPage?: () => void;
  hasNextPage?: boolean;
}

const CirclesPostsList = ({
  data = [],
  isLoading,
  isFetchingNextPage,
  fetchNextPage,
  hasNextPage,
}: CirclesPostsListProps) => {
  type ListItem = IPost | { _id: string };
  const { user } = useSession();
  const [posts, setPosts] = useState<IPost[]>([]);

  // Performance monitoring
  const { measureSync, trackInteraction } = usePerformanceMonitor({
    componentName: 'CirclesPostsList',
    logSlowRenders: true,
  });

  const { mutate: deletePost } = useDeletePost();
  const { handleToggleLike } = useTogglePostsLikes(setPosts, posts || []);

  // Optimize data synchronization with performance tracking
  useEffect(() => {
    measureSync(() => {
      setPosts(data);
    }, 'updatePostsData');
  }, [data, measureSync]);

  // Memoized handlers to prevent unnecessary re-renders
  const handlePostActions = useCallback((action: string, id: string) => {
    const endTracking = trackInteraction('postAction');

    if (action === 'delete') {
      setPosts((prev) => prev.filter((post) => post._id !== id));
      deletePost(id);
    }

    endTracking();
  }, [deletePost, trackInteraction]);

  const handleEndReached = useCallback(() => {
    if (hasNextPage && !isFetchingNextPage) {
      const endTracking = trackInteraction('loadMore');
      fetchNextPage?.();
      endTracking();
    }
  }, [hasNextPage, isFetchingNextPage, fetchNextPage, trackInteraction]);

  // Memoized skeleton data to prevent recreation
  const skeletonData = useMemo(() =>
    Array.from({ length: 5 }, (_, index) => ({
      _id: `skeleton-${index}`,
    })), []
  );

  // Memoized render item function
  const renderItem = useCallback(({ item }: { item: ListItem }) => {
    if ('postedByUserId' in item) {
      if (!item) return <CardSkelton />;

      return (
        <RenderItem
          isPostOwner={user?._id === item.postedByUserId?._id}
          handleReportPost={(e) => handlePostActions(e, item._id)}
          handleToggleLike={() => user?._id && handleToggleLike(item._id)}
          isLiked={item.likes.userId.includes(user?._id || '')}
          item={item}
        />
      );
    }

    // Return skeleton for loading state
    return <CardSkelton />;
  }, [user?._id, handlePostActions, handleToggleLike]);

  // Memoized key extractor
  const keyExtractor = useCallback((item: ListItem, index: number) =>
    item ? item._id : `skeleton-${index}`, []
  );

  // Memoized footer component
  const footerComponent = useMemo(() =>
    isFetchingNextPage ? (
      <View className='py-4'>
        <Loading isLoading />
      </View>
    ) : null, [isFetchingNextPage]
  );

  // Memoized list data
  const listData = useMemo(() =>
    isLoading ? skeletonData : posts,
    [isLoading, skeletonData, posts]
  );

  return (
    <OptimizedFlatList
      data={listData}
      className='flex-1 mb-8'
      keyExtractor={keyExtractor}
      ListEmptyComponent={!isLoading ? <EmptyComponent /> : null}
      renderItem={renderItem}
      contentContainerClassName='gap-3 py-4 flex-grow'
      showsVerticalScrollIndicator={false}
      itemHeight={280} // Estimated height for better performance
      enableVirtualization={true}
      initialNumToRender={5}
      maxToRenderPerBatch={5}
      onEndReachedThreshold={0.5}
      onEndReached={handleEndReached}
      removeClippedSubviews={true}
      ListFooterComponent={footerComponent}
    />
  );
};

// Export memoized component for better performance
export default memo(CirclesPostsList);
