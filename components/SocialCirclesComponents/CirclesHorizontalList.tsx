import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  useWindowDimensions,
  Platform,
} from 'react-native';
import React, { memo, useMemo } from 'react';
import EmptyListComponent from '../ui/EmptyListComponent';
import CircleSkeleton from '../Skilltons/CircleSkelton';
import Avatar from '../ui/Avatar';
import { COLORS } from '@/constants/Theme';
import { ISocialCircle } from '@/types';
import CustomText from '../ui/CustomText';
import { Link } from 'expo-router';

interface ICirclesHorizontalListProps {
  isLoading: boolean;
  data: ISocialCircle[] | undefined;
}

// Social Circles item
const RenderItem = memo(
  ({
    item,
    circleSize,
    strokeWidth,
  }: {
    item: ISocialCircle;
    circleSize: number;
    strokeWidth: number;
  }) => {
    // Calculate total size including border
    const totalSize = circleSize + strokeWidth * 2;

    return (
      <Link
        href={{
          pathname: `/(protected)/socialCircles/filteredPosts/[id]`,
          params: {
            id: item._id,
            title: item.name,
            image: item.image,
            numberOfParticipants: String(item.members?.length),
          },
        }}
        asChild
      >
        <TouchableOpacity style={styles.itemContainer}>
          {/* On Android, we need to use a wrapper View for the border */}
          <View
            style={[
              styles.circleWrapper,
              {
                width: totalSize,
                height: totalSize,
                borderRadius: totalSize / 2,
                borderWidth: strokeWidth,
                padding: strokeWidth, // Add padding equal to stroke width
              },
            ]}
          >
            {/* Inner container for the Avatar */}
            <View
              style={[
                styles.avatarContainer,
                {
                  width: circleSize,
                  height: circleSize,
                  borderRadius: circleSize / 2,
                },
              ]}
            >
              <Avatar
                source={{ uri: item.image }}
                text={item.name}
                size={circleSize}
              />
            </View>
          </View>
          <CustomText
            className='text-neutral-100 text-body3 font-medium'
            numberOfLines={1}
            style={[styles.nameText, { maxWidth: totalSize }]}
          >
            {item.name}
          </CustomText>
        </TouchableOpacity>
      </Link>
    );
  },
);

const CirclesHorizontalList = ({
  isLoading,
  data,
}: ICirclesHorizontalListProps) => {
  const { width: screenWidth } = useWindowDimensions();

  // Calculate responsive sizes based on screen width and platform
  const dimensions = useMemo(() => {
    // Base size calculation with platform-specific adjustments
    // iOS gets larger circles (15% of screen width), Android stays at 12%
    const sizeFactor = Platform.OS === 'ios' ? 0.15 : 0.12;
    const baseSize = screenWidth * sizeFactor;

    // Platform-specific size ranges
    // iOS: 55-85px, Android: 45-70px
    const minSize = Platform.OS === 'ios' ? 55 : 45;
    const maxSize = Platform.OS === 'ios' ? 85 : 70;

    // Clamp the circle size within platform-specific ranges
    const circleSize = Math.max(minSize, Math.min(maxSize, baseSize));

    // Calculate stroke width proportionally
    // iOS gets slightly thinner stroke relative to size for elegance
    const strokeFactor = Platform.OS === 'ios' ? 0.05 : 0.06;
    const baseStrokeWidth = circleSize * strokeFactor;

    // Minimum stroke width is platform-specific
    const minStrokeWidth = Platform.OS === 'ios' ? 2 : 2.5;
    const strokeWidth = Math.max(minStrokeWidth, Math.min(4, baseStrokeWidth));

    // Calculate spacing between items (proportional to circle size)
    // iOS gets slightly more spacing for a more spread-out look
    const spacingFactor = Platform.OS === 'ios' ? 0.28 : 0.25;
    const itemSpacing = Math.max(12, circleSize * spacingFactor);

    return { circleSize, strokeWidth, itemSpacing };
  }, [screenWidth]);

  const { circleSize, strokeWidth, itemSpacing } = dimensions;

  // Memoize skeleton data
  const skeletonData = useMemo(() => Array(5).fill(''), []);

  // Calculate total size for skeleton (including stroke)
  const totalSkeletonSize = circleSize + strokeWidth * 2;

  return (
    <View style={styles.container}>
      {isLoading ? (
        <FlatList
          horizontal
          showsHorizontalScrollIndicator={false}
          initialNumToRender={5}
          data={skeletonData}
          keyExtractor={(_, index) => String(index)}
          contentContainerStyle={[styles.listContent, { gap: itemSpacing }]}
          renderItem={() => (
            <CircleSkeleton
              size={totalSkeletonSize}
              spacing={itemSpacing / 4}
            />
          )}
        />
      ) : (
        <FlatList
          ListEmptyComponent={
            <EmptyListComponent
              title='No Social Circles Yet'
              description='Create or join social circles!'
            />
          }
          data={data || []}
          horizontal={data && !!data?.length}
          initialNumToRender={5}
          maxToRenderPerBatch={8}
          windowSize={5}
          removeClippedSubviews={true}
          contentContainerStyle={[
            styles.listContent,
            {
              gap: itemSpacing,
              // Add platform-specific padding adjustments
              paddingVertical: Platform.OS === 'ios' ? 12 : 8,
            },
          ]}
          showsHorizontalScrollIndicator={false}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <RenderItem
              item={item}
              circleSize={circleSize}
              strokeWidth={strokeWidth}
            />
          )}
        />
      )}
    </View>
  );
};

export default memo(CirclesHorizontalList);

const styles = StyleSheet.create({
  container: {
    width: '100%',
  },
  listContent: {
    paddingHorizontal: 16,
  },
  itemContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    gap: Platform.OS === 'ios' ? 6 : 4, // More gap on iOS
  },
  circleWrapper: {
    borderColor: COLORS.secondary[300],
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden', // Important for Android
    // Add platform-specific shadow for iOS
    ...Platform.select({
      ios: {
        shadowColor: COLORS.secondary[300],
        shadowOffset: { width: 0, height: 1 },
        shadowOpacity: 0.2,
        shadowRadius: 2,
      },
      android: {},
    }),
  },
  avatarContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  nameText: {
    textAlign: 'center',
    marginTop: Platform.OS === 'ios' ? 4 : 2, // More margin on iOS
  },
});
