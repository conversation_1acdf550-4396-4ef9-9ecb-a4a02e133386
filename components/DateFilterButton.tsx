import React, { memo } from 'react';
import { TouchableOpacity } from 'react-native';
import CustomText from './ui/CustomText';

const DateFilterButton = ({
  title,
  onPress,
  isActive,
}: {
  title: string;
  onPress: () => void;
  isActive: boolean;
}) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      className={`bg-secondary-300/20 px-5  py-2 rounded-[2rem] border-2 ${
        isActive ? ' border-secondary-300' : 'border-secondary-300/20'
      }`}
    >
      <CustomText className='text-neutral-800 text-h4'>{title}</CustomText>
    </TouchableOpacity>
  );
};

export default memo(DateFilterButton);
