import { useEffect } from 'react';
import { useSession } from '@/context/AuthContext';
import useGetBlockedUsers from '@/hooks/userHooks/useGetBlockedUsers';

/**
 * Component to initialize blocked users data when the user is authenticated
 * This breaks the circular dependency by moving the initialization outside of AuthContext
 * 
 * Usage: Place this component inside the SessionProvider but outside of AuthContext
 */
const BlockedUsersInitializer = () => {
  const { userId, user } = useSession();

  // Fetch blocked users when user is authenticated
  const { isLoading, error, blockedUsers } = useGetBlockedUsers({
    enabled: !!userId && !!user,
    staleTime: 10 * 60 * 1000, // 10 minutes - blocked users don't change frequently
    gcTime: 30 * 60 * 1000, // 30 minutes cache time
  });

  // Log initialization status for debugging
  useEffect(() => {
    if (userId && user) {
      console.log('🔒 Initializing blocked users for user:', userId);
    }
  }, [userId, user]);

  useEffect(() => {
    if (blockedUsers.length > 0) {
      console.log(`🔒 Loaded ${blockedUsers.length} blocked users`);
    }
  }, [blockedUsers.length]);

  useEffect(() => {
    if (error) {
      console.error('🔒 Error loading blocked users:', error);
    }
  }, [error]);

  // This component doesn't render anything - it's just for side effects
  return null;
};

export default BlockedUsersInitializer;
