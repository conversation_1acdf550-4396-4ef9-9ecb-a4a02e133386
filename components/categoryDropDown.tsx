import { FlatList, StyleSheet, View } from 'react-native';
import React, { useCallback } from 'react';
import { COLORS, normalized } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import CustomDropDown from './ui/CustomDropDown';

import useLanguage from '@/hooks/useLanguage';
import CustomTag from './ui/CustomTag';
import { ICategory } from '@/types';
type ICategoryWithoutTimestamps = Omit<ICategory, 'createdAt' | 'updatedAt'>;

type ICategoryDropDownProps = {
  data: ICategory[];
  onSelect: (item: ICategory) => void;
  selectedCategories: ICategoryWithoutTimestamps[];
  deleteSelectedCategory: (item: ICategoryWithoutTimestamps) => void;
  zIndex?: number;
};

const CategoryDropDown = ({
  data,
  deleteSelectedCategory,
  onSelect,
  selectedCategories,
  zIndex = 1000,
}: ICategoryDropDownProps) => {
  const { isArabic } = useLanguage();
  
  const renderItem = useCallback(({ item }: { item: ICategoryWithoutTimestamps }) => (
    <CustomTag
      title={isArabic ? item.arabic : item.english}
      onDelete={() => deleteSelectedCategory(item)}
    />
  ), [isArabic, deleteSelectedCategory]);
  
  const keyExtractor = useCallback((item: ICategoryWithoutTimestamps) => item._id, []);
  
  return (
    <View style={{ zIndex }}>
      <CustomText style={styles.sectionTitle}>Category</CustomText>
      <CustomDropDown
        placeholder='Category'
        data={data}
        onSelectItem={onSelect}
        renderItem={({ item }) => (
          <CustomText className='text-h2 text-white-50'>
            {isArabic ? item.arabic : item.english}
          </CustomText>
        )}
        listProps={{
          nestedScrollEnabled: true,
          keyboardShouldPersistTaps: 'handled',
        }}
      />
      {/* Tags */}
      {selectedCategories && selectedCategories.length > 0 ? (
        <FlatList
          nestedScrollEnabled={true}
          keyboardShouldPersistTaps='handled'
          className='mt-3 h-[3rem]'
          contentContainerClassName='gap-3'
          horizontal
          showsHorizontalScrollIndicator={false}
          data={selectedCategories}
          keyExtractor={keyExtractor}
          renderItem={renderItem}
        />
      ) : null}
    </View>
  );
};

export default React.memo(CategoryDropDown);

const styles = StyleSheet.create({
  sectionTitle: {
    color: COLORS.white[50],
    fontWeight: 'semibold',
    marginBottom: normalized(3),
    fontSize: normalized(16),
  },
});
