import { View, ScrollView, TouchableOpacity, StyleSheet } from 'react-native';
import React, { memo } from 'react';
import { Image } from 'expo-image';
import { SCREEN_WIDTH } from '@/constants/Theme';

const IMAGE_WIDTH = SCREEN_WIDTH / 2 - 12; // Half screen width minus margins

type Props = { images: string[]; onPress: (index: number) => void };

const ExploreGalleryComponent = ({ images, onPress }: Props) => {
  return (
    <ScrollView contentContainerStyle={styles.container}>
      {/* First Row */}
      <View style={styles.row}>
        {/* Left Side (2 images stacked) */}
        <View style={styles.leftColumn}>
          <TouchableOpacity onPress={() => onPress(0)}>
            <Image source={{ uri: images[0] }}
style={styles.smallImage} />
          </TouchableOpacity>
          <TouchableOpacity onPress={() => onPress(1)}>
            <Image source={{ uri: images[1] }}
style={styles.smallImage} />
          </TouchableOpacity>
        </View>

        {/* Right Side (One large image) */}
        <TouchableOpacity onPress={() => onPress(2)}
style={styles.flexOne}>
          <Image source={{ uri: images[2] }}
style={styles.largeImage} />
        </TouchableOpacity>
      </View>

      {/* Second Row (Full-width image) */}
      <TouchableOpacity onPress={() => onPress(3)}>
        <Image source={{ uri: images[3] }}
style={styles.fullWidthImage} />
      </TouchableOpacity>

      {/* Third Row (Two images side by side) */}
      <View style={styles.row}>
        <TouchableOpacity onPress={() => onPress(4)}>
          <Image source={{ uri: images[4] }}
style={styles.smallImage} />
        </TouchableOpacity>
        <TouchableOpacity onPress={() => onPress(5)}>
          <Image source={{ uri: images[5] }}
style={styles.smallImage} />
        </TouchableOpacity>
      </View>
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingVertical: 6,
  },
  row: {
    flexDirection: 'row',
    gap: 8, // Improved spacing
    marginHorizontal: 6,
  },
  leftColumn: {
    gap: 8,
  },
  smallImage: {
    width: IMAGE_WIDTH,
    height: 150,
    borderRadius: 10,
  },
  largeImage: {
    flex: 1,
    height: 310,
    borderRadius: 10,
  },
  fullWidthImage: {
    width: '100%',
    height: 200,
    marginTop: 8,
    borderRadius: 10,
  },
  flexOne: {
    flex: 1,
  },
});

export default memo(ExploreGalleryComponent);
