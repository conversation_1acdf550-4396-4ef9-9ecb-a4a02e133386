import React, { useCallback, useState, useEffect } from 'react';
import {
  Modal,
  TextInput,
  TouchableOpacity,
  View,
  StyleSheet,
  Platform,
} from 'react-native';
import DateTimePicker, {
  DateTimePickerEvent,
} from '@react-native-community/datetimepicker';
import useLanguage from '@/hooks/useLanguage';
import { COLORS, normalized } from '@/constants/Theme';
import CustomText from './ui/CustomText';
import { APP_Icons } from '@/constants/Images';

interface CustomDatePickerProps {
  initialDate?: Date;
  title?: string;
  onDateChange?: (selectedDate: Date) => void;
  placeholder?: string;
  error?: string;
  is24Hour?: boolean;
  isOptional?: boolean;
  mode?: 'date' | 'time';
  display?: 'default' | 'spinner' | 'calendar' | 'clock';
  value?: Date | null;
  minimumDate?: Date;
  maximumDate?: Date;
}

const CustomDatePicker: React.FC<CustomDatePickerProps> = ({
  initialDate = new Date(),
  placeholder,
  onDateChange,
  error,
  title,
  isOptional,
  mode = 'date',
  is24Hour = false,
  display = 'spinner',
  value,
  minimumDate,
  maximumDate,
}) => {
  const { isArabic } = useLanguage();
  const [showModal, setShowModal] = useState(false);
  const [date, setDate] = useState<Date>(initialDate);
  const [isFocused, setIsFocused] = useState(false);

  useEffect(() => {
    if (value) {
      setDate(value);
    } else {
      // Initialize time to current time if no value provided
      const now = new Date();
      if (mode === 'time' && !value) {
        setDate(now);
      }
    }
  }, [value, mode]);

  const formatDateTime = useCallback(
    (dateInput: string | Date): string => {
      if (!dateInput) return '';

      const date =
        typeof dateInput === 'string' ? new Date(dateInput) : dateInput;

      if (isNaN(date.getTime())) return ''; // Handle invalid dates

      if (mode === 'time') {
        const hours = date.getHours();
        const minutes = date.getMinutes().toString().padStart(2, '0');

        if (is24Hour) {
          return `${hours.toString().padStart(2, '0')}:${minutes}`;
        }

        const ampm = hours >= 12 ? 'PM' : 'AM';
        const twelveHour = hours % 12 || 12;
        return `${twelveHour}:${minutes} ${ampm}`;
      }

      // Format as DD/MM/YYYY
      const day = date.getDate().toString().padStart(2, '0');
      const month = (date.getMonth() + 1).toString().padStart(2, '0');
      const year = date.getFullYear();

      return `${day}/${month}/${year}`;
    },
    [mode, is24Hour],
  );

  const getDefaultPlaceholder = useCallback(() => {
    if (placeholder) return placeholder;
    return mode === 'time'
      ? is24Hour
        ? 'HH:MM'
        : 'HH:MM AM/PM'
      : 'DD/MM/YYYY';
  }, [mode, is24Hour, placeholder]);

  const handleDateChange = (
    event: DateTimePickerEvent,
    selectedDate?: Date,
  ) => {
    const currentDate = selectedDate || date;

    if (event.type === 'set') {
      setDate(currentDate);
      onDateChange?.(currentDate);

      if (Platform.OS === 'android') {
        setShowModal(false);
      }
    }
  };

  const toggleModal = useCallback(() => {
    setShowModal((prev) => !prev);
    setIsFocused((prev) => !prev);
  }, []);

  const handleIOSConfirm = useCallback(() => {
    onDateChange?.(date);
    toggleModal();
  }, [date, onDateChange, toggleModal]);

  return (
    <View className='space-y-2'>
      {/* Title */}
      {title && (
        <View className='flex-row mb-1.5 items-center'>
          <CustomText className='text-h4 text-black-200 font-poppins text-white-50 px-2'>
            {title}
          </CustomText>
          {isOptional && (
            <CustomText className='text-neutral-300'>(optional)</CustomText>
          )}
        </View>
      )}

      {/* Input Field */}
      <View
        className={`
          py-4 group rounded-xl border 
          ${error ? 'border-danger bg-accent-300/20' : 'bg-white-50/10'}
          ${isFocused ? 'border-secondary-300' : 'border-transparent'}
          ${!isArabic ? 'flex-row' : 'flex-row-reverse'}
          bg-gray2/30 w-full px-4 items-center
        `}
      >
        <View className='flex-1 flex-row justify-between items-center'>
          <TextInput
            placeholderTextColor={COLORS.neutral[300]}
            className='text-neutral-300 flex-1'
            placeholder={getDefaultPlaceholder()}
            value={date ? formatDateTime(date) : ''}
            onPressIn={toggleModal}
            editable={false}
          />
          <TouchableOpacity onPress={toggleModal} className='justify-end'>
            {mode === 'date' ? (
              <APP_Icons.CALENDER
                width={normalized(18)}
                height={normalized(18)}
              />
            ) : (
              <APP_Icons.ClockIcon
                width={normalized(18)}
                height={normalized(18)}
              />
            )}
          </TouchableOpacity>
        </View>
        {error && (
          <CustomText className='text-danger font-poppins'>{error}</CustomText>
        )}
      </View>

      {/* Android Picker */}
      {Platform.OS === 'android' && showModal && (
        <DateTimePicker
          value={date}
          mode={mode}
          is24Hour={is24Hour}
          display={display}
          onChange={handleDateChange}
          minimumDate={minimumDate}
          maximumDate={maximumDate}
        />
      )}

      {/* iOS Picker Modal */}
      {Platform.OS === 'ios' && (
        <Modal
          transparent
          animationType='slide'
          visible={showModal}
          onRequestClose={toggleModal}
        >
          <View style={styles.modalBackground}>
            <TouchableOpacity
              onPress={toggleModal}
              style={styles.modalContainer}
            >
              <DateTimePicker
                value={date}
                mode={mode}
                is24Hour={is24Hour}
                display={display}
                onChange={handleDateChange}
                textColor={COLORS.black}
                minimumDate={minimumDate}
                maximumDate={maximumDate}
              />
              <View style={styles.buttonContainer}>
                <TouchableOpacity onPress={toggleModal} style={styles.button}>
                  <CustomText className='text-neutral-100'>Cancel</CustomText>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleIOSConfirm}
                  style={[styles.button, styles.confirmButton]}
                >
                  <CustomText className='text-neutral-100'>Confirm</CustomText>
                </TouchableOpacity>
              </View>
            </TouchableOpacity>
          </View>
        </Modal>
      )}
    </View>
  );
};

export default CustomDatePicker;

const styles = StyleSheet.create({
  modalBackground: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)', // Semi-transparent background
  },
  modalContainer: {
    width: '90%',
    backgroundColor: 'white',
    borderRadius: 10,
    padding: 20,
    alignItems: 'center',
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    width: '100%',
    marginTop: 15,
  },
  button: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: COLORS.neutral[300],
  },
  confirmButton: {
    backgroundColor: COLORS.primary[50],
  },
});
