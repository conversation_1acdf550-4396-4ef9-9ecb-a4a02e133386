import React, { memo } from 'react';
import { TouchableOpacity, View } from 'react-native';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import CustomText from '@/components/ui/CustomText';

interface CreateGroupButtonProps {
  onPress: () => void;
}

/**
 * Memoized create group button component
 * Prevents re-renders when parent component updates
 */
const CreateGroupButton: React.FC<CreateGroupButtonProps> = memo(({ onPress }) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      className='bg-primary-50/20 py-3 px-2.5 rounded-xl flex-row justify-between items-center'
      activeOpacity={0.7}
    >
      <View className='flex-row items-center gap-3'>
        <View className='bg-primary-50 rounded-full w-10 h-10 justify-center items-center'>
          <AntDesign name='adduser' size={18} color={COLORS.neutral[100]} />
        </View>
        <View>
          <CustomText className='text-white-50 text-h5'>
            New Group Chat
          </CustomText>
          <CustomText className='text-neutral-200 text-body1'>
            Create a new group chat
          </CustomText>
        </View>
      </View>

      <Ionicons
        name='chevron-forward'
        size={24}
        color={COLORS.neutral[100]}
      />
    </TouchableOpacity>
  );
});

CreateGroupButton.displayName = 'CreateGroupButton';

export default CreateGroupButton;
