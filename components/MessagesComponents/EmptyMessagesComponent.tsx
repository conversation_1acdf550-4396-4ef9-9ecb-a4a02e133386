import React, { memo } from 'react';
import EmptyListComponent from '@/components/ui/EmptyListComponent';
import { APP_Icons } from '@/constants/Images';

/**
 * Memoized empty messages component
 * Never re-renders since it has no dynamic content
 */
const EmptyMessagesComponent: React.FC = memo(() => {
  return (
    <EmptyListComponent
      title='No Conversations Yet'
      description='Start connecting with others by joining activities or messaging users'
      emptyIcon={() => (
        <APP_Icons.EmptyMessageIcon width={70} height={70} />
      )}
    />
  );
});

EmptyMessagesComponent.displayName = 'EmptyMessagesComponent';

export default EmptyMessagesComponent;
