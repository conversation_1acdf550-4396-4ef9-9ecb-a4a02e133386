import React, { memo, useMemo, useCallback } from 'react';
import { Pressable } from 'react-native';
import ChatListItem from '@/components/chatComponents/ChatListItem';

interface ChatListItemOptimizedProps {
  item: any;
  onPress: (chatId: string, chatData: any) => void;
}

/**
 * Optimized chat list item wrapper with memoized props and handlers
 * Prevents unnecessary re-renders and optimizes navigation
 */
const ChatListItemOptimized: React.FC<ChatListItemOptimizedProps> = memo(({
  item,
  onPress,
}) => {
  // Memoize computed values to prevent recalculation
  const computedValues = useMemo(() => ({
    title: item.displayName || item.username,
    message: item.last_message?.content || '',
    image: item.profile_picture,
    numberOfMessages: item.unseen_message_count || 0,
    isOnline: item.is_online || false,
    userId: item.user_id,
    createdAt: new Date(), // You might want to use actual timestamp
  }), [
    item.displayName,
    item.username,
    item.last_message?.content,
    item.profile_picture,
    item.unseen_message_count,
    item.is_online,
    item.user_id,
  ]);

  // Memoized press handler
  const handlePress = useCallback(() => {
    onPress(computedValues.userId, item);
  }, [onPress, computedValues.userId, item]);

  return (
    <Pressable onPress={handlePress}>
      <ChatListItem
        createdAt={computedValues.createdAt}
        message={computedValues.message}
        title={computedValues.title}
        image={computedValues.image}
        numberOfMessages={computedValues.numberOfMessages}
        href={'/(protected)/messages/[id]'}
        roomId={computedValues.userId}
        type='single'
        isOnline={computedValues.isOnline}
      />
    </Pressable>
  );
});

ChatListItemOptimized.displayName = 'ChatListItemOptimized';

export default ChatListItemOptimized;
