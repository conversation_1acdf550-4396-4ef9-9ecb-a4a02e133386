import { View, TouchableOpacity } from 'react-native';
import React, { memo } from 'react';
import { LinkProps, router } from 'expo-router';
import CustomText from '../ui/CustomText';

// Follower and following
const UserInfo = memo(
  (props: { title: string; value: number; href: LinkProps['href'] }) => {
    return (
      <TouchableOpacity onPress={() => router.push(props.href)}>
        <View className='flex-row items-center gap-x-1'>
          <CustomText className='font-bold text-h6 text-white-50'>
            {props.value}
          </CustomText>
          <CustomText className='text-body3 text-neutral-300'>
            {props.title}
          </CustomText>
        </View>
      </TouchableOpacity>
    );
  },
);

type Props = {
  followers: number;
  following: number;
  userId: string;
};

const ProfileFollowersAndFollowing: React.FC<Props> = ({
  followers = 0,
  following = 0,
  userId,
}) => {
  return (
    <View className='flex-row  gap-3  my-3'>
      <UserInfo
        title='Followers'
        value={followers}
        href={`/(protected)/profile/followers/${userId}`}
      />

      <UserInfo
        title='Following'
        value={following}
        href={`/(protected)/profile/following/${userId}`}
      />
    </View>
  );
};

export default memo(ProfileFollowersAndFollowing);
