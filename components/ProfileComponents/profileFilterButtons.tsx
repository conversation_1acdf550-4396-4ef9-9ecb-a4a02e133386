import { TouchableOpacity, View } from 'react-native';
import React from 'react';
import CustomText from '../ui/CustomText';
import { COLORS } from '@/constants/Theme';

const FILTER_BUTTONS = [
  { title: 'Posts', id: 'posts' },
  { title: 'Media', id: 'media' },
  { title: 'Likes', id: 'likes' },
] as const;

export type FilterId = (typeof FILTER_BUTTONS)[number]['id'];

interface Props {
  onChange: (id: FilterId) => void;
  value: FilterId;
}

const ProfileFilterButtons: React.FC<Props> = ({ onChange, value }) => {
  const handleFilterChange = (id: FilterId) => {
    onChange(id);
  };

  const activeIndex = FILTER_BUTTONS.findIndex((item) => item.id === value);
  const segmentWidth = 100 / FILTER_BUTTONS.length;

  return (
    <>
      <View className='flex-row justify-evenly gap-x-4'>
        {FILTER_BUTTONS.map((item) => (
          <TouchableOpacity
            key={item.id}
            onPress={() => handleFilterChange(item.id)}
          >
            <CustomText
              className={`${
                value === item.id ? 'text-secondary-300' : 'text-[#A7A7A7A7]'
              } text-h4`}
            >
              {item.title}
            </CustomText>
          </TouchableOpacity>
        ))}
      </View>

      <View className='relative w-full h-[2px] bg-neutral-600 mt-2 mb-5'>
        <View
          style={{
            position: 'absolute',
            left: `${activeIndex * segmentWidth}%`,
            width: `${segmentWidth}%`,
            height: '100%',
            backgroundColor: COLORS.secondary[300],
          }}
        />
      </View>
    </>
  );
};

export default ProfileFilterButtons;
