import { TouchableOpacity, View } from 'react-native';
import React, { memo, useMemo } from 'react';
import CustomHeader from '../ui/CustomHeader';
import { router } from 'expo-router';
import GearIcon from '@/assets/icons/gear.svg';
import { COLORS, normalized } from '@/constants/Theme';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';
import { Octicons } from '@expo/vector-icons';
import CustomDropDownMenu from '@/components/ui/CustomDropDownMenu';
import useBlockUser from '@/hooks/userHooks/useBlockUser';
import Loading from '@/layouts/Loading';
import { useSession } from '@/context/AuthContext';
import { useUserStore } from '@/stores/useUserStore';

type Props = {
  avatar: string;
  displayName: string;
  username: string;
  location: string;
  userId?: string;
};

const ProfileHeader: React.FC<Props> = ({
  avatar,
  displayName,
  location,
  username,
  userId,
}) => {
  const { user } = useSession();
  const { isUserBlocked } = useUserStore();

  const isSameUser = user?._id === userId;
  const isBlocked = useMemo(() => userId ? isUserBlocked(userId) : false, [userId, isUserBlocked]);

  const { toggleBlockUser, isLoading } = useBlockUser();

  const dropDownMenuItems = useMemo(() => [
    {
      key: 'report-user',
      title: 'Report User',
    },
    {
      key: 'block-user',
      title: isBlocked ? 'Unblock User' : 'Block User',
    },
  ], [isBlocked]);

  const handleDropDownSelect = (key: string) => {
    switch (key) {
      case 'report-user':
        break;
      case 'block-user':
        if (userId) {
          toggleBlockUser(userId, isBlocked, {
            username,
            displayName,
            image: avatar,
          });
        }
        break;
      default:
        break;
    }
  };

  if (isLoading) {
    <View className=' justify-center items-center bg-black/20 inset-0 absolute '>
      <Loading isLoading />
    </View>;
  }

  return (
    <View>
      <View className='mb-3 py-2'>
        <CustomHeader title='Profile'>
          {/* {isCurrentUser  && ( */}
          <TouchableOpacity
            className='bg-white-50/10  w-[3rem] h-[3rem] items-center justify-center rounded-full border border-neutral-800'
            onPress={() => router.push('/(protected)/profile/profileSettings')}
          >
            <GearIcon width={normalized(18)} height={normalized(18)} />
          </TouchableOpacity>
          {/* )} */}
        </CustomHeader>
      </View>

      {/* Profile Header container */}

      {/* avatar container */}
      <CustomDropDownMenu
        onSelect={(item) => handleDropDownSelect(item)}
        items={isSameUser ? [] : dropDownMenuItems}
        triggerElement={
          <View className=' items-center  flex-row   gap-2.5'>
            <Avatar
              source={{ uri: avatar }}
              text={displayName}
              size={normalized(40)}
            />
            <View>
              <CustomText
                className='text-white-50 font-semibold text-h4 '
                numberOfLines={1}
              >
                {displayName}
              </CustomText>
              <CustomText className='text-neutral-300 text-body2 mb-1'>
                @{username}
              </CustomText>
              {/* Location */}
              <View className='flex-row items-center gap-3'>
                <Octicons
                  name='location'
                  size={normalized(14)}
                  color={COLORS.secondary[300]}
                />
                <CustomText className='text-white-50 text-body3'>
                  {location || 'No Entered Location'}
                </CustomText>
              </View>
            </View>
          </View>
        }
      />
    </View>
  );
};

export default memo(ProfileHeader);
