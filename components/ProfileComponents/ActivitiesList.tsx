import { StyleSheet, View } from 'react-native';
import React from 'react';
import { FlatList } from 'react-native-gesture-handler';
import { IActivityItem } from '@/types';
import { getDayFromDate, getMonthFromDate } from '@/utils/formatDates';
import ActivityCard from '../ActivityCard';
import { Link } from 'expo-router';
import { useActivityStore } from '@/stores/useActivityStore';

type IActivityListProps = {
  activities: IActivityItem[];
};

const ActivitiesList = ({ activities }: IActivityListProps) => {
  const { setSelectedActivity } = useActivityStore();
  return (
    <View className='flex-1'>
      <FlatList
        data={activities}
        initialNumToRender={30}
        scrollEventThrottle={16}
        showsVerticalScrollIndicator={false}
        contentContainerClassName='gap-2'
        style={styles.listStyles}
        keyExtractor={(item) => item._id}
        renderItem={({ item }) => {
          return (
            <Link
              onPress={() => setSelectedActivity(item)}
              href={`/(protected)/activity/${item._id}`}
              asChild
            >
              <ActivityCard
                cover={item.images[0]}
                title={item.name}
                location={item.address}
                day={getDayFromDate(item.createdAt)}
                month={getMonthFromDate(item.createdAt)}
                currency={item.currency}
                price={Number(item.price || 0)}
                members={item.members.map((v) => v)}
              />
            </Link>
          );
        }}
      />
    </View>
  );
};

export default ActivitiesList;

const styles = StyleSheet.create({
  listStyles: {
    flex: 1,
  },
});
