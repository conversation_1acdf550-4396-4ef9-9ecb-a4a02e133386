import { FlatList, StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo, useCallback } from 'react';
import { Image } from 'expo-image';
import { normalized } from '@/constants/Theme';
import EmptyListComponent from '../ui/EmptyListComponent';
import Loading from '@/layouts/Loading';
import { IMediaItem } from '@/types';

interface IMediaListProps {
  onPress: (images: string[]) => void;
  isLoading?: boolean;
  data: IMediaItem[];
  isFetchingNextPage?: boolean;
  handleLoadMore?: () => void;
  refetch?: () => void;
  isError?: boolean;
}

const MediaList = ({
  onPress,
  isLoading,
  data,
  handleLoadMore,
  isError,
  isFetchingNextPage,
  refetch,
}: IMediaListProps) => {
  const renderItem = useCallback(
    ({ item }: { item: IMediaItem }) => (
      <TouchableOpacity
        className='flex-1 px-1.5 py-3'
        onPress={() => onPress(item?.image)}
      >
        <Image
          source={{
            uri: item.image?.[0],
          }}
          style={styles.mediaImage}
          transition={200}
          contentFit='cover'
        />
      </TouchableOpacity>
    ),
    [onPress],
  );

  if (isLoading) {
    return (
      <View className='flex-1 justify-center items-center '>
        <Loading isLoading />
      </View>
    );
  }

  return (
    <FlatList
      style={styles.container}
      data={data}
      initialNumToRender={10}
      ListEmptyComponent={
        <EmptyListComponent
          title='No Media for this user'
          isError={isError}
          onRefreshPress={refetch}
        />
      }
      contentContainerClassName='gap-4'
      showsVerticalScrollIndicator={false}
      scrollEventThrottle={16}
      numColumns={2}
      keyExtractor={(item) => item._id}
      onEndReached={handleLoadMore}
      onEndReachedThreshold={0.5}
      ListFooterComponent={
        isFetchingNextPage ? (
          <View className='flex-1 justify-center items-center'>
            <Loading isLoading />
          </View>
        ) : null
      }
      renderItem={renderItem}
    />
  );
};

export default memo(MediaList);

const styles = StyleSheet.create({
  mediaImage: {
    flex: 1,
    height: normalized(180),
    marginRight: normalized(4),
    borderRadius: normalized(8),
  },
  container: {
    flex: 1,
    paddingBottom: normalized(4),
    marginBottom: normalized(50),
  },
});
