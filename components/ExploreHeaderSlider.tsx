import React, { useMemo } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import { FontAwesome } from '@expo/vector-icons';

import GoBack from '@/layouts/GoBack';
import CustomText from './ui/CustomText';
import { APP_Icons } from '@/constants/Images';
import Slider from './ui/Slider/Slider';

type Props = {
  images: string[];
  title: string;
  address: string;
  rating: { average: number; count: number };
  onShare: () => void;
  onGalleryPress: () => void;
};

const ExploreHeaderSlider = (props: Props) => {
  const { images, title, address, rating, onShare, onGalleryPress } = props;
  const inset = useSafeAreaInsets();

  // Make sure we have at least one image to display
  const imageList = useMemo(() => {
    if (!images || images.length === 0) {
      return ['https://via.placeholder.com/800x600?text=No+Image+Available'];
    }
    return images;
  }, [images]);

  // Filter out null/undefined from address
  const filteredAddress = useMemo(() => {
    if (!address) return '';
    return address.replace(/null|undefined|,/g, '').trim();
  }, [address]);

  return (
    <View style={styles.container}>
      {/* Slider */}
      <Slider
        itemList={imageList}
        imageStyles={styles.sliderImage}
        showPagination={true}
        showControls={true}
        autoPlay={true}
        autoPlayInterval={6000}
        containerStyle={styles.sliderContainer}
      />

      {/* Overlay Gradient */}
      <LinearGradient
        style={styles.gradient}
        colors={['transparent', 'rgba(0,0,0,0.5)', 'rgba(0,0,0,0.8)']}
        pointerEvents="none"
      />

      {/* Top Navigation */}
      <View style={[styles.topNav, { top: inset.top }]}>
        <GoBack />
        <TouchableOpacity
          className="bg-primary-200/60 px-3 py-1 rounded-3xl"
          onPress={onGalleryPress}
        >
          <APP_Icons.GalleryIcon
            width={normalized(22)}
            height={normalized(22)}
          />
        </TouchableOpacity>
      </View>

      {/* Bottom Content */}
      <View style={styles.bottomContent}>
        <View className="flex-row items-center justify-between w-full px-3">
          <CustomText
            className="text-white-50 font-semibold text-h1"
            numberOfLines={1}
          >
            {title}
          </CustomText>
          {/* Uncomment if you want to enable sharing
          <TouchableOpacity onPress={onShare}>
            <APP_Icons.ShareIcon width={normalized(22)} height={normalized(22)} />
          </TouchableOpacity>
          */}
        </View>

        {/* Location */}
        <View className="flex-row items-center gap-2 px-2.5">
          <APP_Icons.LocationIcon
            width={normalized(18)}
            height={normalized(18)}
          />
          <CustomText className="text-neutral-100 text-h4">
            {filteredAddress || 'No location provided'}
          </CustomText>
        </View>

        {/* Rating - Uncomment if you want to show ratings
        <View className="flex-row gap-3 px-3">
          <View className="flex-row items-center gap-2 bg-primary-200/60 px-3 py-1 rounded-2xl">
            <View className="flex-row items-center gap-1">
              <FontAwesome name="star" color={COLORS.starColor} size={normalized(20)} />
              <CustomText className="text-neutral-100 font-medium text-h3">
                {rating.average}
              </CustomText>
            </View>
            <CustomText className="text-neutral-100">({rating.count} Reviews)</CustomText>
          </View>
        </View>
        */}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    width: '100%',
    height: SCREEN_HEIGHT / 3,
    position: 'relative',
  },
  sliderContainer: {
    height: SCREEN_HEIGHT / 3,
  },
  sliderImage: {
    width: '100%',
    height: '100%',
    objectFit: 'cover',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: '70%',
    zIndex: 2,
  },
  topNav: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
    padding: 10,
    zIndex: 3,
  },
  bottomContent: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: normalized(10),
    zIndex: 3,
  },
});

export default ExploreHeaderSlider;
