import React, { memo, forwardRef } from 'react';
import {
  StyleProp,
  StyleSheet,
  TouchableOpacity,
  TouchableOpacityProps,
  View,
  ViewStyle,
  TextStyle,
} from 'react-native';
import { COLORS, FONT, normalized } from '@/constants/Theme';
import CustomText from '../CustomText';
import Loading from '@/layouts/Loading';

type ButtonVariant = ViewStyle & { textStyle?: TextStyle };
type ButtonSize = 'sm' | 'md' | 'lg';

interface CustomButtonProps extends TouchableOpacityProps {
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
  variant?: 'primary' | 'secondary' | 'grey' | 'outline' | ButtonVariant;
  size?: ButtonSize;
  loading?: boolean;
  loadingComponent?: React.ReactNode;
  icon?: React.ReactNode;
  iconPosition?: 'left' | 'right';
  loadingColor?: string;
  loadingSize?: number;
  className?: string;
}

const sizeStyles: Record<ButtonSize, ViewStyle> = {
  sm: { paddingVertical: normalized(8), paddingHorizontal: normalized(9) },
  md: { paddingVertical: normalized(7), paddingHorizontal: normalized(9) },
  lg: { paddingVertical: normalized(8), paddingHorizontal: normalized(9) },
};
const variantStyles: Record<string, ButtonVariant> = {
  primary: {
    backgroundColor: COLORS.secondary[300],
    textStyle: { color: COLORS.white[50] },
    borderWidth: 1,
    borderColor: COLORS.secondary[300],
  },
  secondary: {
    backgroundColor: COLORS.primary[50],
    textStyle: { color: COLORS.white[50] },
    borderWidth: 1,
    borderColor: COLORS.primary[50],
  },
  grey: {
    backgroundColor: COLORS.neutral[100],
    textStyle: { color: COLORS.neutral[800] },
  },
  outline: {
    backgroundColor: 'transparent',
    borderWidth: 1,
    borderColor: COLORS.neutral[300],
    textStyle: { color: COLORS.neutral[300] },
  },
};
const CustomButton = forwardRef<
  React.ElementRef<typeof TouchableOpacity>,
  CustomButtonProps
>(
  (
    {
      variant = 'primary',
      size = 'md',
      style,
      textStyle,
      loading = false,
      loadingComponent,
      loadingColor = COLORS.white[50],
      loadingSize = 25,
      icon,
      iconPosition = 'right',
      children,
      disabled,
      ...props
    },
    ref,
  ) => {
    const resolvedVariant =
      typeof variant === 'string' ? variantStyles[variant] : variant;

    const containerStyle = StyleSheet.flatten([
      styles.base,
      sizeStyles[size],
      resolvedVariant,
      disabled && styles.disabled,
      style,
    ]);

    const resolvedTextStyle = StyleSheet.flatten([
      styles.text,
      resolvedVariant.textStyle,
      textStyle,
      disabled && styles.disabledText,
    ]);

    return (
      <TouchableOpacity
        ref={ref}
        style={containerStyle}
        disabled={disabled || loading}
        activeOpacity={0.8}
        accessibilityRole='button'
        {...props}
      >
        {loading ? (
          loadingComponent || (
            <Loading
              isLoading={true}
              colorFilters={[
                { color: loadingColor || COLORS.secondary[300], keypath: '**' },
              ]}
              style={{
                width: normalized(loadingSize),
                height: normalized(loadingSize),
              }}
            />
          )
        ) : (
          <View style={styles.content}>
            {iconPosition === 'left' && icon}
            {children && (
              <CustomText style={resolvedTextStyle} numberOfLines={1}>
                {children}
              </CustomText>
            )}
            {iconPosition === 'right' && icon}
          </View>
        )}
      </TouchableOpacity>
    );
  },
);

const styles = StyleSheet.create({
  base: {
    borderRadius: normalized(20),
    justifyContent: 'center',
    alignItems: 'center',
    flexDirection: 'row',
  },
  content: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: normalized(8),
  },
  text: {
    fontWeight: '600',
    fontSize: 14,
    fontFamily: FONT.fontFamily,
  },
  disabled: {
    opacity: 0.8,
  },
  disabledText: {
    color: COLORS.neutral[500],
  },
});

export default memo(CustomButton);
