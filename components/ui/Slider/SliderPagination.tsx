import { Dimensions, StyleSheet, View, TouchableOpacity } from 'react-native';
import React from 'react';
import Animated, {
  Extrapolation,
  interpolate,
  SharedValue,
  useAnimatedStyle,
} from 'react-native-reanimated';
import { COLORS } from '@/constants/Theme';

type props = {
  items: string[];
  paginationIndex: number;
  scrollX: SharedValue<number>;
  onDotPress?: (index: number) => void;
};

const { width } = Dimensions.get('window');

const SliderPagination = ({ items, paginationIndex, scrollX, onDotPress }: props) => {
  // Don't render pagination for a single item
  if (!items || items.length <= 1) return null;

  return (
    <View style={styles.container}>
      {items.map((_, index) => {
        const pgAnimatedStyle = useAnimatedStyle(() => {
          const dotWidth = interpolate(
            scrollX.value,
            [(index - 1) * width, index * width, (index + 1) * width],
            [8, 24, 8],
            Extrapolation.CLAMP,
          );

          const opacity = interpolate(
            scrollX.value,
            [(index - 1) * width, index * width, (index + 1) * width],
            [0.5, 1, 0.5],
            Extrapolation.CLAMP,
          );

          return {
            width: dotWidth,
            opacity,
          };
        });

        return (
          <TouchableOpacity
            key={index}
            activeOpacity={0.7}
            onPress={() => onDotPress && onDotPress(index)}
          >
            <Animated.View
              style={[
                styles.dot,
                pgAnimatedStyle,
                {
                  backgroundColor: paginationIndex === index
                    ? COLORS.white[50]
                    : 'rgba(255,255,255,0.5)',
                },
              ]}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

export default SliderPagination;

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    height: 40,
    justifyContent: 'center',
    alignItems: 'center',
    paddingBottom: 10,
  },
  dot: {
    height: 8,
    width: 8,
    marginHorizontal: 4,
    borderRadius: 4,
    // Add a subtle shadow for better visibility on all backgrounds
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.2,
    shadowRadius: 1,
    elevation: 2,
  },
});
