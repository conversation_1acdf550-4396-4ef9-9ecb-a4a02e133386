import {
  Dimensions,
  Image,
  View,
  ViewToken,
  StyleSheet,
  TouchableOpacity,
} from 'react-native';
import React, { useEffect, useRef, useState } from 'react';
import Animated, {
  useAnimatedRef,
  useAnimatedScrollHandler,
  useDerivedValue,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import SliderPagination from './SliderPagination';
import { ImageStyle } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';

type Props = {
  itemList: Array<any>;
  imageStyles?: ImageStyle;
  showPagination?: boolean;
  autoPlay?: boolean;
  autoPlayInterval?: number;
  showControls?: boolean;
  containerStyle?: any;
};

const { width } = Dimensions.get('window');

const Slider = ({
  itemList,
  imageStyles,
  showPagination = false,
  autoPlay = true,
  autoPlayInterval = 5000,
  showControls = false,
  containerStyle,
}: Props) => {
  const [paginationIndex, setPaginationIndex] = useState(0);
  const [data, setData] = useState(itemList);
  const [isAutoPlay, setIsAutoPlay] = useState(autoPlay);
  const [currentIndex, setCurrentIndex] = useState(0);

  const opacity = useSharedValue(0);
  const scrollX = useSharedValue(0);
  const interval = useRef<NodeJS.Timeout | null>(null);
  const ref = useAnimatedRef<Animated.FlatList<string>>();
  const offset = useSharedValue(0);

  const viewabilityConfig = { itemVisiblePercentThreshold: 50 };
  const onViewableItemsChanged = ({
    viewableItems,
  }: {
    viewableItems: ViewToken[];
  }) => {
    if (
      viewableItems[0]?.index !== undefined &&
      viewableItems[0].index !== null
    ) {
      const index = viewableItems[0].index % itemList.length;
      setPaginationIndex(index);
      setCurrentIndex(index);
    }
  };

  const viewabilityConfigCallbackPairs = useRef([
    {
      viewabilityConfig,
      onViewableItemsChanged,
    },
  ]);

  const onScrollHandler = useAnimatedScrollHandler({
    onScroll: (e) => (scrollX.value = e.contentOffset.x),
    onMomentumEnd: (e) => {
      offset.value = e.contentOffset.x || 0;
    },
  });

  // Smoothly transition opacity when the image changes
  useDerivedValue(() => {
    opacity.value = withTiming(1, { duration: 500 });
  }, [paginationIndex]);

  useEffect(() => {
    if (isAutoPlay && autoPlay) {
      interval.current = setInterval(() => {
        const nextIndex = (currentIndex + 1) % data.length;
        if (ref.current) {
          ref.current.scrollToOffset({
            offset: nextIndex * width,
            animated: true,
          });
        }
      }, autoPlayInterval);
    } else if (interval.current) {
      clearInterval(interval.current);
      interval.current = null;
    }
    return () => {
      if (interval.current) clearInterval(interval.current);
    };
  }, [
    isAutoPlay,
    autoPlay,
    currentIndex,
    data.length,
    width,
    autoPlayInterval,
  ]);

  // Update data when itemList changes
  useEffect(() => {
    setData(itemList);
  }, [itemList]);

  // Navigation handlers
  const goToPrevious = () => {
    if (currentIndex > 0 && ref.current) {
      const newIndex = currentIndex - 1;
      ref.current.scrollToOffset({
        offset: newIndex * width,
        animated: true,
      });
    }
  };

  const goToNext = () => {
    if (currentIndex < data.length - 1 && ref.current) {
      const newIndex = currentIndex + 1;
      ref.current.scrollToOffset({
        offset: newIndex * width,
        animated: true,
      });
    }
  };

  return (
    <>
      <View style={[styles.container, containerStyle]}>
        <Animated.FlatList
          ref={ref}
          onScroll={onScrollHandler}
          data={data}
          horizontal
          showsHorizontalScrollIndicator={false}
          pagingEnabled={true}
          viewabilityConfigCallbackPairs={
            viewabilityConfigCallbackPairs.current
          }
          renderItem={({ item }) => (
            <View style={styles.slideContainer}>
              <Image
                source={{ uri: item }}
                style={[styles.image, imageStyles]}
              />
            </View>
          )}
          scrollEventThrottle={16}
          onEndReachedThreshold={0.5}
          onScrollBeginDrag={() => setIsAutoPlay(false)}
          onScrollEndDrag={() => setIsAutoPlay(true)}
        />

        {/* Navigation Controls */}
        {showControls && data.length > 1 && (
          <>
            <TouchableOpacity
              style={[styles.navButton, styles.prevButton]}
              onPress={goToPrevious}
              activeOpacity={0.7}
            >
              <Ionicons
                name='chevron-back'
                size={normalized(24)}
                color={COLORS.white[50]}
              />
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.navButton, styles.nextButton]}
              onPress={goToNext}
              activeOpacity={0.7}
            >
              <Ionicons
                name='chevron-forward'
                size={normalized(24)}
                color={COLORS.white[50]}
              />
            </TouchableOpacity>
          </>
        )}

        {/* Pagination Indicators */}
        {showPagination && data.length > 1 && (
          <View style={styles.paginationContainer}>
            <SliderPagination
              items={data}
              scrollX={scrollX}
              paginationIndex={paginationIndex}
              onDotPress={(index) => {
                if (ref.current) {
                  ref.current.scrollToOffset({
                    offset: index * width,
                    animated: true,
                  });
                }
              }}
            />
          </View>
        )}
      </View>
    </>
  );
};

export default Slider;

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    width: '100%',
  },
  slideContainer: {
    width,

    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  navButton: {
    position: 'absolute',
    top: '50%',
    transform: [{ translateY: -20 }],
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0,0,0,0.3)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 10,
  },
  prevButton: {
    left: 10,
  },
  nextButton: {
    right: 10,
  },
  paginationContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    alignItems: 'center',
  },
});
