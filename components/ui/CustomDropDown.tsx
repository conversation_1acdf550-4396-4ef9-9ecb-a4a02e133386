import {
  StyleSheet,
  TouchableOpacity,
  View,
  FlatList,
  ListRenderItem,
  TextInput,
  FlatListProps,
  ViewStyle,
  StyleProp,
} from 'react-native';
import React, { useCallback, useState } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import { BlurView } from 'expo-blur';
import CustomText from './CustomText';

interface DropdownMenuProps<T> {
  data: T[];
  renderItem: ListRenderItem<T>;
  placeholder?: string;
  keyExtractor?: (item: T) => string;
  containerStyle?: StyleProp<ViewStyle>;
  itemContainerStyle?: StyleProp<ViewStyle>;
  onSelectItem?: (item: T) => void;
  selectedItem?: T | null;

  setOpen?: (value: boolean) => void;
  onBlur?: () => void;
  onFocus?: () => void;
  searchQuery?: string;
  onChangeText?: (e: string) => void;
  listProps?: Partial<FlatListProps<T>>;
}

const CustomDropDown = <T extends { id?: string; _id?: string }>({
  data,
  renderItem,
  placeholder = 'Select an item',
  keyExtractor = (item) => String(item.id || item._id || ''),
  containerStyle,
  itemContainerStyle,
  onSelectItem,
  setOpen,
  onBlur,
  onFocus,
  searchQuery,
  onChangeText,
  listProps,
}: DropdownMenuProps<T>) => {
  const [isOpen, setIsOpen] = useState(false);
  const [focused, setFocused] = useState(false);

  const toggleDropdown = () => {
    setIsOpen((prev) => !prev);
    setFocused((prev) => !prev);
    setOpen?.(!isOpen);
  };

  const closeDropdown = () => {
    setIsOpen(false);
    setOpen?.(false);
    setFocused(false);
  };

  const handleItemPress = useCallback(
    (item: T) => {
      onSelectItem?.(item);
      closeDropdown();
    },
    [onSelectItem],
  );

  const renderCustomItem: ListRenderItem<T> = useCallback(
    ({ item }) => (
      <TouchableOpacity
        onPress={() => handleItemPress(item)}
        style={[styles.itemContainer, itemContainerStyle]}
      >
        {renderItem({
          item,
          index: 0,
          separators: {
            highlight: () => {},
            unhighlight: () => {},
            updateProps: () => {},
          },
        })}
      </TouchableOpacity>
    ),
    [handleItemPress, itemContainerStyle, renderItem],
  );

  return (
    <View
      style={[
        styles.container,
        containerStyle,
        focused ? styles.activeContainer : null,
      ]}
    >
      <TouchableOpacity onPress={toggleDropdown} style={styles.inputWrapper}>
        <TextInput
          editable={!!onChangeText}
          value={searchQuery}
          onChangeText={onChangeText}
          focusable={focused}
          onPress={() => {
            setFocused(true);
            setIsOpen(true);
            setOpen?.(true);
          }}
          onFocus={() => {
            onFocus?.();
          }}
          onBlur={() => {
            closeDropdown();
            onBlur?.();
            setFocused(false);
          }}
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={COLORS.neutral[400]}
        />
        <Ionicons
          name={isOpen ? 'chevron-up' : 'chevron-down'}
          color={COLORS.neutral[400]}
          size={normalized(24)}
        />
      </TouchableOpacity>

      {isOpen && (
        <BlurView intensity={30} tint='light' style={styles.listContainer}>
          {data.length ? (
            <FlatList
              nestedScrollEnabled
              keyboardShouldPersistTaps='handled'
              data={data}
              renderItem={renderCustomItem}
              keyExtractor={keyExtractor}
              contentContainerStyle={styles.listContent}
              {...listProps}
            />
          ) : (
            <CustomText style={styles.noDataText}>No Data found</CustomText>
          )}
        </BlurView>
      )}
    </View>
  );
};

export default CustomDropDown;

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1,
    borderWidth: 1,
    borderColor: 'transparent',
    borderRadius: 10,
  },
  activeContainer: {
    borderColor: COLORS.secondary[300],
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: normalized(16),
    paddingVertical: normalized(12),
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: normalized(8),
    borderWidth: 1,
    borderColor: 'transparent',
  },
  input: {
    flex: 1,
    backgroundColor: 'transparent',
    paddingHorizontal: normalized(4),
    height: normalized(24),
    color: COLORS.white[50],
    fontFamily: 'Poppins',
  },
  listContainer: {
    position: 'absolute',
    top: '100%',
    width: '100%',
    marginTop: normalized(8),
    borderRadius: normalized(8),
    maxHeight: normalized(200),
    overflow: 'hidden',
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 10,
    elevation: 5,
  },
  listContent: {
    paddingVertical: 1,
    flexGrow: 1,
  },
  itemContainer: {
    paddingHorizontal: normalized(16),
    paddingVertical: normalized(12),
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  noDataText: {
    paddingVertical: normalized(12),
    textAlign: 'center',
    color: COLORS.neutral[200],
    fontWeight: 'bold',
  },
});
