import { StyleSheet, TextStyle, View } from 'react-native';
import React, { memo } from 'react';
import GoBack from '@/layouts/GoBack';
import CustomText from './CustomText';
import { Href } from 'expo-router';
import { COLORS } from '@/constants/Theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';

export interface ICustomHeaderProps {
  title?: string;
  children?: React.ReactNode;
  textStyles?: TextStyle;
  href?: Href;
  onPress?: () => void;
}

const CustomHeader = ({
  title,
  children,
  textStyles,
  href,
  onPress,
}: ICustomHeaderProps) => {
  const insets = useSafeAreaInsets();
  return (
    <View style={[styles.container, { paddingTop: insets.top - 75 }]}>
      {/* Left: GoBack on extreme left */}
      <View style={styles.left}>
        <GoBack href={href} onPress={onPress} />
      </View>

      {/* Center: Title Deadly Center */}
      <View style={styles.center}>
        <CustomText
          style={[styles.title, textStyles]}
          numberOfLines={2}
          ellipsizeMode='tail'
        >
          {title}
        </CustomText>
      </View>

      {/* Right: Children (if exists) on extreme right */}
      <View style={styles.right}>{children}</View>
    </View>
  );
};

export default memo(CustomHeader);

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between', // Ensure left & right are at extremes
    paddingHorizontal: 16,
  },
  left: {
    flex: 1, // Pushes the title to center
    alignItems: 'flex-start',
  },
  center: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 10, // Prevents edge clipping
  },

  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: COLORS.white[50],
  },
  right: {
    flex: 1, // Pushes the title to center
    alignItems: 'flex-end',
  },
});
