import useLanguage from '@/hooks/useLanguage';
import React from 'react';
import * as DropdownMenu from 'zeego/dropdown-menu';

// type ItemProps = React.ComponentProps<typeof DropdownMenu.Item>;

// export const DropDownMenuCustomItem: React.FC<ItemProps> = (props) => {
//   return <DropdownMenu.Item {...props} />;
// };
type DropDownMenuProps = {
  triggerElement: React.ReactNode;
  items: {
    key: string;
    title: string;
    icon?: string;
    androidIcon?: React.ReactNode;
  }[];
  onSelect: (key: string) => void;
};

const CustomDropDownMenu = (props: DropDownMenuProps) => {
  const { isArabic } = useLanguage();
  return (
    <DropdownMenu.Root dir={isArabic ? 'rtl' : 'ltr'}>
      <DropdownMenu.Trigger>{props.triggerElement}</DropdownMenu.Trigger>
      <DropdownMenu.Content>
        {props.items.map((item) => (
          <DropdownMenu.Item
            onSelect={() => props.onSelect(item.key)}
            key={item.key}
          >
            {item.title}
          </DropdownMenu.Item>
        ))}
      </DropdownMenu.Content>
    </DropdownMenu.Root>
  );
};

export default CustomDropDownMenu;
