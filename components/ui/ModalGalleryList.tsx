import React, { useCallback, useEffect, useRef, useState, memo } from 'react';
import {
  FlatList,
  StyleSheet,
  TouchableOpacity,
  View,
  FlatListProps,
  ImageStyle,
  NativeSyntheticEvent,
  NativeScrollEvent,
  Modal,
  useWindowDimensions,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomText from './CustomText';

interface GalleryListProps {
  images: string[];
  visible: boolean;
  onClose: () => void;
  initialIndex?: number;
  imageStyle?: ImageStyle;
  closeIconColor?: string;
  closeButtonBackgroundColor?: string;
  flatListProps?: Partial<FlatListProps<string>>;
  showCounter?: boolean;
  counterTextStyle?: object;
  counterContainerStyle?: object;
}

const CustomModalGallery = ({
  images = [],
  visible,
  onClose,
  initialIndex = 0,
  imageStyle,
  closeIconColor = '#000',
  closeButtonBackgroundColor = COLORS.white[50],
  flatListProps = {},
  showCounter = true,
  counterTextStyle = {},
  counterContainerStyle = {},
}: GalleryListProps) => {
  const flatListRef = useRef<FlatList>(null);
  const [currentIndex, setCurrentIndex] = useState(initialIndex);
  const { width, height } = useWindowDimensions();
  const insets = useSafeAreaInsets();
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>(
    height > width ? 'portrait' : 'landscape',
  );

  useEffect(() => {
    const subscription = Dimensions.addEventListener('change', ({ window }) => {
      setOrientation(window.height > window.width ? 'portrait' : 'landscape');
    });
    return () => subscription?.remove();
  }, []);

  const getItemLayout = useCallback(
    (_: ArrayLike<string> | null | undefined, index: number) => ({
      length: width,
      offset: width * index,
      index,
    }),
    [width],
  );

  const handleScroll = useCallback(
    (event: NativeSyntheticEvent<NativeScrollEvent>) => {
      const contentOffsetX = event.nativeEvent.contentOffset.x;
      const newIndex = Math.round(contentOffsetX / width);
      setCurrentIndex(newIndex);
    },
    [width],
  );

  const renderItem = useCallback(
    ({ item }: { item: string }) => (
      <View style={{ width, height }}>
        <Image
          source={{ uri: item }}
          style={{ width: '100%', height: '100%' }}
          accessibilityLabel='Gallery image'
          contentFit='contain'
          cachePolicy='memory-disk'
        />
      </View>
    ),
    [width, height],
  );

  const handleScrollToIndexFailed = useCallback(
    (info: {
      index: number;
      highestMeasuredFrameIndex: number;
      averageItemLength: number;
    }) => {
      flatListRef.current?.scrollToIndex({
        index: info.highestMeasuredFrameIndex,
        animated: false,
      });
    },
    [],
  );

  useEffect(() => {
    if (visible && flatListRef.current && images.length > 0) {
      const index = Math.min(Math.max(initialIndex, 0), images.length - 1);
      setCurrentIndex(index);

      const timeout = setTimeout(() => {
        flatListRef.current?.scrollToIndex({
          index,
          animated: false,
        });
      }, 100);

      return () => clearTimeout(timeout);
    }
  }, [visible, initialIndex, images.length]);

  if (!visible) return null;

  return (
    <Modal visible={true} transparent={true} animationType='slide'>
      <View style={[styles.container, { backgroundColor: 'black' }]}>
        <TouchableOpacity
          onPress={onClose}
          activeOpacity={0.7}
          hitSlop={{ top: 20, bottom: 20, left: 20, right: 20 }}
          style={[
            styles.closeButton,
            {
              backgroundColor: closeButtonBackgroundColor,
              top: insets.top + 20,
              left: insets.left + 20,
            },
          ]}
        >
          <Ionicons name='close' size={32} color={closeIconColor} />
        </TouchableOpacity>

        <FlatList
          ref={flatListRef}
          data={images}
          keyExtractor={(_, index) => `gallery-image-${index}`}
          horizontal
          pagingEnabled
          initialScrollIndex={initialIndex}
          getItemLayout={getItemLayout}
          showsHorizontalScrollIndicator={false}
          onMomentumScrollEnd={handleScroll}
          renderItem={renderItem}
          onScrollToIndexFailed={handleScrollToIndexFailed}
          removeClippedSubviews={true}
          maxToRenderPerBatch={3}
          windowSize={5}
          initialNumToRender={1}
          decelerationRate='fast'
          {...flatListProps}
        />

        {showCounter && images.length > 0 && (
          <View
            style={[
              styles.counterContainer,
              counterContainerStyle,
              orientation === 'portrait'
                ? { bottom: insets.bottom + 40 }
                : { top: insets.top + 20 },
            ]}
          >
            <CustomText style={[styles.counterText, counterTextStyle]}>
              {currentIndex + 1}/{images.length}
            </CustomText>
          </View>
        )}
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
  },
  closeButton: {
    position: 'absolute',
    zIndex: 10,
    height: 40,
    width: 40,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 20,
  },
  counterContainer: {
    position: 'absolute',
    alignSelf: 'center',
    backgroundColor: 'rgba(0,0,0,0.5)',
    paddingHorizontal: 20,
    paddingVertical: 8,
    borderRadius: 20,
  },
  counterText: {
    color: COLORS.white[50],
    fontSize: 20,
    fontWeight: '500',
  },
});

export default memo(CustomModalGallery);
