import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import { Image } from 'expo-image';
import { SCREEN_WIDTH } from '@/constants/Theme';
import { array } from 'zod';

interface ImageBoxesProps {
  images: string[];
  onImagePress: (index: number) => void;
  maxImages?: number;
  imageContainerStyle?: object;
  imageStyle?: object;
}

const ImageBoxes = ({
  images,
  onImagePress,
  maxImages = 6,
  imageContainerStyle,
  imageStyle,
}: ImageBoxesProps) => {
  const displayImages = Array.from({ length: maxImages });

  return (
    <View className='flex-row flex-wrap gap-y-3 justify-evenly items-center'>
      {displayImages.map((_, index) => (
        <TouchableOpacity
          onPress={() => onImagePress(index)}
          className='bg-black/10 rounded-md'
          style={[styles.galleryImageContainer, imageContainerStyle]}
          key={`Image-${index}`}
        >
          <Image
            source={{ uri: images[index] }}
            style={[styles.galleryImage, imageStyle]}
            contentFit='cover'
          />
        </TouchableOpacity>
      ))}
    </View>
  );
};

export default memo(ImageBoxes);

const styles = StyleSheet.create({
  galleryImageContainer: {
    width: SCREEN_WIDTH / 3 - 16,
    height: SCREEN_WIDTH / 3 - 16,
    overflow: 'hidden',
  },
  galleryImage: {
    width: '100%',
    height: '100%',
  },
});
