import React, { useEffect } from 'react';
import { View, Pressable, StyleSheet } from 'react-native';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
} from 'react-native-reanimated';
import { Ionicons } from '@expo/vector-icons';
import CustomText from './CustomText';

import { COLORS, normalized, MAX_HEIGHT } from '@/constants/Theme';

type propTypes = {
  isActive: boolean;
  onClose: () => void;
  children: React.ReactNode;
  maxHeight?: number;
  title?: string;
};
const CustomBottomSheet = ({
  isActive,
  onClose,
  children,
  title,
  maxHeight = MAX_HEIGHT,
}: propTypes) => {
  const height = useSharedValue(0); // Initial height
  const opacity = useSharedValue(0);

  const animatedStyle = useAnimatedStyle(() => ({
    height: withSpring(height.value, { damping: 15 }),
    opacity: opacity.value,
  }));

  useEffect(() => {
    if (isActive) {
      height.value = withSpring(MAX_HEIGHT, { damping: 20 });
      opacity.value = withTiming(1, { duration: 300 });
    } else {
      height.value = withTiming(0, { duration: 300 });
      opacity.value = withTiming(0, { duration: 200 });
    }
  }, [isActive]);

  return (
    <Animated.View
      style={[styles.bottomSheet, animatedStyle, { maxHeight: maxHeight }]}
    >
      <View className='flex-row items-center'>
        <CustomText className='text-black text-center flex-1 text-h1 font-semibold p-4'>
          {title}
        </CustomText>
        <Pressable onPress={onClose}
className='justify-end'>
          <Ionicons
            name='close'
            color={COLORS.neutral[900]}
            size={normalized(22)}
          />
        </Pressable>
      </View>

      {children}
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  bottomSheet: {
    backgroundColor: COLORS.white[50],
    width: '100%',
    borderRadius: normalized(30),
    padding: normalized(10),
    position: 'absolute',
    zIndex: 1000,
    bottom: 0,

    overflow: 'hidden',
  },
});

export default CustomBottomSheet;
