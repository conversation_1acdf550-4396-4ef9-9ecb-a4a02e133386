import React, { useState, useCallback } from 'react';
import { TextInput, View, StyleSheet, Text } from 'react-native';
import CustomText from './CustomText';
import useLanguage from '@/hooks/useLanguage';
import { COLORS } from '@/constants/Theme';

interface ICustomPhoneInputProps {
  countryCode: string;
  phoneNumber: string;
  onCountryCodeChange: (code: string) => void;
  onPhoneNumberChange: (number: string) => void;
  otherStyles?: string;
  title?: string;
  placeholder?: string;
  value?: string;
  error?: string;
  isOptional?: boolean;
}

const CustomPhoneInput = (props: ICustomPhoneInputProps) => {
  const [isFocused, setIsFocused] = useState<boolean>(false);
  const { isArabic } = useLanguage();

  const formatPhoneNumber = useCallback((text: string) => {
    // Remove all non-digit characters
    const cleaned = text.replace(/\D/g, '');
    
    // Format the number with spaces
    let formatted = '';
    for (let i = 0; i < cleaned.length; i++) {
      if (i > 0 && i % 3 === 0) {
        formatted += ' ';
      }
      formatted += cleaned[i];
    }
    
    return formatted;
  }, []);

  const handlePhoneNumberChange = useCallback((text: string) => {
    const formatted = formatPhoneNumber(text);
    props.onPhoneNumberChange(formatted);
  }, [formatPhoneNumber, props.onPhoneNumberChange]);

  return (
    <View className="space-y-2">
      <View className="flex-row items-center mb-1.5">
        <CustomText className="text-h5 text-white-50 font-semibold">
          {props.title}
        </CustomText>
        {props.isOptional && (
          <CustomText className="text-neutral-300 ml-1">(optional)</CustomText>
        )}
      </View>
      <View
        className={`
          flex-row items-center rounded-xl border px-4 py-3.5
          ${props.error ? 'border-danger bg-accent-300/20' : 'bg-white-50/10'}
          ${isFocused ? 'border-secondary-300' : 'border-transparent'}
          ${!isArabic ? 'flex-row' : 'flex-row-reverse'}
        `}
      >
        {/* Country Code Input */}
        <TextInput
          className="text-base text-white-50 font-medium w-16"
          value={props.countryCode}
          onChangeText={props.onCountryCodeChange}
          placeholder="+966"
          placeholderTextColor={COLORS.white[400]}
          keyboardType="phone-pad"
          maxLength={5}
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
        <View className="h-6 w-[1px] bg-neutral-300 mx-3" />
        {/* Phone Number Input */}
        <TextInput
          className="text-base text-white-50 font-medium flex-1"
          value={props.phoneNumber}
          onChangeText={handlePhoneNumberChange}
          placeholder="************"
          placeholderTextColor={COLORS.white[400]}
          keyboardType="phone-pad"
          maxLength={12} // 9 digits + 2 spaces
          onFocus={() => setIsFocused(true)}
          onBlur={() => setIsFocused(false)}
        />
      </View>
      {/* Error Message */}
      {props.error && (
        <CustomText className="text-danger text-sm mt-1.5 px-1">
          {props.error}
        </CustomText>
      )}
    </View>
  );
};

export default CustomPhoneInput;
