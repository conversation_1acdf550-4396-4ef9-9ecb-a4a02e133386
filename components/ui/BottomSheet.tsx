import { StyleSheet, View } from 'react-native';
import React, { ReactNode, useImperativeHandle, forwardRef } from 'react';
import { Gesture, GestureDetector } from 'react-native-gesture-handler';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withSpring,
} from 'react-native-reanimated';
import { COLORS, SCREEN_HEIGHT } from '@/constants/Theme';

const MIN_TRANSLATE_Y = -SCREEN_HEIGHT + 300; // Max position upwards
const MAX_TRANSLATE_Y = SCREEN_HEIGHT - 50; // Prevent from going too low

export type BottomSheetRef = {
  open: (y?: number) => void; // Optional y value to set a custom position
  close: () => void;
};

const BottomSheet = forwardRef(({ children }: { children: ReactNode }, ref) => {
  const translateY = useSharedValue(MAX_TRANSLATE_Y); // Start at lowest allowed position
  const context = useSharedValue({ y: 0 });

  const gesture = Gesture.Pan()
    .onStart(() => {
      context.value = { y: translateY.value };
    })
    .onUpdate((event) => {
      translateY.value = Math.min(
        Math.max(event.translationY + context.value.y, MIN_TRANSLATE_Y),
        MAX_TRANSLATE_Y,
      );
    })
    .onEnd((event) => {
      if (event.translationY > 150) {
        translateY.value = withSpring(MAX_TRANSLATE_Y, { damping: 20 }); // Close
      } else {
        translateY.value = withSpring(MIN_TRANSLATE_Y, { damping: 20 }); // Open
      }
    });

  const rBottomSheetStyle = useAnimatedStyle(() => ({
    transform: [{ translateY: translateY.value }],
  }));

  useImperativeHandle(ref, () => ({
    open: (y = MIN_TRANSLATE_Y) => {
      // Ensure y is within bounds
      translateY.value = withSpring(
        Math.min(Math.max(y, MIN_TRANSLATE_Y), MAX_TRANSLATE_Y),
        {
          damping: 20,
        },
      );
    },
    close: () => {
      translateY.value = withSpring(MAX_TRANSLATE_Y, { damping: 20 });
    },
  }));

  return (
    <GestureDetector gesture={gesture}>
      <Animated.View style={[styles.bottomSheetContainer, rBottomSheetStyle]}>
        <View style={styles.line} />
        {children}
      </Animated.View>
    </GestureDetector>
  );
});

export default BottomSheet;

const styles = StyleSheet.create({
  bottomSheetContainer: {
    height: SCREEN_HEIGHT,
    width: '100%',
    backgroundColor: COLORS.white[50],
    position: 'absolute',
    top: SCREEN_HEIGHT,
    borderTopLeftRadius: 25,
    borderTopRightRadius: 25,
    zIndex: 10000,
  },
  line: {
    width: 75,
    height: 4,
    backgroundColor: COLORS.gray_colors[200],
    alignSelf: 'center',
    marginVertical: 15,
    borderRadius: 2,
  },
});
