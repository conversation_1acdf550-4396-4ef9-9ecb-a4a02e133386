import React, { memo } from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  TouchableWithoutFeedback,
  Keyboard,
  ViewStyle,
  StyleSheet,
} from 'react-native';
import { ScrollView } from 'react-native-virtualized-view';
interface KeyboardAvoidingWrapperProps {
  children: React.ReactNode;
  behaviorIOS?: 'height' | 'position' | 'padding';
  behaviorAndroid?: 'height' | 'position' | 'padding';
  keyboardVerticalOffset?: number;
  contentContainerStyle?: ViewStyle;
}

const KeyboardAvoidingWrapper: React.FC<KeyboardAvoidingWrapperProps> = ({
  children,
  behaviorIOS = 'padding',
  behaviorAndroid = undefined,
  keyboardVerticalOffset = Platform.OS === 'ios' ? 16 : 0,
  contentContainerStyle = {},
}) => {
  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? behaviorIOS : behaviorAndroid}
      keyboardVerticalOffset={keyboardVerticalOffset}
    >
      <TouchableWithoutFeedback onPress={Keyboard.dismiss} accessible={false}>
        <ScrollView
          contentContainerStyle={[
            styles.scrollContainer,
            contentContainerStyle,
          ]}
          keyboardShouldPersistTaps='handled'
          showsVerticalScrollIndicator={false}
        >
          {children}
        </ScrollView>
      </TouchableWithoutFeedback>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContainer: {
    flexGrow: 1,
  },
});

export default memo(KeyboardAvoidingWrapper);
