import { TextInputProps, TextStyle, View, ViewStyle } from 'react-native';
import React, { memo } from 'react';
import { TextInput } from 'react-native-gesture-handler';
import { Controller, Control } from 'react-hook-form';
import { COLORS } from '@/constants/Theme';
import CustomText from './CustomText';

export type TextAreaTypes = {
  inputProps?: TextInputProps;
  label?: string;
  labelStyles?: TextStyle;
  containerStyles?: ViewStyle;
  error?: string;
  control?: Control<any>; // react-hook-form control (optional)
  name?: string; // Field name for react-hook-form
};

const CustomTextArea = ({ control, name, error, ...props }: TextAreaTypes) => {
  // If control and name are provided, use react-hook-form's Controller
  if (control && name) {
    return (
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <View>
            {props.label && (
              <CustomText
                className='text-neutral-100 mb-1 text-h4'
                style={props.labelStyles}
              >
                {props.label}
              </CustomText>
            )}

            <View
              className='h-[7.625rem] bg-neutral-100/10 rounded-xl px-2 py-3'
              style={props.containerStyles}
            >
              <TextInput
                placeholder='Add description'
                placeholderTextColor={COLORS.neutral[400]}
                className='flex-1 h-full px-2 text-white-50 font-poppins'
                multiline
                value={field.value}
                onChangeText={field.onChange}
                {...props.inputProps}
              />
            </View>

            {error && <CustomText className='text-danger'>{error}</CustomText>}
          </View>
        )}
      />
    );
  }

  // If react-hook-form is not used, fallback to uncontrolled usage
  return (
    <View>
      {props.label && (
        <CustomText
          className='text-neutral-100 mb-1 text-h4'
          style={props.labelStyles}
        >
          {props.label}
        </CustomText>
      )}

      <View
        className='h-[7.625rem] bg-neutral-100/10 rounded-xl px-2 py-3'
        style={props.containerStyles}
      >
        <TextInput
          placeholder='Add description'
          placeholderTextColor={COLORS.neutral[400]}
          className='flex-1 h-full px-2 text-white-50 font-poppins'
          multiline
          {...props.inputProps}
        />
      </View>

      {error && <CustomText className='text-danger'>{error}</CustomText>}
    </View>
  );
};

export default memo(CustomTextArea);
