import React, { memo, useState, useCallback } from 'react';
import { View, Image, ImageProps, ActivityIndicator } from 'react-native';
import { COLORS } from '@/constants/Theme';

interface OptimizedImageProps extends Omit<ImageProps, 'source'> {
  source: { uri: string } | number;
  placeholder?: React.ReactNode;
  fallback?: React.ReactNode;
  showLoader?: boolean;
  resizeMode?: 'cover' | 'contain' | 'stretch' | 'repeat' | 'center';
  priority?: 'low' | 'normal' | 'high';
  lazy?: boolean;
}

/**
 * Performance-optimized Image component with:
 * - Lazy loading
 * - Error handling
 * - Loading states
 * - Memory optimization
 * - Progressive loading
 */
const OptimizedImage: React.FC<OptimizedImageProps> = ({
  source,
  placeholder,
  fallback,
  showLoader = true,
  resizeMode = 'cover',
  priority = 'normal',
  lazy = false,
  style,
  ...props
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [shouldLoad, setShouldLoad] = useState(!lazy);

  const handleLoadStart = useCallback(() => {
    setIsLoading(true);
    setHasError(false);
  }, []);

  const handleLoadEnd = useCallback(() => {
    setIsLoading(false);
  }, []);

  const handleError = useCallback(() => {
    setIsLoading(false);
    setHasError(true);
  }, []);

  const handleLayout = useCallback(() => {
    if (lazy && !shouldLoad) {
      setShouldLoad(true);
    }
  }, [lazy, shouldLoad]);

  // Don't render image if lazy loading and not ready
  if (lazy && !shouldLoad) {
    return (
      <View style={style} onLayout={handleLayout}>
        {placeholder || (
          <View style={[style, { backgroundColor: COLORS.neutral[800] }]} />
        )}
      </View>
    );
  }

  // Show fallback if error occurred
  if (hasError) {
    return (
      <View style={style}>
        {fallback || (
          <View style={[style, { backgroundColor: COLORS.neutral[700] }]} />
        )}
      </View>
    );
  }

  return (
    <View style={style}>
      <Image
        source={source}
        style={style}
        resizeMode={resizeMode}
        onLoadStart={handleLoadStart}
        onLoadEnd={handleLoadEnd}
        onError={handleError}
        {...props}
      />
      
      {/* Loading indicator */}
      {isLoading && showLoader && (
        <View
          style={[
            style,
            {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              justifyContent: 'center',
              alignItems: 'center',
              backgroundColor: COLORS.neutral[800],
            },
          ]}
        >
          <ActivityIndicator size="small" color={COLORS.secondary[300]} />
        </View>
      )}
      
      {/* Placeholder while loading */}
      {isLoading && placeholder && (
        <View
          style={[
            style,
            {
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
            },
          ]}
        >
          {placeholder}
        </View>
      )}
    </View>
  );
};

export default memo(OptimizedImage);
