import { FC, memo } from 'react';
import CustomText from './CustomText';
import { View } from 'react-native';
import EmptyCloud from '../../assets/icons/empty-cloud.svg';
import CustomButton from './buttons/CustomButton';
import { normalized } from '@/constants/Theme';
import { SvgProps } from 'react-native-svg';

interface IEmptyList {
  title?: string;
  description?: string;
  isError?: boolean;
  emptyIcon?: FC<SvgProps>;
  onRefreshPress?: () => void;
  errorTitle?: string;
  errorDescription?: string;
  errorIcon?: FC<SvgProps>;
  buttonTitle?: string;
  buttonOnPress?: () => void;
  errorButtonTitle?: string;
  containerStyle?: string;
}

const EmptyList = memo((props: IEmptyList) => {
  const baseContainerStyle = 'justify-center items-center flex-1 py-8 px-4';
  const containerStyle = props.containerStyle
    ? `${baseContainerStyle} ${props.containerStyle}`
    : baseContainerStyle;

  return !props.isError ? (
    <View className={containerStyle}>
      {props.emptyIcon ? (
        <View className='mb-4'>
          <props.emptyIcon />
        </View>
      ) : null}
      {props.title && (
        <CustomText className='text-h3 text-white-50 font-bold mb-2'>
          {props.title}
        </CustomText>
      )}
      <CustomText className='text-body2 text-neutral-300  font-semibold max-w-[80%] text-center mb-4'>
        {props.description}
      </CustomText>
      {props.buttonTitle && (
        <CustomButton
          style={{ borderRadius: normalized(5) }}
          variant='secondary'
          className='px-5 py-3 rounded-sm'
          onPress={props.buttonOnPress}
        >
          {props.buttonTitle}
        </CustomButton>
      )}
    </View>
  ) : (
    <View className={containerStyle}>
      <View className='mb-4'>
        {props.errorIcon ? <props.errorIcon /> : <EmptyCloud />}
      </View>
      <CustomText className='text-h5 text-white-50 font-bold mb-2'>
        {props.errorTitle}
      </CustomText>
      <CustomText className='text-body1 text-neutral-300 font-bold max-w-[90%] text-center mb-4'>
        {props.errorDescription}
      </CustomText>
      <CustomButton
        style={{ borderRadius: normalized(5) }}
        variant='secondary'
        className='px-5 py-3 rounded-sm'
        onPress={props.onRefreshPress}
      >
        {props.errorButtonTitle ? props.errorButtonTitle : 'Refresh Now'}
      </CustomButton>
    </View>
  );
});

export default memo(EmptyList);
