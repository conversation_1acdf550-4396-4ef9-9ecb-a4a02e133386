import { useRouter } from 'expo-router';
import { ReactNode, useCallback } from 'react';
import { TouchableOpacity } from 'react-native';

type Props = {
  children: ReactNode;
  user: {
    _id: string;
    displayName: string;
    username?: string;
    image?: string;
    isActive?: boolean;
  };
};

const ChatButtonLink: React.FC<Props> = (props) => {
  const route = useRouter();

  const handleRoutePush = useCallback(() => {
    route.push({
      pathname: '/(protected)/(tabs)/messages',
      params: {
        id: props.user._id,
        user: JSON.stringify(props.user),
      },
    });
  }, [route]);
  return (
    <TouchableOpacity onPress={handleRoutePush}>
      {props.children}
    </TouchableOpacity>
  );
};

export default ChatButtonLink;
