import React, { memo } from 'react';
import {
  StyleSheet,
  View,
  ImageSourcePropType,
  TouchableOpacity,
} from 'react-native';
import CustomText from './CustomText';
import { Image } from 'expo-image';
import { COLORS, normalized } from '@/constants/Theme';

interface AvatarProps {
  size?: number;
  source?: ImageSourcePropType;
  text?: string;
  backgroundColor?: string;
  textColor?: string;
  onPress?: () => void;
  borderColor?: string;
  borderWidth?: number;
}

const Avatar = ({
  size = 40, // Smaller default size
  source,
  text = '',
  backgroundColor = COLORS.primary[50],
  textColor = COLORS.white[50],
  onPress,
  borderColor,
  borderWidth = 0,
}: AvatarProps) => {
  // Check if image source is valid
  const isValidImage =
    source &&
    typeof source === 'object' &&
    'uri' in source &&
    typeof source.uri === 'string' &&
    source.uri.trim().length > 0;

  // Get initial letter for text avatar
  const initial = text && text.length > 0 ? text[0].toUpperCase() : '?';

  // Create avatar content based on image availability
  const avatarContent = isValidImage ? (
    <Image
      source={source}
      style={[
        styles.image,
        {
          width: normalized(size),
          height: normalized(size),
          borderRadius: normalized(size / 2),
          borderWidth,
          borderColor,
          backgroundColor: '#rgba(122, 38, 177, 0.4)',
        },
      ]}
      transition={150}
      contentFit='cover'
      cachePolicy='memory-disk'
    />
  ) : (
    <View
      style={[
        styles.textContainer,
        {
          width: normalized(size),
          height: normalized(size),
          borderRadius: normalized(size / 2),
          backgroundColor,
          borderWidth,
          borderColor,
        },
      ]}
    >
      <CustomText
        style={[
          styles.text,
          {
            color: textColor,
            fontSize: normalized(size / 2.5),
          },
        ]}
      >
        {initial}
      </CustomText>
    </View>
  );

  // Wrap in TouchableOpacity if onPress is provided
  if (onPress) {
    return (
      <TouchableOpacity
        activeOpacity={0.7}
        onPress={onPress}
        style={styles.touchable}
        accessibilityRole='button'
      >
        {avatarContent}
      </TouchableOpacity>
    );
  }

  return avatarContent;
};

const styles = StyleSheet.create({
  image: {
    backgroundColor: 'transparent',
  },
  textContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  text: {
    fontWeight: '700',
    letterSpacing: -0.5,
  },
  touchable: {
    alignSelf: 'flex-start',
  },
});

export default memo(Avatar);
