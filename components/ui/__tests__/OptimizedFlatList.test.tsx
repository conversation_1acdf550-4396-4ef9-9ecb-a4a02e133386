import React, { useRef } from 'react';
import { render } from '@testing-library/react-native';
import { FlatList, Text, View } from 'react-native';
import OptimizedFlatList from '../OptimizedFlatList';

// Mock data for testing
const mockData = [
  { _id: '1', title: 'Item 1' },
  { _id: '2', title: 'Item 2' },
  { _id: '3', title: 'Item 3' },
];

// Mock render item component
const MockRenderItem = ({ item }: { item: { _id: string; title: string } }) => (
  <View testID={`item-${item._id}`}>
    <Text>{item.title}</Text>
  </View>
);

describe('OptimizedFlatList', () => {
  it('should render correctly with basic props', () => {
    const { getByTestId } = render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        testID="optimized-flatlist"
      />
    );

    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });

  it('should render items correctly', () => {
    const { getByTestId } = render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        testID="optimized-flatlist"
      />
    );

    // Check if items are rendered
    expect(getByTestId('item-1')).toBeTruthy();
    expect(getByTestId('item-2')).toBeTruthy();
    expect(getByTestId('item-3')).toBeTruthy();
  });

  it('should forward ref correctly', () => {
    const TestComponent = () => {
      const flatListRef = useRef<FlatList<typeof mockData[0]>>(null);

      return (
        <OptimizedFlatList
          ref={flatListRef}
          data={mockData}
          renderItem={({ item }) => <MockRenderItem item={item} />}
          testID="optimized-flatlist"
        />
      );
    };

    const { getByTestId } = render(<TestComponent />);
    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });

  it('should apply performance optimizations when itemHeight is provided', () => {
    const { getByTestId } = render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        itemHeight={100}
        testID="optimized-flatlist"
      />
    );

    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });

  it('should handle empty data gracefully', () => {
    const { getByTestId } = render(
      <OptimizedFlatList
        data={[]}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        testID="optimized-flatlist"
      />
    );

    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });

  it('should use custom keyExtractor when provided', () => {
    const customKeyExtractor = jest.fn((item, index) => `custom-${item._id}-${index}`);

    render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        keyExtractor={customKeyExtractor}
        testID="optimized-flatlist"
      />
    );

    expect(customKeyExtractor).toHaveBeenCalled();
  });

  it('should handle virtualization settings', () => {
    const { getByTestId } = render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        enableVirtualization={false}
        testID="optimized-flatlist"
      />
    );

    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });

  it('should pass through additional FlatList props', () => {
    const onEndReached = jest.fn();
    const onRefresh = jest.fn();

    const { getByTestId } = render(
      <OptimizedFlatList
        data={mockData}
        renderItem={({ item }) => <MockRenderItem item={item} />}
        onEndReached={onEndReached}
        onRefresh={onRefresh}
        refreshing={false}
        testID="optimized-flatlist"
      />
    );

    expect(getByTestId('optimized-flatlist')).toBeTruthy();
  });
});
