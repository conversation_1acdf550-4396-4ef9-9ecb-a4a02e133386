import {
  TextInput,
  TextInputProps,
  TouchableOpacity,
  View,
} from 'react-native';
import React, { useState, useEffect } from 'react';
import { COLORS, normalized } from '@/constants/Theme';
import { APP_Icons } from '@/constants/Images';
import { ImagePickerAsset } from 'expo-image-picker';
import Loading from '@/layouts/Loading';

export type commentInputProps = {
  pickImage?: () => Promise<void>;
  images?: ImagePickerAsset[];
  inputProps?: TextInputProps;
  handleDeleteImage?: (img: ImagePickerAsset) => void;
  isLoading?: boolean;
  submitLabel?: string;
  handleSubmit?: (e: string | undefined) => void;
  hasCancelButton?: boolean;
  handleCancel?: () => void;
};
const CommentInput = (props: commentInputProps) => {
  // const { pickImage, result, setResult } = useUploadFromMedia({
  //   imagePickOptions: {
  //     mediaTypes: ['images'],
  //     selectionLimit: 1,
  //   },
  // })

  const [focused, setFocused] = useState(false);
  const [text, setText] = useState(props.inputProps?.value || '');
  const isInputDisabled = !text || text.trim() === '';

  // Update text when props change
  useEffect(() => {
    if (props.inputProps?.value !== undefined) {
      setText(props.inputProps.value);
    }
  }, [props.inputProps?.value]);

  const onSubmit = () => {
    if (!text || text.trim() === '') return;

    props.handleSubmit && props.handleSubmit(text);
    setText(''); // Clear input immediately after submission
  };

  return (
    <View className='w-full'>
      <View className='flex-row items-center gap-3 mt-3'>
        <View
          className={`flex-row items-center bg-white-50/10 -3 flex-1 p-3.5 rounded-2xl border`}
          style={{ borderColor: focused ? '#BFBFBF' : COLORS.neutral[500] }}
        >
          <TextInput
            placeholderTextColor={COLORS.neutral[300]}
            className='flex-1 px-2 font-poppins text-white-50'
            onFocus={() => {
              props.inputProps?.onFocus && props.inputProps?.onFocus;
              setFocused(true);
            }}
            onBlur={() => {
              props.inputProps?.onBlur && props.inputProps?.onBlur;
              setFocused(false);
            }}
            value={text}
            onChangeText={setText}
            {...props.inputProps}
          />

          {/* {props.pickImage && (
            <TouchableOpacity onPress={pickImage}>
              <APP_Icons.GalleryIcon
                width={normalized(18)}
                height={normalized(18)}
              />
            </TouchableOpacity>
          )} */}
        </View>
        <TouchableOpacity
          disabled={isInputDisabled}
          onPress={onSubmit}
          className='justify-center items-center w-[50px] h-[42px] rounded-full bg-secondary-300'
        >
          {props.isLoading ? (
            <Loading isLoading={props.isLoading} />
          ) : (
            <APP_Icons.MessageSend
              width={normalized(18)}
              height={normalized(18)}
            />
          )}
        </TouchableOpacity>
      </View>

      {/* {result && (
        <FlatList
          horizontal
          style={styles.imagesContainer}
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={{ gap: _spacing }}
          data={result || []}
          keyExtractor={(item, index) => item.uri || String(index)}
          renderItem={({ item }) => (
            <View className="relative">
              <Image source={{ uri: item.uri }} style={styles.image} />
              <TouchableOpacity
                className="absolute -bottom-3 -right-1.5 bg-neutral-100 rounded-full"
                onPress={() =>
                  props.handleDeleteImage && props.handleDeleteImage(item)
                }
              >
                <Ionicons
                  name="close"
                  size={normalized(18)}
                  color={COLORS.neutral[900]}
                />
              </TouchableOpacity>
            </View>
          )}
        />
      )} */}
    </View>
  );
};

export default CommentInput;

// const styles = StyleSheet.create({
//   imagesContainer: {
//     paddingBlock: _spacing,
//   },
//   image: {
//     width: normalized(70),
//     height: normalized(70),
//     borderRadius: 10,
//   },
// })
