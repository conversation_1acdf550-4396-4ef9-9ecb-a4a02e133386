import React, { memo } from 'react';
import { Pressable, StyleSheet } from 'react-native';
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
} from 'react-native-reanimated';
import { Controller, Control } from 'react-hook-form';
import { COLORS } from '@/constants/Theme';

interface ICustomSwitchTypes {
  onPress: (v?: boolean) => void;
  active: boolean;
  control?: Control<any>; // react-hook-form control (optional)
  name?: string; // Field name for react-hook-form
}

const CustomSwitch = ({
  control,
  name,
  active,
  onPress,
}: ICustomSwitchTypes) => {
  const translateX = useSharedValue(active ? 22 : 2);

  // Animate the switch thumb when toggling
  const thumbStyle = useAnimatedStyle(() => ({
    transform: [{ translateX: withTiming(active ? 22 : 2, { duration: 200 }) }],
  }));

  // If `control` and `name` are provided, use React Hook Form's `Controller`
  if (control && name) {
    return (
      <Controller
        control={control}
        name={name}
        render={({ field }) => (
          <Pressable
            style={[styles.switchContainer, field.value && styles.activeTrack]}
            onPress={() => {
              field.onChange(!field.value); // Toggle switch value
              onPress && onPress(!field.value); // Optional callback
            }}
          >
            <Animated.View style={[styles.thumb, thumbStyle]} />
          </Pressable>
        )}
      />
    );
  }

  // If React Hook Form is not used, fallback to uncontrolled usage
  return (
    <Pressable
      style={[styles.switchContainer, active && styles.activeTrack]}
      onPress={() => {
        onPress && onPress(!active); // Toggle active state
      }}
    >
      <Animated.View style={[styles.thumb, thumbStyle]} />
    </Pressable>
  );
};

const styles = StyleSheet.create({
  switchContainer: {
    width: 50,
    height: 28,
    borderRadius: 16,
    backgroundColor: COLORS.neutral[300], // Default inactive track
    justifyContent: 'center',
    paddingHorizontal: 2,
  },
  activeTrack: {
    backgroundColor: COLORS.secondary[300], // Active color
  },
  thumb: {
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: '#FFF', // Thumb color
    position: 'absolute',
  },
});

export default memo(CustomSwitch);
