import { COLORS } from '@/constants/Theme';
import { LinearGradient } from 'expo-linear-gradient';
import React, { memo } from 'react';
import { StyleSheet, TouchableOpacity, View } from 'react-native';
import Avatar from './Avatar';

interface IGradientProps {
  onPress?: () => void;
  colors: readonly [string, string, ...string[]];
  image: string;
  size?: number; // total outer size including gradient
  gap?: number; // thickness of the border
  backgroundColor?: string;
  gapBackgroundColor?: string;
  padding?: number; // padding inside the gradient before the gap
}

const GradientCircleButton = ({
  onPress,
  colors,
  image,
  size = 72,
  gap = 2,
  padding = 3,
  backgroundColor = COLORS.neutral[300],
  gapBackgroundColor = COLORS.primary[200],
}: IGradientProps) => {
  const borderRadius = size / 2;
  const innerSize = size - gap - padding * 2; // size minus padding and border thickness
  const avatarSize = innerSize * 0.9; // 90% of inner view for Avatar

  return (
    <TouchableOpacity onPress={onPress} activeOpacity={0.8}>
      <LinearGradient
        colors={colors}
        start={{ x: 0.0, y: 1.0 }}
        end={{ x: 1.0, y: 1.0 }}
        style={[
          styles.gradientBorder,
          {
            width: size,
            height: size,
            padding,
            borderRadius,
            backgroundColor,
          },
        ]}
      >
        <View
          style={[
            styles.innerCircle,
            {
              width: innerSize,
              height: innerSize,
              borderRadius: innerSize / 2,
              backgroundColor: gapBackgroundColor,
            },
          ]}
        >
          <Avatar source={{ uri: image }} size={avatarSize} />
        </View>
      </LinearGradient>
    </TouchableOpacity>
  );
};

export default memo(GradientCircleButton);

const styles = StyleSheet.create({
  gradientBorder: {
    justifyContent: 'center',
    alignItems: 'center',
    overflow: 'hidden',
  },
  innerCircle: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});
