import { StyleSheet, Text, View } from 'react-native';
import React, { memo } from 'react';
import { OtpInput, OtpInputProps, OtpInputRef } from 'react-native-otp-entry';
import { COLORS, FONT, normalized } from '@/constants/Theme';

interface IOTPInputsTypes extends OtpInputProps {
  error?: string;
  isValid?: boolean;
}

const OTPInputs = React.forwardRef<OtpInputRef, IOTPInputsTypes>(
  (
    { error, isValid, numberOfDigits = 4, secureTextEntry = false, ...props },
    ref,
  ) => {
    return (
      <View style={styles.container}>
        <OtpInput
          {...props}
          ref={ref}
          numberOfDigits={numberOfDigits}
          placeholder='.'
          secureTextEntry={secureTextEntry}
          theme={{
            containerStyle: styles.container,
            pinCodeContainerStyle: StyleSheet.flatten([
              styles.pinCodeContainer,
              error ? styles.errorBorder : null,
              isValid ? styles.successBorder : null,
            ]),
            pinCodeTextStyle: styles.pinCodeText,
            focusStickStyle: styles.focusStick,
            focusedPinCodeContainerStyle: StyleSheet.flatten([
              styles.activePinCodeContainer,
              isValid ? styles.successBorder : null,
              error ? styles.errorBorder : null,
            ]),
            placeholderTextStyle: styles.placeholderText,
            filledPinCodeContainerStyle: styles.filledPinCodeContainer,
            disabledPinCodeContainerStyle: styles.disabledPinCodeContainer,
          }}
        />
      </View>
    );
  },
);

export default memo(OTPInputs);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'space-around',
  },
  pinCodeContainer: {
    width: normalized(45),
    height: normalized(45),
    borderColor: 'transparent',
    borderWidth: 1,
  },
  errorBorder: {
    borderColor: COLORS.danger,
  },
  successBorder: {
    borderColor: COLORS.success[400],
  },
  pinCodeText: {
    color: COLORS.white[50],
  },
  focusStick: {
    backgroundColor: COLORS.secondary[300],
  },
  activePinCodeContainer: {
    borderColor: COLORS.secondary[300],
  },
  placeholderText: {
    fontFamily: FONT.fontFamily,
  },
  filledPinCodeContainer: {
    backgroundColor: 'rgba(255,255,255,0.2)',
  },
  disabledPinCodeContainer: {},
  errorText: {
    color: COLORS.danger,
    fontSize: normalized(10),
    marginTop: normalized(5),
  },
});
