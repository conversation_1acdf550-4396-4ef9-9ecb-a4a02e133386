import React, { useCallback, useMemo, useState } from 'react';
import { FlatList, View, StyleSheet, TouchableOpacity } from 'react-native';
import ReviewsComponent from '../ReviewsComponent';
import { IReviewItem } from '@/types';
import useAddExploreReview from '@/hooks/exploreHooks/useAddExploreReview';
import { v4 as uuidv4 } from 'uuid';
import { useSession } from '@/context/AuthContext';
import { useForm } from 'react-hook-form';
import ShowMore from '../showMore';
import { t } from 'i18next';
import { timeAgo } from '@/utils/formatDates';
import CustomInput from '../ui/CustomInput';
import { APP_Icons } from '@/constants/Images';

type IExploreReviewsList = {
  reviews: (IReviewItem & {
    user: {
      _id: string;
      displayName: string;
      image?: string;
    };
  })[];
  setReviews: React.Dispatch<
    React.SetStateAction<IExploreReviewsList['reviews']>
  >;
  exploreId: string;
};

const ExploreReviewsList = ({
  reviews,
  setReviews,
  exploreId,
}: IExploreReviewsList) => {
  const { mutate, isPending } = useAddExploreReview();
  const { user } = useSession();
  const [showMore, setShowMore] = useState(false);

  const { control, handleSubmit, reset } = useForm({
    defaultValues: { review: '' },
  });

  const handleAddReview = useCallback(
    async (data: { review: string }) => {
      if (!data.review.trim()) return;

      const newCommentId = uuidv4();

      const newReview: IReviewItem & {
        user: {
          _id: string;
          displayName: string;
          image?: string;
        };
      } = {
        _id: newCommentId,
        review: data.review,
        createdAt: new Date(),
        userId: {
          _id: user?._id || '',
          username: user?.username || '',
          displayName: user?.displayName || 'Unknown',
          image: user?.image,
        },
        user: {
          displayName: user?.displayName || 'Unknown',
          image: user?.image,
          _id: user?._id || '',
        },
      };

      setReviews((prev) => [newReview, ...prev]);

      mutate(
        { exploreId, review: data.review },
        {
          onSuccess: () => reset(),
          onError: () => {
            setReviews((prev) => prev.filter((v) => v._id !== newCommentId));
          },
        },
      );
    },
    [exploreId, mutate, reset, setReviews, user],
  );

  const renderedReviews = useMemo(() => {
    if (!reviews) return [];
    return reviews.length > 3 && !showMore ? reviews.slice(0, 3) : reviews;
  }, [reviews, showMore]);

  return (
    <View>
      <CustomInput
        control={control}
        name='review'
        placeholder={t('add-your-review')}
        rightIcon={
          <TouchableOpacity
            onPress={handleSubmit(handleAddReview)}
            className='bg-secondary-300 w-10 h-10 rounded-full justify-center items-center'
          >
            <APP_Icons.MessageSend width={18} height={18} />
          </TouchableOpacity>
        }
      />

      <FlatList
        data={renderedReviews}
        keyExtractor={(item) => item._id}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.listContainer}
        style={styles.listStyle}
        ItemSeparatorComponent={() => <View style={styles.separator} />}
        renderItem={({ item }) => (
          <ReviewsComponent
            description={item.review}
            // rate={Math.floor(Math.random() * 5)} // TODO: Replace with actual rating
            username={item.user?.displayName || 'Unknown'}
            avatar={item.user?.image || ''}
            createdAt={timeAgo(item.createdAt)}
            isCreator={item.user?._id === user?._id}
          />
        )}
      />

      {reviews.length > 3 && (
        <ShowMore
          title={
            renderedReviews.length > 3 ? t('show-less') : t('see-all-reviews')
          }
          isActive={showMore}
          onPress={() => setShowMore((prev) => !prev)}
          textProps={{ className: 'text-secondary-300 text-h3 px-3' }}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  listContainer: {
    gap: 20,
    paddingBottom: 20,
  },
  listStyle: {
    paddingVertical: 20,
  },
  separator: {
    borderBottomWidth: StyleSheet.hairlineWidth,
    borderBottomColor: 'rgba(200, 200, 200, 0.2)',
    marginVertical: 12,
  },
});

export default ExploreReviewsList;
