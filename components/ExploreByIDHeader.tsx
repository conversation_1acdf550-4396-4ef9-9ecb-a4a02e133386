import { StyleSheet, TouchableOpacity, View } from 'react-native';
import { ImageBackground } from 'expo-image';
import { LinearGradient } from 'expo-linear-gradient';
import GoBack from '@/layouts/GoBack';
import CustomText from './ui/CustomText';
import { APP_Icons } from '@/constants/Images';
import { FontAwesome } from '@expo/vector-icons';
import { COLORS, normalized, SCREEN_HEIGHT } from '@/constants/Theme';
import { memo, useMemo } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
type props = {
  coverImage: string;
  title: string;
  address: string;
  rating: { average: number; count: number };
  onShare: () => void;
  onGalleryPress: () => void;
};
const ExploreByIDHeader = (props: props) => {
  const inset = useSafeAreaInsets();

  const filteredAddress = useMemo(() => {
    if (!props.address) return ''; // Handle empty or undefined props.address
    return props.address.replace(/null|undefined|,/g, '').trim();
  }, [props.address]);

  return (
    <ImageBackground
      source={{
        uri: props.coverImage,
      }}
      style={[styles.image]}
      imageStyle={styles.imageContainer}
    >
      {/* gradient overlay */}
      <LinearGradient
        style={{ position: 'absolute', width: '100%', height: '100%' }}
        colors={['rgba(0,0,0,0.2)', 'rgba(0,0,0,0.4)', 'rgba(0,0,0,0.8)']}
      />
      <View style={[styles.goBack, { top: inset.top }]}>
        <GoBack />
        <TouchableOpacity
          className='bg-primary-200/60 px-3 py-1 rounded-3xl '
          onPress={props.onGalleryPress}
        >
          <APP_Icons.GalleryIcon
            width={normalized(22)}
            height={normalized(22)}
          />
        </TouchableOpacity>
      </View>

      {/* Bottom Content Inside Image */}
      <View style={styles.headerContainer}>
        <View className='flex-row items-center justify-between w-full px-3'>
          <CustomText
            className='text-white-50 font-semibold text-h1'
            numberOfLines={1}
          >
            {props.title}
          </CustomText>
          {/* <TouchableOpacity onPress={props.onShare}>
            <APP_Icons.ShareIcon width={normalized(22)} height={normalized(22)} />
          </TouchableOpacity> */}
        </View>

        {/* Location */}
        <View className='flex-row items-center gap-2 px-2.5'>
          <APP_Icons.LocationIcon
            width={normalized(18)}
            height={normalized(18)}
          />
          <CustomText className='text-neutral-100 text-h4'>
            {filteredAddress || 'No location provided'}
          </CustomText>
        </View>

        {/* Rate and image */}
        <View className='flex-row gap-3 px-3'>
          {/* <View className="flex-row items-center gap-2 bg-primary-200/60 px-3 py-1 rounded-2xl">
            <View className="flex-row items-center gap-1">
              <FontAwesome name="star" color={COLORS.starColor} size={normalized(20)} />
              <CustomText className="text-neutral-100 font-medium text-h3">
                {props.rating.average}
              </CustomText>
            </View>
            <CustomText className="text-neutral-100">({props.rating.count} Reviews)</CustomText>
          </View> */}
        </View>
      </View>
    </ImageBackground>
  );
};
const styles = StyleSheet.create({
  goBack: {
    position: 'absolute',
    flexDirection: 'row',
    justifyContent: 'space-between',
    flex: 1,
    width: '100%',
    padding: 10,
  },
  image: {
    width: '100%',
    height: SCREEN_HEIGHT / 3, // Sets the image height
    justifyContent: 'flex-end',
    position: 'relative',
  },
  imageContainer: {
    objectFit: 'cover',
    height: SCREEN_HEIGHT / 3,
  },
  headerContainer: {
    padding: normalized(10),
    zIndex: 30,
  },
});

export default memo(ExploreByIDHeader);
