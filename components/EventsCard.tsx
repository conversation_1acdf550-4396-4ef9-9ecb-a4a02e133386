import { StyleSheet, View } from 'react-native';
import React, { memo } from 'react';
import { normalized } from '@/constants/Theme';
import { APP_Icons } from '@/constants/Images';
import { Image } from 'expo-image';
import CustomText from './ui/CustomText';

interface IEventsCardProps {
  image: string;
  title: string;
  location: string;
  date: string;
}

const EventsCard = (props: IEventsCardProps) => {
  return (
    <View className='flex-1  gap-3 rounded-lg '>
      <Image source={{ uri: props.image }}
style={styles.imageStyles} />
      {/* Content */}
      <View className='gap-2'>
        {/* Title */}
        <CustomText
          className='text-white-50 text-h3 font-semibold'
          numberOfLines={1}
        >
          {props.title}
        </CustomText>

        {/* location and data  */}
        <View className='flex-row justify-between'>
          {/* Location view */}
          <View className='flex-row items-center gap-2'>
            <APP_Icons.LocationIcon
              width={normalized(16)}
              height={normalized(16)}
            />
            <CustomText className='font-semibold text-neutral-300 text-h-6'>
              {props.location}
            </CustomText>
          </View>
          {/* Calender View */}
          <View className='flex-row items-center gap-2'>
            <APP_Icons.CALENDER
              width={normalized(18)}
              height={normalized(18)}
            />
            <CustomText className='text-neutral-300 text-h-6'>
              {props.date}
            </CustomText>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(EventsCard);
const styles = StyleSheet.create({
  imageStyles: {
    height: normalized(150),
    objectFit: 'cover',
    backgroundColor: 'rgba(0,0,0,0.5)',
    borderRadius: normalized(10),
  },
  IconStyles: {
    width: normalized(18),
    height: normalized(18),
  },
});
