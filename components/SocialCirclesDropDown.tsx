import { StyleSheet, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import Avatar from './ui/Avatar';
import CustomDropDown from './ui/CustomDropDown';
import CustomText from './ui/CustomText';
import { FlatList } from 'react-native-gesture-handler';
import { ISocialCircle } from '@/types';

type ISocialCirclesDropDownProps = {
  data: ISocialCircle[];
  onSearch: (e: string) => void;
  searchValue: string;
  onSelect: (item: ISocialCircle) => void;
  selectedCircles: ISocialCircle[] | undefined;
  deleteSelectedCircle: (item: ISocialCircle) => void;
};

const _circleSize = 58;
const _strokeWidth = 3;

const SocialCircleItem = memo(
  ({
    item,
    deleteSelectedCircle,
  }: {
    item: ISocialCircle;
    deleteSelectedCircle: (item: ISocialCircle) => void;
  }) => {
    return (
      <View style={styles.container}>
        <TouchableOpacity
          onPress={() => deleteSelectedCircle(item)}
          style={styles.deleteCircle}
        >
          <Ionicons name='close'
size={22}
color={COLORS.neutral[700]} />
        </TouchableOpacity>
        <View style={styles.circleContainer}>
          <Avatar
            size={normalized(_circleSize)}
            source={{ uri: item.image }}
            text={item.name}
          />
        </View>
      </View>
    );
  },
);

const SocialCirclesDropDown = ({
  data,
  onSearch,
  searchValue,
  onSelect,
  selectedCircles,
  deleteSelectedCircle,
}: ISocialCirclesDropDownProps) => {
  return (
    <View>
      <CustomText style={styles.sectionTitle}>Add Social Circle</CustomText>
      <CustomDropDown
        keyExtractor={(item) => `circlesDropDown-${item._id}`}
        onChangeText={onSearch}
        searchQuery={searchValue}
        data={data}
        placeholder='Social Circles'
        onSelectItem={onSelect}
        renderItem={({ item }) => (
          <View className='flex-row items-center gap-2 flex-1'>
            <Avatar
              size={normalized(35)}
              source={{ uri: item.image }}
              text={item.name}
            />
            <CustomText className='text-white-50 text-h4 font-semibold'>
              {item.name}
            </CustomText>
          </View>
        )}
      />

      {selectedCircles && (
        <FlatList
          className='mt-4 py-3 max-h-[6rem]'
          horizontal
          contentContainerClassName='gap-3'
          showsHorizontalScrollIndicator={false}
          data={selectedCircles || []}
          keyExtractor={(item) => item._id}
          renderItem={({ item }) => (
            <SocialCircleItem
              deleteSelectedCircle={deleteSelectedCircle}
              item={item}
            />
          )}
        />
      )}
    </View>
  );
};

export default SocialCirclesDropDown;

const styles = StyleSheet.create({
  container: {
    width: _strokeWidth + _circleSize,
    height: _strokeWidth + _circleSize,
    position: 'relative',
  },

  circleContainer: {
    justifyContent: 'center',
    alignItems: 'center',
    borderWidth: _strokeWidth,
    width: _circleSize + _strokeWidth,
    height: _circleSize + _strokeWidth,
    borderColor: COLORS.secondary[300],
    borderRadius: _circleSize / 2,
    overflow: 'hidden',
  },
  sectionTitle: {
    color: COLORS.white[50],
    fontWeight: 'semibold',
    marginBottom: normalized(3),
    fontSize: normalized(16),
  },
  //   absolute border border-neutral-700 w-7 h-7 right-[2rem] top-[-0.5rem] z-10 bg-[#D9D9D9] items-center justify-center rounded-full
  deleteCircle: {
    position: 'absolute',
    width: _circleSize / 2.5,
    height: _circleSize / 2.5,
    borderWidth: 1,
    borderColor: COLORS.neutral[700],
    top: -8,
    right: 0,
    backgroundColor: '#D9D9D9',
    zIndex: 20,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: _circleSize / 2.5 / 2,
  },
});
