import { StyleSheet, View } from 'react-native';
import React, { memo, useMemo } from 'react';
import { normalized } from '@/constants/Theme';
import Avatar from './ui/Avatar';
import { Image } from 'expo-image';
import CustomText from './ui/CustomText';
import { APP_Icons } from '@/constants/Images';
import { LinearGradient } from 'expo-linear-gradient';
import { formattedCurrency } from '@/utils/common';

interface IActivityCardProps {
  title: string;
  price?: number;
  currency?: string;
  location: string;
  cover: any;
  members?: string[];
  day: string;
  month: string;
}

const ActivityCard = (props: IActivityCardProps) => {
  const memoFormatedCurrency = useMemo(() => {
    return formattedCurrency(props.currency || '');
  }, [props.currency]);
  return (
    <View className='flex-1  justify-between border-2 bg-primary-50/30  border-[#7A26B1] p-2 rounded-3xl'>
      {/* Image */}
      <View>
        <LinearGradient
          style={styles.gradient}
          colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.2)']}
        />
        {props.cover ? (
          <Image
            source={{ uri: props.cover }}
            style={styles.coverImage}
            contentFit='cover'
            transition={200}
            recyclingKey={`activity-cover-${props.day}-${props.month}`}
            cachePolicy='memory-disk'
            contentPosition='center'
          />
        ) : (
          <View className=' ' style={styles.coverImage} />
        )}
        {/* Date container */}
        <View className='bg-white-50/20 z-10 ' style={styles.dateContainer}>
          <CustomText className='text-h3  text-center text-white-50 font-bold'>
            {props.month}
          </CustomText>
          <CustomText className='text-h4  text-white-50  font-bold'>
            {props.day}
          </CustomText>
        </View>
      </View>
      {/* content */}
      <View className='  gap-1 '>
        <CustomText
          className='text-white-50 max-w-[80%]  font-semibold text-h2'
          numberOfLines={1}
        >
          {props.title}
        </CustomText>
        <View className='flex-row gap-2  items-center my-2'>
          <APP_Icons.LocationIcon width={normalized(18)} height={'100%'} />
          <CustomText
            className='text-neutral-200  font-light    text-h5'
            style={{ fontSize: normalized(14) }}
          >
            {props.location}
          </CustomText>
        </View>
        {/* Avatars section */}
        <View className='flex-row justify-between  items-center mb-2  px-2'>
          <View className='flex-row '>
            {props.members?.slice(0, 3)?.map((v, index) => (
              <View
                key={`member-${index}`}
                style={{ marginLeft: index === 0 ? 0 : normalized(-20) }}
              >
                <Avatar source={{ uri: v }} size={normalized(35)} />
              </View>
            ))}
            {props.members && props.members.length > 3 && (
              <View
                className='bg-secondary-300  rounded-full justify-center items-center -ml-5 '
                style={{ width: normalized(35), height: normalized(35) }}
              >
                <CustomText className='text-white-50  '>
                  {props.members.length - 3}+
                </CustomText>
              </View>
            )}
          </View>

          <View className='flex-row items-center gap-2'>
            <CustomText className='text-neutral-200 font-semibold'>
              {memoFormatedCurrency}
            </CustomText>
            <CustomText className='text-h2  font-bold text-white-50 justify-center items-center gap-1'>
              {props.price}
            </CustomText>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(ActivityCard);

const styles = StyleSheet.create({
  dateContainer: {
    width: normalized(55),
    height: normalized(55),
    padding: normalized(5),
    borderRadius: 150,
    justifyContent: 'center',
    alignItems: 'center',
    position: 'absolute',
    right: normalized(8),
    top: normalized(5),
  },
  avatarImage: {
    marginLeft: normalized(-10),
  },
  coverImage: {
    height: normalized(177),
    borderRadius: normalized(15),
    marginBottom: normalized(8),
    backgroundColor: 'rgba(0,0,0,0.2)',
  },
  gradient: {
    position: 'absolute',
    width: '100%',
    height: normalized(177),
    zIndex: 10,
    borderRadius: 10,
  },
});
