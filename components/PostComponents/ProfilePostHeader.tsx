import React, { memo } from 'react';
import CustomHeader from '@/components/ui/CustomHeader';

/**
 * Memoized header component for profile post screen
 * Simple header that doesn't change, so it's highly optimized
 */
const ProfilePostHeader: React.FC = memo(() => {
  return (
    <CustomHeader title='Post' />
  );
});

ProfilePostHeader.displayName = 'ProfilePostHeader';

export default ProfilePostHeader;
