import React, { memo, useMemo } from 'react';
import { View } from 'react-native';
import PostsCard from '@/components/PostsCard';
import OptimizedPostCommentList from '@/layouts/OptimizedPostCommentList';
import { timeAgo } from '@/utils/formatDates';
import { IPost } from '@/types';

interface ProfilePostCardProps {
  post: IPost;
  isLiked: boolean;
  isPostOwner: boolean;
  userId: string;
  handleToggleLike: () => void;
  handlePostActions: (action: string) => void;
  showImageModal: () => void;
  postId: string;
}

/**
 * Optimized ProfilePostCard component with memoized computed values
 * Prevents unnecessary re-renders and expensive computations
 */
const ProfilePostCard: React.FC<ProfilePostCardProps> = memo(({
  post,
  isLiked,
  isPostOwner,
  userId,
  handleToggleLike,
  handlePostActions,
  showImageModal,
  postId,
}) => {
  // Memoize computed values to prevent recalculation
  const computedValues = useMemo(() => ({
    createdAt: timeAgo(post.createdAt),
    likesCount: Number(post.likes.likesCount),
    commentsCount: Number(post.comments?.length || 0),
    coverImage: post.image?.[0] || '',
    location: post.visibility || '',
  }), [
    post.createdAt,
    post.likes.likesCount,
    post.comments?.length,
    post.image,
    post.visibility,
  ]);

  // Memoize post actions handler
  const handleSelectAction = useMemo(() => (action: string) => {
    handlePostActions(action);
  }, [handlePostActions]);

  return (
    <PostsCard
      isLiked={isLiked}
      cover={computedValues.coverImage}
      location={computedValues.location}
      variant='profile'
      title={post.content}
      createdAt={computedValues.createdAt}
      likesCount={computedValues.likesCount}
      commentsCount={computedValues.commentsCount}
      name={post.postedByUserId.displayName}
      avatar={post.postedByUserId.image}
      userId={post.postedByUserId._id}
      onLikePress={handleToggleLike}
      showImageModal={showImageModal}
      isPostOwner={isPostOwner}
      onSelect={handleSelectAction}
    >
      <View className='px-2 gap-3 flex-1'>
        <OptimizedPostCommentList postId={postId} isDark={true} />
      </View>
    </PostsCard>
  );
});

ProfilePostCard.displayName = 'ProfilePostCard';

export default ProfilePostCard;
