import React, { memo, useMemo } from 'react';
import { View } from 'react-native';
import PostsCard from '@/components/PostsCard';
import OptimizedPostCommentList from '@/layouts/OptimizedPostCommentList';
import { timeAgo } from '@/utils/formatDates';
import { IPost } from '@/types';

interface PostCardProps {
  post: IPost;
  isLiked: boolean;
  handleToggleLike: () => void;
  showImageModal: () => void;
  postId: string;
}

/**
 * Optimized PostCard component with memoized computed values
 * Prevents unnecessary re-renders and expensive computations
 */
const PostCard: React.FC<PostCardProps> = memo(({
  post,
  isLiked,
  handleToggleLike,
  showImageModal,
  postId,
}) => {
  // Memoize computed values to prevent recalculation
  const computedValues = useMemo(() => ({
    createdAt: timeAgo(post.createdAt),
    likesCount: Number(post.likes.likesCount),
    commentsCount: Number(post.comments?.length || 0),
    coverImage: post.image[0],
    userId: post.postedByUserId._id,
  }), [
    post.createdAt,
    post.likes.likesCount,
    post.comments?.length,
    post.image,
    post.postedByUserId._id,
  ]);

  return (
    <PostsCard
      name={post.content}
      title={post.content}
      images={post.image}
      userId={computedValues.userId}
      variant='circle'
      isLiked={isLiked}
      onLikePress={handleToggleLike}
      cover={computedValues.coverImage}
      showImageModal={showImageModal}
      createdAt={computedValues.createdAt}
      likesCount={computedValues.likesCount}
      commentsCount={computedValues.commentsCount}
    >
      {/* Comments List */}
      <View className='px-2'>
        <OptimizedPostCommentList postId={postId} isDark={false} />
      </View>
    </PostsCard>
  );
});

PostCard.displayName = 'PostCard';

export default PostCard;
