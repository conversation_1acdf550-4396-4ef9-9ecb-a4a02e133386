import React, { memo } from 'react';
import { View } from 'react-native';
import ModalGalleryList from '@/components/ui/ModalGalleryList';

interface PostModalProps {
  visible: boolean;
  images: string[];
  onClose: () => void;
}

/**
 * Memoized modal component for post images
 * Only re-renders when visibility or images change
 */
const PostModal: React.FC<PostModalProps> = memo(({
  visible,
  images,
  onClose,
}) => {
  if (!visible) return null;

  return (
    <View className='flex-1 absolute inset-0 bg-black'>
      <ModalGalleryList
        images={images || []}
        onClose={onClose}
        visible={visible}
      />
    </View>
  );
});

PostModal.displayName = 'PostModal';

export default PostModal;
