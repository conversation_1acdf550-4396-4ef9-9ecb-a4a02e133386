import React, { memo } from 'react';
import { View } from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import CustomHeader from '@/components/ui/CustomHeader';
import SocialCircleAdminHeader from '@/components/SocialCircleAdminHeader';

interface PostHeaderProps {
  isPostOwner: boolean;
  circleImage: string;
  circleName: string;
}

/**
 * Memoized header component for post screen
 * Prevents re-renders when props haven't changed
 */
const PostHeader: React.FC<PostHeaderProps> = memo(({
  isPostOwner,
  circleImage,
  circleName,
}) => {
  const inset = useSafeAreaInsets();

  return (
    <View className='pb-3' style={{ paddingTop: inset.top }}>
      {!isPostOwner && (
        <CustomHeader
          title='Post'
          textStyles={{ fontSize: 24 }}
          href='/(protected)/(tabs)/socialCircles'
        />
      )}

      {isPostOwner && (
        <SocialCircleAdminHeader
          image={circleImage}
          name={circleName || 'Public'}
          participantsNumber={0}
        />
      )}
    </View>
  );
});

PostHeader.displayName = 'PostHeader';

export default PostHeader;
