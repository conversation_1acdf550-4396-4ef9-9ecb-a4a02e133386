import React, { memo, useCallback } from 'react';
import ExploreAttractionCard from './ExploreAttractionCard';
import { Platform, Pressable, View } from 'react-native';
import EmptyList from './ui/EmptyListComponent';
import { IExploreItem } from '@/types';
import { Link } from 'expo-router';
import useGetExploreQuery from '@/hooks/exploreHooks/useGetExplore';
import useExploreStore from '@/stores/useExploreStore';
import useLocation from '@/hooks/useLocation';
import OptimizedFlatList from './ui/OptimizedFlatList';

const ExploreAttractionsList = () => {
  const { setItem } = useExploreStore();
  const { location } = useLocation();
  const { data, isLoading, refetch, isError } = useGetExploreQuery({
    page: 1,

    latitude: location?.latitude,
    longitude: location?.longitude,
  });

  const handleDispatchExploreItem = useCallback((item: IExploreItem) => {
    setItem(item);
  }, []);

  return (
    <OptimizedFlatList
      data={data?.data || []}
      className={` flex-1 ${Platform.OS === 'ios' ? 'pb-10' : 'pb-3'}`}
      ListEmptyComponent={
        <EmptyList
          title='No Attractions Found!'
          description="We couldn't find any attractions near you."
          onRefreshPress={refetch}
          isError={isError}
        />
      }
      keyExtractor={(item) => item._id}
      horizontal={data?.data && data.data.length > 0}
      showsHorizontalScrollIndicator={false}
      renderItem={({ item }) => (
        <Link href={`/(protected)/explore/${item._id}`} asChild>
          <Pressable onPress={() => handleDispatchExploreItem(item)}>
            <ExploreAttractionCard title={item.name} image={item.coverImage} />
          </Pressable>
        </Link>
      )}
      ItemSeparatorComponent={() => <View className='mx-1' />}
    />
  );
};

export default memo(ExploreAttractionsList);
