import { TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import { APP_Icons } from '@/constants/Images';
import { normalized } from '@/constants/Theme';
interface IFilterButtonProps {
  onPress: () => void;
}
const FilterButton = ({ onPress }: IFilterButtonProps) => {
  return (
    <TouchableOpacity
      onPress={onPress}
      className='bg-white-50/10 px-2 py-3 rounded-3xl items-center justify-center  w-16 h-16 '
    >
      <APP_Icons.FilterAltIcon width={normalized(24)}
height={normalized(24)} />
    </TouchableOpacity>
  );
};

export default memo(FilterButton);
