import { View } from 'react-native';
import React, { memo } from 'react';
import GoBack from '@/layouts/GoBack';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText';
import CustomDropDownMenu from './ui/CustomDropDownMenu';
import { Entypo } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';

type DropDownItem = {
  title: string;
  key: string;
};

interface IHeaderProps {
  image: string;
  name: string;
  participantsNumber: number;
  dropDownActionsList?: DropDownItem[];
  onDropDownSelect?: (key: string) => void;
}

const SocialCircleAdminHeader = ({
  image,
  name,
  participantsNumber,
  dropDownActionsList,
  onDropDownSelect = () => {},
}: IHeaderProps) => {
  return (
    <View className='bg-primary-200 rounded-b-3xl py-7'>
      <View className='flex-row px-2 items-center justify-between'>
        <View className='flex-row items-center gap-2'>
          <GoBack href='/(protected)/(tabs)/socialCircles' />
          <View className='flex-row items-center gap-2'>
            <Avatar source={{ uri: image }} />
            <View className='gap-1'>
              <CustomText className='text-white-50 font-bold text-h2'>
                {name}
              </CustomText>
              <CustomText className='text-neutral-100'>
                {participantsNumber} people
              </CustomText>
            </View>
          </View>
        </View>
        <CustomDropDownMenu
          triggerElement={
            <Entypo
              name='dots-three-vertical'
              size={24}
              color={COLORS.neutral[300]}
            />
          }
          items={dropDownActionsList || []}
          onSelect={onDropDownSelect}
        />
      </View>
    </View>
  );
};

export default memo(SocialCircleAdminHeader);
