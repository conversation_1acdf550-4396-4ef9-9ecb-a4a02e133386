import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import React, { memo } from 'react';
import CustomText from './ui/CustomText';
import { Image } from 'expo-image';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import { ImagePickerAsset } from 'expo-image-picker';

/**
 * @used to upload create activity images
 *
 * */

type createActivityImages = {
  pickImage: () => Promise<void>;
  onDeleteImage: (img: ImagePickerAsset) => void;
  error?: string;
  images: ImagePickerAsset[];
};

const CreateActivityImages = ({
  pickImage,
  images,
  onDeleteImage,
  error,
}: createActivityImages) => {
  return (
    <View>
      <CustomText style={styles.sectionTitle}>Upload Images</CustomText>
      <View className='flex-row items-center flex-wrap justify-center gap-2'>
        {Array(8)
          .fill('')
          .map((_, index) => {
            const imageUriExists =
              images?.[index]?.uri && images?.[index]?.uri.length;
            return (
              <View
                className='bg-neutral-300 rounded-2xl w-24 h-24 mb-2 relative'
                key={String(index)}
              >
                {imageUriExists && (
                  <Image
                    source={{ uri: images?.[index]?.uri }}
                    style={styles.image}
                  />
                )}

                {index === 0 && imageUriExists && (
                  <View className='bg-[#391162] px-4 py-1 rounded-3xl absolute mt-2 left-2'>
                    <CustomText className='text-white-50'>Cover</CustomText>
                  </View>
                )}

                {imageUriExists && (
                  <TouchableOpacity
                    onPress={() => onDeleteImage(images[index])}
                    className='w-8 h-8 rounded-full bg-neutral-100 justify-center items-center absolute bottom-[-0.4rem] right-0'
                  >
                    <Ionicons
                      name='close'
                      size={normalized(15)}
                      color={COLORS.neutral[600]}
                    />
                  </TouchableOpacity>
                )}

                {!imageUriExists && (
                  <TouchableOpacity
                    onPress={pickImage}
                    className='w-8 h-8 rounded-full bg-secondary-300 justify-center items-center absolute bottom-[-0.4rem] right-0'
                  >
                    <Ionicons
                      name='add'
                      size={normalized(12)}
                      color={COLORS.white[50]}
                    />
                  </TouchableOpacity>
                )}
              </View>
            );
          })}
      </View>
      {error && <CustomText className='text-danger'>{error}</CustomText>}
    </View>
  );
};

export default memo(CreateActivityImages);

const styles = StyleSheet.create({
  sectionTitle: {
    color: COLORS.white[50],
    fontWeight: 'semibold',
    marginBottom: normalized(3),
    fontSize: normalized(16),
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: normalized(10),
  },
});
