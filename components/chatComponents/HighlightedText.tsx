import React from 'react';
import { COLORS } from '@/constants/Theme';
import CustomText from '../ui/CustomText';

interface HighlightedTextProps {
  text: string;
  searchQuery: string;
  textStyle?: any;
}

const HighlightedText = ({
  text,
  searchQuery,
  textStyle,
}: HighlightedTextProps) => {
  if (!searchQuery.trim()) {
    return <CustomText style={textStyle}>{text}</CustomText>;
  }

  const parts = text.split(new RegExp(`(${searchQuery})`, 'gi'));

  return (
    <CustomText style={textStyle}>
      {parts.map((part, index) => (
        <CustomText
          key={index}
          style={
            part.toLowerCase() === searchQuery.toLowerCase()
              ? { backgroundColor: COLORS.neutral[100], borderRadius: 2 }
              : {}
          }
        >
          {part}
        </CustomText>
      ))}
    </CustomText>
  );
};

export default HighlightedText;
