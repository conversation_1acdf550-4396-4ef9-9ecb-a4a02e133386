import React, { forwardRef } from 'react';
import {
  TouchableOpacity,
  View,
  TextInput,
  TextInputProps,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import { APP_Icons } from '@/constants/Images';

interface ChatInputAndUploadProps {
  inputText: string;
  setInputText: (text: string) => void;
  handleSendMessage: () => void;
}

const ChatInputAndUpload = forwardRef<TextInput, ChatInputAndUploadProps>(
  ({ inputText, setInputText, handleSendMessage }, ref) => {
    return (
      <View className='px-3 flex-row items-center gap-2 '>
        <TouchableOpacity>
          <Ionicons name='add' size={32} color={COLORS.secondary[300]} />
        </TouchableOpacity>

        <View className='bg-white-50 flex-row py-2 px-3 rounded-3xl flex-1 justify-center items-center'>
          <TextInput
            ref={ref}
            className='flex-1 font-poppins text-neutral-800 px-2 max-h-[100px]'
            multiline
            placeholder='Type a message here...'
            value={inputText}
            onChangeText={setInputText}
            onSubmitEditing={handleSendMessage}
          />
          <TouchableOpacity
            className='bg-secondary-300 rounded-full w-10 h-10 items-center justify-center'
            onPress={handleSendMessage}
          >
            <APP_Icons.MessageSend width={20} height={20} />
          </TouchableOpacity>
        </View>
      </View>
    );
  },
);

ChatInputAndUpload.displayName = 'ChatInputAndUpload';

export default ChatInputAndUpload;
