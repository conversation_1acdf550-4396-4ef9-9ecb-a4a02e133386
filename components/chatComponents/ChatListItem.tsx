import React, { memo } from 'react';
import { Pressable, View } from 'react-native';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';
import { formatTimeRange } from '@/utils/formatDates';
import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import { Href, RelativePathString, router } from 'expo-router';

interface IChatListItemProps {
  image?: string;
  title: string;
  message: string;
  numberOfMessages?: number;
  createdAt: Date;
  href: Href;
  roomId?: string;
  type: 'single' | 'group';
  isOnline?: boolean;
}

const ChatListItem = ({
  createdAt,
  message,
  title,
  image,
  numberOfMessages = 0,
  href,
  roomId,
  type,
  isOnline,
}: IChatListItemProps) => {
  return (
    <Pressable
      onPress={() =>
        router.push({
          pathname: href as RelativePathString,
          params: {
            id: roomId,
            image,
            title,
            type,
            isOnline: String(isOnline),
          },
        })
      }
      android_ripple={{ color: '#E5E7EB' }} // subtle gray ripple
      className='px-4 py-3'
    >
      <View className='flex-row items-center justify-between'>
        {/* Left: Avatar + Text */}
        <View className='flex-row items-center gap-3 flex-1'>
          <Avatar source={{ uri: image }} text={title} />

          <View className='flex-1'>
            <CustomText
              className='text-neutral-100 font-medium text-base'
              numberOfLines={1}
            >
              {title}
            </CustomText>
            <CustomText
              className='text-neutral-300 text-sm mt-1'
              numberOfLines={1}
            >
              {message || 'No messages yet'}
            </CustomText>
          </View>
        </View>

        {/* Right: Time + Badge */}
        <View className='items-end ml-2'>
          <CustomText className='text-neutral-400 text-xs'>
            {formatTimeRange(createdAt, undefined)}
          </CustomText>

          {numberOfMessages > 0 ? (
            <View className='mt-2 bg-secondary-300 min-w-[20px] px-2 py-[2px] rounded-full items-center justify-center'>
              <CustomText className='text-white text-xs font-medium'>
                {numberOfMessages}
              </CustomText>
            </View>
          ) : (
            <Ionicons
              name='chevron-forward'
              size={20}
              color={COLORS.neutral[400]}
              style={{ marginTop: 12 }}
            />
          )}
        </View>
      </View>
    </Pressable>
  );
};

export default memo(ChatListItem);
