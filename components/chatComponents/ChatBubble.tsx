import React, { memo, useMemo } from 'react';
import CustomText from '../ui/CustomText';
import { View } from 'react-native';
import Avatar from '../ui/Avatar';

import { Ionicons } from '@expo/vector-icons';
import { COLORS } from '@/constants/Theme';
import HighlightedText from './HighlightedText';

interface ChatBubbleProps {
  text: string;
  isCurrentUser: boolean;
  createdAt: Date;
  user: {
    displayName: string;
    image: string;
  };
  seen?: boolean;
  searchQuery?: string;
}

const ChatBubble = ({
  text,
  isCurrentUser,
  createdAt,
  user,
  seen,
  searchQuery = '',
}: ChatBubbleProps) => {
  // Memoize the formatted time to prevent unnecessary re-renders
  const formattedTime = useMemo(() => {
    if (createdAt instanceof Date) {
      return createdAt.toLocaleTimeString([], {
        hour: '2-digit',
        minute: '2-digit',
      });
    }
    return '';
  }, [createdAt]);

  // Memoize styles based on user type
  const bubbleStyle = useMemo(() => {
    return `py-2 px-3 rounded-2xl ${
      isCurrentUser
        ? 'bg-primary-50 rounded-tr-none'
        : 'bg-[#F1F0F0] rounded-tl-none'
    }`;
  }, [isCurrentUser]);

  const textColor = useMemo(() => {
    return isCurrentUser ? COLORS.neutral[100] : COLORS.neutral[900];
  }, [isCurrentUser]);

  return (
    <View
      className={`mb-3 ${isCurrentUser ? 'self-end items-end' : 'self-start items-start'}`}
    >
      {/* Message container with tail */}
      <View className='flex-row'>
        {!isCurrentUser && (
          <View className='mr-2 mt-1'>
            <Avatar
              source={user.image ? { uri: user.image } : undefined}
              text={user.displayName}
              size={30}
            />
          </View>
        )}

        <View className='max-w-[75%]'>
          {/* User name (only for non-current user) */}
          {!isCurrentUser && (
            <CustomText className='text-xs font-medium text-secondary-300 ml-2 mb-1'>
              {user.displayName}
            </CustomText>
          )}

          {/* Message bubble */}
          <View className={bubbleStyle}>
            <HighlightedText
              text={text}
              searchQuery={searchQuery}
              textStyle={{ color: textColor }}
            />

            {/* Time and seen status inside bubble */}
            <View className='flex-row items-center justify-end mt-1 gap-1'>
              <CustomText
                className={`text-[10px] ${isCurrentUser ? 'text-white-50/70' : 'text-neutral-500'}`}
              >
                {formattedTime}
              </CustomText>

              {isCurrentUser && (
                <Ionicons
                  name={seen ? 'checkmark-done' : 'checkmark'}
                  size={12}
                  color={
                    seen
                      ? COLORS.secondary[300]
                      : isCurrentUser
                        ? 'rgba(255,255,255,0.7)'
                        : COLORS.neutral[400]
                  }
                />
              )}
            </View>
          </View>
        </View>
      </View>
    </View>
  );
};

export default memo(ChatBubble);
