import React, { memo } from 'react';
import { View } from 'react-native';
import CustomActionModal from '@/layouts/CustomActionModal';
import CustomText from '../ui/CustomText';

type DeleteActivityModalProps = {
  deleteModal: boolean;
  setDeleteModal: React.Dispatch<React.SetStateAction<boolean>>;
  handleDeleteActivity: () => void;
  deleteLoading: boolean;
};

const DeleteActivityModal: React.FC<DeleteActivityModalProps> = ({
  deleteModal,
  setDeleteModal,
  handleDeleteActivity,
  deleteLoading,
}) => {
  return (
    <CustomActionModal
      visible={deleteModal}
      onCancel={() => setDeleteModal(false)}
      onAction={handleDeleteActivity}
      actionButtonTitle='Delete'
      actionButtonStyle={{ flex: 1 }}
      cancelButtonStyle={{ flex: 1 }}
      actionButtonLoading={deleteLoading}
      actionButtonDisabled={deleteLoading}
    >
      <CustomText className='text-black text-h2 text-center'>
        Are you sure you want to delete this activity?
      </CustomText>
    </CustomActionModal>
  );
};

export default memo(DeleteActivityModal);
