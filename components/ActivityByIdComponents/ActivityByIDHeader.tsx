import { StyleSheet, View } from 'react-native';
import React, { memo, useMemo } from 'react';
import { ImageBackground } from 'expo-image';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import GoBack from '@/layouts/GoBack';
import CustomDropDownMenu from '../ui/CustomDropDownMenu';
import { Entypo } from '@expo/vector-icons';
import { COLORS, SCREEN_HEIGHT } from '@/constants/Theme';
import { LinearGradient } from 'expo-linear-gradient';

const dropDownActionsList = [
  {
    key: 'delete-activity',
    title: 'Delete Activity',
  },
  {
    key: 'edit-activity',
    title: 'Edit Activity',
  },
];

type HeaderProps = {
  images: string[] | undefined;
  isCreator: boolean;
  handleDropDownActions: (e: string) => void;
};
const ActivityByIDHeader = memo(
  ({ images, isCreator, handleDropDownActions }: HeaderProps) => {
    const inset = useSafeAreaInsets();

    const headerContainerStyle = useMemo(
      () => [styles.headerContainer, { paddingTop: inset.top }],
      [inset.top],
    );

    const imageSource = useMemo(
      () => ({
        uri: images?.[0],
      }),
      [images],
    );

    return (
      <ImageBackground
        source={imageSource}
        style={headerContainerStyle}
        imageStyle={styles.headerImage}
      >
        <LinearGradient
          colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.3)']}
          style={{
            position: 'absolute',
            left: 0,
            right: 0,
            top: 0,
            bottom: 0,
          }}
        />

        <View className='justify-between px-3 flex-row'>
          <GoBack />

          {isCreator && (
            <CustomDropDownMenu
              items={dropDownActionsList}
              onSelect={handleDropDownActions}
              triggerElement={
                <Entypo
                  name='dots-three-vertical'
                  size={24}
                  color={COLORS.neutral[100]}
                />
              }
            />
          )}
        </View>
      </ImageBackground>
    );
  },
);

ActivityByIDHeader.displayName = 'ActivityByIDHeader';

const styles = StyleSheet.create({
  headerContainer: {
    height: SCREEN_HEIGHT / 3,
  },
  headerImage: {
    backgroundColor: 'rgba(0,0,0,0.3)',
    objectFit: 'cover',
    width: '100%',
  },
});

export default ActivityByIDHeader;
