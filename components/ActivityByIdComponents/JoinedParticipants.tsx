import { Pressable, View } from 'react-native';
import React, { memo, useCallback, useMemo } from 'react';
import CustomText from '../ui/CustomText';
import JoinedParticipant from '../Joinedparticipant';
import { useTranslation } from 'react-i18next';
import { IActivityItem } from '@/types';
import { router } from 'expo-router';

import { formatDateDayMonthWeek } from '@/utils/formatDates';
import { useActivityStore } from '@/stores/useActivityStore';

type JoinedParticipantsProps = {
  activityData: IActivityItem | undefined;
  membersCount: number;
  isCreator: boolean;
};
const JoinedParticipants = ({
  activityData,
  membersCount,
  isCreator,
}: JoinedParticipantsProps) => {
  const { t } = useTranslation();
  const { setSharedActivityParticipantsData } = useActivityStore();

  const participantsData = useMemo(
    () => ({
      participants: activityData?.participant.participants || [],
      total: activityData?.participant.total || 0,
    }),
    [activityData?.participant],
  );

  const formattedDateRange = useMemo(
    () =>
      `${formatDateDayMonthWeek(activityData?.startDate)} - ${formatDateDayMonthWeek(activityData?.endDate)}`,
    [activityData?.startDate, activityData?.endDate],
  );

  const handleSeeParticipantsClick = useCallback(() => {
    if (!activityData?._id) return;

    if (isCreator) {
      router.push(
        `/(protected)/activity/participants/owner/${activityData._id}`,
      );

      setSharedActivityParticipantsData({
        createdAt: formattedDateRange,
        image: activityData.images[0],
        participants: activityData.participant.participants,
        title: activityData.name,
      });

      return;
    }

    router.push(`/(protected)/activity/participants/${activityData._id}`);
  }, [isCreator, activityData, formattedDateRange]);

  const renderNoParticipants = useMemo(
    () => (
      <View>
        <CustomText className='text-white-50 font-medium text-h2'>
          {t('no-participants-yet')}
        </CustomText>
        <CustomText className='text-neutral-400 text-h4 font-light'>
          {t('be-the-first-one-to-get-started')}
        </CustomText>
      </View>
    ),
    [t],
  );

  return (
    <Pressable onPress={handleSeeParticipantsClick} className='gap-2'>
      <CustomText className='text-h3 text-white-50'>
        {t('people-joined', { count: membersCount })}
      </CustomText>

      {membersCount > 0 ? (
        <JoinedParticipant participants={participantsData} />
      ) : (
        renderNoParticipants
      )}
    </Pressable>
  );
};

export default memo(JoinedParticipants);
