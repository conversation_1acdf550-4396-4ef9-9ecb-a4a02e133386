import { StyleSheet, View } from 'react-native';
import React, { memo } from 'react';
import CustomText from '../ui/CustomText';
import CustomButton from '../ui/buttons/CustomButton';
import { useTranslation } from 'react-i18next';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { formattedCurrency } from '@/utils/common';

type propsType = {
  isCreator: boolean;
  isParticipant: boolean;
  isPaid: boolean;
  currency: string;
  price: string;
  handleJoinActivity: () => void;
  handleLeaveActivity: () => void;
  isLoading?: boolean;
  isPending: boolean;
};
const JoinOrLeaveActivity = ({
  isCreator,
  isParticipant,
  currency,
  isPaid,
  price,
  handleJoinActivity,
  handleLeaveActivity,
  isLoading,
  isPending,
}: propsType) => {
  const { t } = useTranslation();
  const inset = useSafeAreaInsets();
  return (
    <View className='px-5 mb-2'>
      {!isPending && (
        <>
          {/* if not joined and user is not the creator */}
          {!isCreator && !isParticipant && (
            // IF not joined
            <View
              className='flex-row justify-between px-5'
              style={{ marginBlock: inset.bottom }}
            >
              <View className='flex-row items-center gap-2 flex-1'>
                <CustomText className='text-white-50'>
                  {formattedCurrency(currency)}
                </CustomText>
                <CustomText className='text-h1 text-white-50 font-medium '>
                  {isPaid ? `${price}` : t('free')}
                </CustomText>
              </View>
              <CustomButton
                loading={isLoading}
                onPress={handleJoinActivity}
                className='flex-1'
              >
                {t('join')}
              </CustomButton>
            </View>
          )}
          {/* Only if joined */}

          {isParticipant && !isCreator && (
            <CustomButton
              onPress={handleLeaveActivity}
              loading={isLoading}
              style={styles.leaveActivityButton}
            >
              <CustomText className='text-secondary-300 text-h5 font-medium'>
                {t('leave-activity')}
              </CustomText>
            </CustomButton>
          )}
        </>
      )}

      {isPending && (
        <View
          className='flex-row justify-between px-5'
          style={{ marginBlock: inset.bottom }}
        >
          <CustomText className='text-h1 text-white-50 font-medium flex-1'>
            Your Request is under review
          </CustomText>
        </View>
      )}
    </View>
  );
};

export default memo(JoinOrLeaveActivity);

const styles = StyleSheet.create({
  leaveActivityButton: {
    backgroundColor: 'transparent',
    alignSelf: 'flex-end',
  },
});
