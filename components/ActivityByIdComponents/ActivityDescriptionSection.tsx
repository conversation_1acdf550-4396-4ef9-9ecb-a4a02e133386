import { View } from 'react-native';
import React, { memo, useCallback, useState } from 'react';
import CustomText from '../ui/CustomText';
import ShowMore from '../showMore';
import { useTranslation } from 'react-i18next';

type IActivityDescriptionProps = {
  description: string;
};

const ActivityDescriptionSection = (props: IActivityDescriptionProps) => {
  const { t } = useTranslation();
  const [numberOfLines, setNumberOfLines] = useState<number | undefined>(6);

  const toggleDescriptionLines = useCallback(() => {
    setNumberOfLines(numberOfLines === undefined ? 6 : undefined);
  }, [numberOfLines]);

  return (
    <View className='gap-2'>
      <CustomText className={'text-white-50 font-medium text-h2'}>
        {t('description')}
      </CustomText>
      <CustomText
        numberOfLines={numberOfLines}
        className='text-neutral-400 text-h4'
      >
        {props.description}
      </CustomText>
      <View>
        {props.description?.length > 40 && (
          <ShowMore
            onPress={toggleDescriptionLines}
            title={t('see-more')}
            textProps={{ className: 'text-secondary-300 text-h3' }}
          />
        )}
      </View>
    </View>
  );
};

export default memo(ActivityDescriptionSection);
