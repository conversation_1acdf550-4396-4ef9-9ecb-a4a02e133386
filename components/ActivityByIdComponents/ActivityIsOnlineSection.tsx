import { View, Pressable } from 'react-native';
import React, { memo } from 'react';

import { APP_Icons } from '@/constants/Images';
import { COLORS, normalized } from '@/constants/Theme';
import CustomText from '../ui/CustomText';
import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';

const ActivityIsOnlineSection = () => {
  return (
    <Link href='/(protected)/(tabs)/activities' asChild>
      <Pressable className='flex-row items-center justify-between bg-white-50/10 py-2.5 px-4 rounded-2xl'>
        <View className='flex-row items-start gap-2'>
          <APP_Icons.ChatIcon width={normalized(32)} height={normalized(32)} />
          <View>
            <CustomText className='text-white-50 text-h3 font-medium'>
              Activity Group Chat
            </CustomText>
            <CustomText className='text-neutral-200 text-h5'>
              group chat for participants
            </CustomText>
          </View>
        </View>
        <Ionicons
          name='chevron-forward'
          size={normalized(24)}
          color={COLORS.neutral[200]}
        />
      </Pressable>
    </Link>
  );
};

export default memo(ActivityIsOnlineSection);
