import React, { memo } from 'react';
import { Pressable, View } from 'react-native';
import SmallLocationMap from '../SmallLocationMap';

type IActivtyLocationProps = {
  location: {
    latitude: number;
    longitude: number;
  };
  handleSetMapLocation: () => void;
};
const ActivityLocationSection = (props: IActivtyLocationProps) => {
  return (
    <View className='h-36 mt-4 relative'>
      <Pressable
        onPress={props.handleSetMapLocation}
        className='absolute inset-0 h-full w-full bg-transparent'
        style={{ zIndex: 10 }} // Ensure it stays above other elements
      />
      <View style={{ zIndex: 1 }}>
        <SmallLocationMap {...props.location} />
      </View>
    </View>
  );
};

export default memo(ActivityLocationSection);
