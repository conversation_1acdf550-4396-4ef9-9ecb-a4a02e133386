import { Pressable, StyleSheet, View } from 'react-native';
import React, { memo, ReactNode } from 'react';
import Avatar from './ui/Avatar';
import CustomText from './ui/CustomText';
import { Href, Link, router } from 'expo-router';
import { COLORS } from '@/constants/Theme';

interface IUserWithActionButton {
  children?: ReactNode;
  avatar?: string;
  name: string;
  size?: number;
  href?: Href;
}

const UserWithActionButton = ({
  children,
  name,
  avatar,
  size = 60,
  href,
}: IUserWithActionButton) => {
  return (
    <View style={styles.container}>
      <View style={styles.userInfo}>
        {href ? (
          <Pressable onPress={() => href && router.push(href)}>
            <Avatar
              size={size}
              source={avatar ? { uri: avatar } : undefined}
              text={name}
            />
          </Pressable>
        ) : (
          <Avatar
            size={size}
            source={avatar ? { uri: avatar } : undefined}
            text={name}
          />
        )}
        <CustomText numberOfLines={1} style={styles.text}>
          {name}
        </CustomText>
      </View>

      {children}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    flex: 1,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16, // Adjust as needed
  },
  text: {
    color: COLORS.neutral[300],
    fontSize: 16, // Equivalent to "text-h5"
  },
});

export default memo(UserWithActionButton);
