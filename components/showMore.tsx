import {
  StyleSheet,
  TouchableOpacity,
  ViewStyle,
  Animated,
} from 'react-native';
import React, { useEffect, useRef } from 'react';
import { TextProps } from 'react-native/Libraries/Text/Text';
import CustomText from './ui/CustomText';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';

type ShowMoreProps = {
  title?: string;
  textProps?: TextProps;
  isActive?: boolean;
  onPress: () => void;
  iconSize?: number;
  iconColor?: string;
  containerStyle?: ViewStyle;
};

const ShowMore = (props: ShowMoreProps) => {
  const rotationAnim = useRef(
    new Animated.Value(props.isActive ? 180 : 0),
  ).current;

  useEffect(() => {
    Animated.timing(rotationAnim, {
      toValue: props.isActive ? 180 : 0,
      duration: 200, // Animation duration in ms
      useNativeDriver: true, // Improves performance
    }).start();
  }, [props.isActive]);

  const rotateStyle = {
    transform: [
      {
        rotate: rotationAnim.interpolate({
          inputRange: [0, 180],
          outputRange: ['0deg', '180deg'],
        }),
      },
    ],
  };

  return (
    <TouchableOpacity
      style={props.containerStyle}
      onPress={props.onPress}
      className='flex-row items-center gap-1.5'
    >
      {props.title && (
        <CustomText {...props.textProps}>{props.title}</CustomText>
      )}

      <Animated.View style={rotateStyle}>
        <Ionicons
          name='chevron-down'
          size={props.iconSize || normalized(20)}
          color={props.iconColor || COLORS.secondary[300]}
        />
      </Animated.View>
    </TouchableOpacity>
  );
};

export default ShowMore;
