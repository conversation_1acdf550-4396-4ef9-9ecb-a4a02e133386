import {
  Platform,
  StyleSheet,
  TouchableOpacity,
  View,
  KeyboardAvoidingView,
  Keyboard,
  TouchableWithoutFeedback,
} from 'react-native';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { useVideoPlayer, VideoPlayer, VideoView } from 'expo-video';
import { useEvent, useEventListener } from 'expo';
import { Ionicons } from '@expo/vector-icons';
import { COLORS, normalized } from '@/constants/Theme';
import Animated, {
  Easing,
  runOnJS,
  useAnimatedStyle,
  useSharedValue,
  withTiming,
} from 'react-native-reanimated';
import { TapGestureHandler } from 'react-native-gesture-handler';
import { LinearGradient } from 'expo-linear-gradient';
import { Controller, useForm } from 'react-hook-form';
import CustomInput from '../ui/CustomInput';
import Loading from '@/layouts/Loading';
import useUploadShort from '@/hooks/shortsHooks/useUploadShort';
import { useSession } from '@/context/AuthContext';
import { router } from 'expo-router';
import CircularButton from '../ui/buttons/CircularButton';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';

// Constants
const ANIMATION_DURATION = {
  FADE_IN: 300,
  FADE_OUT: 500,
};

const CONTROLS_TIMEOUT = 2000;
const VIDEO_UPDATE_INTERVAL = 0.25;

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

interface VideoProps {
  mediaUri: string;
  onClear: () => void;
}

// THIS VIDEO VIEW FOR THE Video had been captured by short from camera

const VideoViewComponent = ({ mediaUri, onClear }: VideoProps) => {
  // Hooks
  const inset = useSafeAreaInsets();

  const { userId, user } = useSession();
  console.log(user);
  const { mutate, isPending, generateFormData, isSuccess } = useUploadShort();
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);
  const doubleTapRef = useRef();

  // State
  const [videoEnded, setVideoEnded] = useState(false);
  const fadeAnim = useSharedValue(0);
  const progressWidth = useSharedValue(0);

  // Form
  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm({
    defaultValues: { title: '' },
  });

  // Animations
  const fadeStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
  }));

  const progressStyle = useAnimatedStyle(() => ({
    width: withTiming(`${progressWidth.value}%`, { easing: Easing.linear }),
  }));

  // Video Setup
  const normalizedUri =
    Platform.OS === 'android' && mediaUri && !mediaUri.startsWith('file://')
      ? `file://${mediaUri}`
      : mediaUri;

  const player = useVideoPlayer(mediaUri, (videoPlayer: VideoPlayer) => {
    videoPlayer.loop = false;
    videoPlayer.timeUpdateEventInterval = VIDEO_UPDATE_INTERVAL;
    videoPlayer.play();
  });

  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player.playing,
  });

  // Handlers
  const hideControls = useCallback(() => {
    fadeAnim.value = withTiming(0, { duration: ANIMATION_DURATION.FADE_OUT });
  }, []);

  const showControls = useCallback(() => {
    fadeAnim.value = withTiming(1, { duration: ANIMATION_DURATION.FADE_IN });

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      runOnJS(hideControls)();
    }, CONTROLS_TIMEOUT);
  }, []);

  const togglePlayer = useCallback(() => {
    if (videoEnded) {
      player.seekBy(0);
      setVideoEnded(false);
      player.play();
      return;
    }

    isPlaying ? player.pause() : player.play();
    showControls();
  }, [videoEnded, isPlaying]);

  const handleSave = handleSubmit(({ title }) => {
    const formData = generateFormData({
      title,
      userId: userId ?? '',
      file: normalizedUri,
    });
    mutate(formData, {
      onSuccess: () => {
        router.replace('/(protected)/(tabs)');
      },
    });
  });

  // Event Listeners
  useEventListener(player, 'playToEnd', showControls);

  useEventListener(player, 'timeUpdate', (payload) => {
    if (payload.currentTime !== 0) {
      progressWidth.value = Math.floor(
        (payload.currentTime / player.duration) * 100,
      );
    }
  });

  // Effects
  useEffect(() => {
    if (isSuccess) onClear();
  }, [isSuccess]);

  // UI Components
  const renderPlayPauseButton = () => (
    <Animated.View
      className='absolute justify-center items-center'
      style={[
        fadeStyle,
        {
          top: '50%',
          left: '50%',
          transform: [{ translateX: -40 }, { translateY: -40 }],
        },
      ]}
    >
      <TouchableOpacity
        onPress={() => {
          togglePlayer();
          showControls();
        }}
      >
        <Ionicons
          name={isPlaying ? 'pause-circle' : 'play-circle'}
          size={80}
          color={COLORS.neutral[100]}
        />
      </TouchableOpacity>
    </Animated.View>
  );

  const renderTitleInput = () =>
    !isPlaying && (
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 18 : 20}
        style={styles.keyboardAvoidingView}
      >
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <View
            className='w-full px-4'
            style={{ marginBottom: inset.bottom + 20 }}
          >
            <Controller
              control={control}
              name='title'
              render={({ field: { onChange, value } }) => (
                <CustomInput
                  onChangeText={onChange}
                  value={value}
                  otherStyles='flex-1 bg-white-50/90 rounded-xl'
                  className='text-black flex-1'
                  placeholder='Write your title'
                  error={errors.title?.message}
                  autoCapitalize='sentences'
                  rightIcon={
                    <CircularButton
                      disabled={isPending}
                      variant='secondary'
                      size='small'
                      onPress={handleSave}
                      icon={
                        isPending ? (
                          <Loading isLoading={isPending} />
                        ) : (
                          <Ionicons
                            name='chevron-forward'
                            size={normalized(20)}
                            color={COLORS.white[50]}
                          />
                        )
                      }
                    />
                  }
                />
              )}
            />
          </View>
        </TouchableWithoutFeedback>
      </KeyboardAvoidingView>
    );

  return (
    <View className='h-screen relative bg-black'>
      <TapGestureHandler
        numberOfTaps={1}
        waitFor={doubleTapRef}
        onActivated={showControls}
      >
        <TouchableOpacity
          activeOpacity={1}
          className='flex-1 justify-center items-center'
        >
          <AnimatedLinearGradient
            colors={
              !isPlaying
                ? ['rgba(0,0,0,0.4)', 'transparent', 'rgba(0,0,0,0.4)']
                : ['transparent', 'transparent']
            }
            style={StyleSheet.absoluteFillObject}
          />

          <View className='relative top-4 w-[90%] z-30 h-[10px] justify-center items-center rounded-xl bg-white-50/20'>
            <Animated.View
              className='h-[3px] absolute left-0 bg-white-50 rounded-xl'
              style={progressStyle}
            />
          </View>

          <View
            className='absolute h-[3px] w-full z-30 px-5 gap-2'
            style={{ top: inset.top + 10 }}
          >
            <View
              className='  flex-row justify-between items-center'
              style={{ marginTop: inset.top }}
            >
              <View className='flex-1  flex-row gap-3 z-10 '>
                <Avatar
                  text={user?.displayName || ''}
                  source={{ uri: user?.image }}
                />
                <CustomText className='text-white-50 z-20 text-h3'>
                  {user?.displayName}
                </CustomText>
              </View>

              <TouchableOpacity
                onPress={onClear}
                className='bg-white-50/20 w-12 h-12 rounded-full border border-neutral-300 justify-center items-center'
              >
                <Ionicons name='close' size={20} color={COLORS.white[50]} />
              </TouchableOpacity>
            </View>
          </View>

          <VideoView
            style={{ width: '100%', height: '100%' }}
            player={player}
            allowsFullscreen={false}
            allowsVideoFrameAnalysis={false}
            nativeControls={false}
            contentFit='cover'
          />

          {renderPlayPauseButton()}
          {renderTitleInput()}
        </TouchableOpacity>
      </TapGestureHandler>
    </View>
  );
};

const styles = StyleSheet.create({
  keyboardAvoidingView: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    width: '100%',
    zIndex: 10,
  },
});

export default VideoViewComponent;
