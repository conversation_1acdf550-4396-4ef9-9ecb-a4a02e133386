import React, {
  useCallback,
  useEffect,
  useMemo,
  useRef,
  useState,
} from 'react';
import {
  StyleSheet,
  View,
  Pressable,
  Keyboard,
  Platform,
  KeyboardAvoidingView,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import Animated, {
  useAnimatedStyle,
  useSharedValue,
  withTiming,
  runOnJS,
  withSpring,
} from 'react-native-reanimated';
import {
  GestureHandlerRootView,
  TapGestureHandler,
} from 'react-native-gesture-handler';
import { useEvent, useEventListener } from 'expo';
import { LinearGradient } from 'expo-linear-gradient';
import BottomSheet from '@gorhom/bottom-sheet';
import { AntDesign } from '@expo/vector-icons';

import {
  COLORS,
  normalized,
  SCREEN_HEIGHT,
  SCREEN_WIDTH,
} from '@/constants/Theme';
import { useSession } from '@/context/AuthContext';
import useUpdateShorts from '@/hooks/shortsHooks/useUpdateShorts';
import useDeleteShort from '@/hooks/shortsHooks/useDeleteShort';
import { IShortsItem } from '@/types';
import useVideoWithAutoplay from '@/hooks/shortsHooks/useVideoWithAutoplay';
import { formatVideoUrl, isVideoUrl } from '@/utils/videoUtils';
import VideoPlayer from './VideoPlayer';
import ActionButtons from './ActionButtons';
import CommentInput from './CommentInput';
import HeaderBar from './HeaderBar';
import ShortContent from './ShortContent';
import ViewersBottomSheet from './ViewersBottomSheet';

const AnimatedLinearGradient = Animated.createAnimatedComponent(LinearGradient);

// Detect foldable devices by aspect ratio
const IS_FOLDABLE = SCREEN_WIDTH / SCREEN_HEIGHT > 0.7;

interface ShortsVideoViewProps {
  item: IShortsItem;
  onClose: () => void;
}

const ShortsVideoView = (props: ShortsVideoViewProps) => {
  const { item: initialItem, onClose } = props;
  const { user } = useSession();

  // State
  const [short, setShort] = useState<IShortsItem>(initialItem);
  const [isLiked, setIsLiked] = useState(false);
  const [message, setMessage] = useState('');
  const [isCommentVisible, setIsCommentVisible] = useState(false);

  // Refs
  const bottomSheetRef = useRef<BottomSheet>(null);
  const doubleTapRef = useRef(null);
  const timeoutRef = useRef<NodeJS.Timeout | null>(null);

  // Animated values
  const fadeAnim = useSharedValue(0);
  const animatedWidth = useSharedValue(0);
  const heartScale = useSharedValue(0);
  const commentInputHeight = useSharedValue(0);

  // Mutations
  const { mutate: handleUpdateView } = useUpdateShorts('view');
  const { mutate: handleUpdateShort, isPending } = useUpdateShorts('comment');
  const { mutate: HandleDeleteShort, isPending: deleteLoading } =
    useDeleteShort();
  const { mutate: handleLike } = useUpdateShorts('like');
  const { mutate: handleUnlike } = useUpdateShorts('unLike');

  // Check if user is the creator of the short
  const isShortCreator = useMemo(
    () => short?.userId?._id === user?._id,
    [short?.userId?._id, user?._id],
  );

  // Process video URL and handle format detection
  const videoUrl = useMemo(
    () => formatVideoUrl(short?.videoUrl),
    [short?.videoUrl],
  );

  // Check if the content is a video
  const isAVideo = useMemo(
    () => isVideoUrl(short?.videoUrl),
    [short?.videoUrl],
  );

  // Initialize video player with autoplay
  const { player, hasError, ensurePlay } = useVideoWithAutoplay(
    isAVideo ? videoUrl || '' : '',
  );

  // Get player state
  const { isPlaying } = useEvent(player, 'playingChange', {
    isPlaying: player?.playing || false,
  });

  // Check if user has liked the short
  useEffect(() => {
    if (!short) return;
    setIsLiked(short.likedBy.some((v) => v._id === user?._id));
  }, [short, user]);

  // Handle video progress updates
  useEventListener(player, 'timeUpdate', (payload) => {
    if (payload.currentTime !== 0 && isAVideo && player?.duration) {
      animatedWidth.value = Math.floor(
        (payload.currentTime / player.duration) * 100,
      );
    }
  });

  // Handle video end
  useEventListener(player, 'playToEnd', () => {
    if (isAVideo) {
      // Close the short instead of looping
      onClose();
    }
  });

  // Update view count on mount
  useEffect(() => {
    handleUpdateView({ shortId: short._id || '' });
  }, []);

  // For image shorts, implement a countdown timer
  useEffect(() => {
    if (isAVideo) return;

    let countDown = 0;
    const timer = setInterval(() => {
      countDown += 1;
      animatedWidth.value = withTiming((countDown / 30) * 100, {
        duration: 500,
      });
      if (countDown >= 30) {
        clearInterval(timer);
        runOnJS(onClose)();
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [isAVideo, onClose]);

  // Control overlay display
  const showControls = useCallback(() => {
    fadeAnim.value = withTiming(1, { duration: 300 });

    if (timeoutRef.current) clearTimeout(timeoutRef.current);
    timeoutRef.current = setTimeout(() => {
      fadeAnim.value = withTiming(0, { duration: 500 });
    }, 2000);
  }, []);

  // Toggle video play/pause
  const togglePlayer = useCallback(() => {
    if (!player) return;

    if (isPlaying) {
      player.pause();
    } else {
      ensurePlay();
    }

    showControls();
  }, [isPlaying, player, ensurePlay, showControls]);

  // Toggle like status
  const toggleLike = useCallback(() => {
    if (!user) return;

    const alreadyLiked = short.likedBy.some((v) => v._id === user?._id);

    if (alreadyLiked) {
      handleUnlike({ shortId: short._id });
      setShort((prev: typeof short) => ({
        ...prev,
        likesCount: Math.max(prev.likesCount - 1, 0),
        likedBy: prev.likedBy.filter((v) => v._id !== user?._id),
      }));
    } else {
      handleLike({ shortId: short._id });
      setShort((prev: typeof short) => ({
        ...prev,
        likesCount: prev.likesCount + 1,
        likedBy: [
          {
            _id: user?._id || '',
            username: user?.username || '',
            displayName: user?.displayName || '',
            image: user?.image || '',
          },
          ...prev.likedBy,
        ],
      }));

      // Animate heart
      heartScale.value = withSpring(1, { damping: 10 });
      setTimeout(() => {
        heartScale.value = withTiming(0, { duration: 500 });
      }, 1000);
    }
  }, [short, user, handleLike, handleUnlike]);

  // Double tap to like
  const onDoubleTap = useCallback(() => {
    if (!isLiked) {
      toggleLike();
    }

    // Always show heart animation
    heartScale.value = withSpring(1, { damping: 10 });
    setTimeout(() => {
      heartScale.value = withTiming(0, { duration: 500 });
    }, 1000);
  }, [isLiked, toggleLike]);

  // Toggle comment input
  const toggleCommentInput = useCallback(() => {
    if (isCommentVisible) {
      // If already visible, hide it
      Keyboard.dismiss();
      setIsCommentVisible(false);
      togglePlayer();
      commentInputHeight.value = withTiming(0, {
        duration: 300,
      });
    } else {
      // If hidden, show it and focus the input
      setIsCommentVisible(true);
      commentInputHeight.value = withTiming(60, {
        duration: 300,
      });
    }
  }, [isCommentVisible, togglePlayer]);

  // Handle comment submission
  const handleInputSubmit = useCallback(() => {
    if (!message.trim()) return;

    // Optimistically update the comment count
    setShort((prev) => ({
      ...prev,
      commentsCount: prev.commentsCount + 1,
    }));

    // Send the comment to the server and trigger revalidation
    handleUpdateShort({ shortId: short._id, comment: message });

    // Clear the input field immediately for better UX
    setMessage('');
  }, [message, short._id, handleUpdateShort]);

  // Handle delete short
  const handleDeleteShort = useCallback(() => {
    HandleDeleteShort(short._id);
    setTimeout(() => {
      onClose();
    }, 500);
  }, [short._id, HandleDeleteShort, onClose]);

  // Animated styles
  const fadeStyle = useAnimatedStyle(() => ({
    opacity: fadeAnim.value,
  }));

  const heartAnimatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: heartScale.value }],
    opacity: heartScale.value,
  }));

  return (
    <SafeAreaView className='flex-1 bg-black'>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1 }}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 10 : 0}
      >
        <GestureHandlerRootView className='flex-1'>
          <TapGestureHandler
            numberOfTaps={2}
            ref={doubleTapRef}
            onActivated={onDoubleTap}
          >
            <TapGestureHandler
              numberOfTaps={1}
              waitFor={doubleTapRef}
              onActivated={showControls}
            >
              <Pressable
                style={styles.fullscreenTapArea}
                onPress={togglePlayer}
                className='relative'
              >
                {/* Video Gradient Overlay */}
                <AnimatedLinearGradient
                  colors={['rgba(0,0,0,0.3)', 'transparent', 'rgba(0,0,0,0.5)']}
                  style={styles.overlay}
                />

                {/* Video Player */}
                <VideoPlayer
                  isAVideo={isAVideo}
                  videoUrl={videoUrl}
                  hasError={hasError}
                  isPlaying={isPlaying}
                  player={player}
                  thumbnail={short.thumbnail || ''}
                  fadeStyle={fadeStyle}
                  onPlayPause={togglePlayer}
                />

                {/* Heart Animation */}
                <Animated.View
                  style={[styles.heartContainer, heartAnimatedStyle]}
                >
                  <AntDesign
                    name='heart'
                    size={normalized(80)}
                    color={COLORS.danger}
                  />
                </Animated.View>

                {/* Top Header with Progress Bar */}
                <HeaderBar
                  fadeStyle={fadeStyle}
                  progress={animatedWidth}
                  userName={short?.userId?.displayName || '?'}
                  userAvatar={short?.userId?.image || ''}
                  userId={short?.userId?._id || ''}
                  onClose={onClose}
                />

                {/* Right Side Actions */}
                <ActionButtons
                  isLiked={isLiked}
                  likesCount={short.likesCount}
                  commentsCount={short.commentsCount}
                  viewsCount={short.viewsCount}
                  isCreator={isShortCreator}
                  deleteLoading={deleteLoading}
                  onLikePress={toggleLike}
                  onCommentPress={toggleCommentInput}
                  onViewersPress={() => bottomSheetRef?.current?.snapToIndex(0)}
                  onDeletePress={handleDeleteShort}
                />

                {/* Bottom Content */}
                <View className='absolute bottom-0 left-0 right-0 px-4 pb-4 z-10'>
                  {/* Title and Description */}
                  <ShortContent
                    title={short.title}
                    description={short.description}
                  />

                  {/* Comment Input */}
                  <CommentInput
                    message={message}
                    setMessage={setMessage}
                    isVisible={isCommentVisible}
                    isPending={isPending}
                    inputHeight={commentInputHeight}
                    onSubmit={handleInputSubmit}
                  />
                </View>
              </Pressable>
            </TapGestureHandler>
          </TapGestureHandler>

          {/* Viewers Bottom Sheet */}
          <ViewersBottomSheet
            ref={bottomSheetRef}
            viewsCount={short.viewsCount}
            viewedBy={short?.viewedBy || []}
            creatorId={short?.userId?._id || ''}
            isFoldable={IS_FOLDABLE}
          />
        </GestureHandlerRootView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

export default ShortsVideoView;

const styles = StyleSheet.create({
  fullscreenTapArea: {
    ...StyleSheet.absoluteFillObject,
    justifyContent: 'center',
    alignItems: 'center',
  },
  overlay: {
    position: 'absolute',
    inset: 0,
    backgroundColor: 'rgba(0,0,0,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 5,
  },
  heartContainer: {
    position: 'absolute',
    alignSelf: 'center',
    top: '45%',
    zIndex: 999,
  },
});
