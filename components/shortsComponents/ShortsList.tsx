import {
  FlatList,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
  Modal,
  useWindowDimensions,
} from 'react-native';
import React, { memo, useCallback, useEffect, useRef, useState } from 'react';
import { COLORS, gradients } from '@/constants/Theme';
import { IShortsItem } from '@/types';
import ShortsVideoView from './ShortsVideoView';
import { useRouter } from 'expo-router';
import CircleSkelton from '../Skilltons/CircleSkelton';
import useGetShorts from '@/hooks/shortsHooks/useGetShorts';
import GradientCircleButton from '../ui/GradientCircleButton';
import { useSession } from '@/context/AuthContext';
import OptimizedFlatList from '../ui/OptimizedFlatList';

const LIMIT = 5;

type ShortsListItem = IShortsItem | { _id: 'addButton' };

const DashedButton = memo(
  ({ onPress, size }: { onPress: () => void; size: number }) => (
    <TouchableOpacity
      onPress={onPress}
      style={[
        styles.dashedButton,
        {
          width: size,
          height: size,
          borderRadius: size / 2,
          borderWidth: size * 0.03,
        },
      ]}
    >
      <Text style={[styles.buttonText, { fontSize: size * 0.35 }]}>+</Text>
    </TouchableOpacity>
  ),
);

const ShortsList = () => {
  const router = useRouter();
  const { width: screenWidth } = useWindowDimensions();
  const flatListRef = useRef<FlatList<ShortsListItem>>(null);
  const { userId } = useSession();
  const [selectedShort, setSelectedShort] = useState<IShortsItem | null>(null);
  const [shorts, setShorts] = useState<IShortsItem[]>([]);
  const [page, setPage] = useState(1);
  const [loadingMore, setLoadingMore] = useState(false);
  const [hasMore, setHasMore] = useState(true);

  // Calculate dynamic sizes based on screen width
  const itemSize = Math.min(screenWidth * 0.18, 70); // 18% of screen width, max 70
  const containerHeight = itemSize + 15; // Add some padding
  const itemSpacing = Math.min(screenWidth * 0.02, 8); // 2% of screen width, max 8

  const { shortsData, isLoading } = useGetShorts({ page, limit: LIMIT });

  useEffect(() => {
    if (shortsData) {
      const newItems = shortsData.data.filter(
        (item) => !shorts.some((existing) => existing._id === item._id),
      );
      setShorts((prev) => [...prev, ...newItems]);
      setHasMore(shortsData.data.length === LIMIT);
      setLoadingMore(false);
    }
  }, [shortsData]);

  const handleLoadMore = () => {
    if (!loadingMore && hasMore && !isLoading) {
      setLoadingMore(true);
      setPage((prev) => prev + 1);
    }
  };

  const handleSelectShot = useCallback(
    (item: IShortsItem) => {
      flatListRef.current?.scrollToIndex({ index: shorts.indexOf(item) + 1 });
      setSelectedShort(item);
      setShorts((prev) =>
        prev.map((v) => {
          if (v._id === item._id) {
            return {
              ...v,
              viewedBy: [
                ...v.viewedBy,
                { _id: userId || '', username: '', displayName: '' },
              ],
            };
          }
          return v;
        }),
      );
    },
    [shorts, userId],
  );

  const navigateToShortsCamera = useCallback(() => {
    router.navigate('/(protected)/shorts/shortsCameraScreen');
  }, [router]);

  const isSeen = useCallback(
    (item: IShortsItem) => {
      return item.viewedBy.some((v) => v._id.includes(userId || ''));
    },
    [userId],
  );

  const renderItem = useCallback(
    ({ item }: { item: ShortsListItem }) => {
      if (item._id === 'addButton') {
        return (
          <View
            style={[styles.itemContainer, { marginHorizontal: itemSpacing }]}
          >
            <DashedButton onPress={navigateToShortsCamera} size={itemSize} />
          </View>
        );
      }

      // Type guard to ensure item is IShortsItem
      const shortsItem = item as IShortsItem;
      return (
        <View style={[styles.itemContainer, { marginHorizontal: itemSpacing }]}>
          {shortsItem.isPublic && (
            <GradientCircleButton
              image={shortsItem.thumbnail}
              colors={isSeen(shortsItem) ? ['gray', 'gray'] : gradients.border}
              onPress={() => handleSelectShot(shortsItem)}
              size={itemSize}
              padding={1.5}
            />
          )}
        </View>
      );
    },
    [itemSize, itemSpacing, navigateToShortsCamera, isSeen, handleSelectShot],
  );

  return (
    <>
      {isLoading && shorts.length === 0 ? (
        <OptimizedFlatList
          data={Array.from({ length: 4 }, (_, index) => ({
            _id: `skeleton-${index}`,
          }))}
          keyExtractor={(_, index) => String(index)}
          renderItem={() => <CircleSkelton size={itemSize} />}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.loadingContainer}
        />
      ) : shorts.length === 0 ? (
        <View style={[styles.emptyContainer, { height: containerHeight }]}>
          <Text style={styles.emptyText}>No shorts available</Text>
          <DashedButton onPress={navigateToShortsCamera} size={itemSize} />
        </View>
      ) : (
        <OptimizedFlatList<ShortsListItem>
          ref={flatListRef}
          style={[styles.container, { height: containerHeight }]}
          keyExtractor={(item) => item._id}
          data={[{ _id: 'addButton' }, ...shorts]}
          horizontal
          snapToAlignment='center'
          showsHorizontalScrollIndicator={false}
          initialNumToRender={LIMIT}
          pagingEnabled
          onEndReached={handleLoadMore}
          onEndReachedThreshold={0.3}
          contentContainerStyle={styles.listContent}
          ListFooterComponent={
            loadingMore ? <CircleSkelton size={itemSize} /> : null
          }
          renderItem={renderItem}
        />
      )}
      {selectedShort && (
        <Modal visible={true} transparent={false} animationType='slide'>
          <ShortsVideoView
            item={selectedShort}
            onClose={() => setSelectedShort(null)}
          />
        </Modal>
      )}
    </>
  );
};

export default ShortsList;

const styles = StyleSheet.create({
  loadingContainer: {
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  emptyText: {
    color: COLORS.white[50],
    fontSize: 16,
    marginBottom: 8,
    fontWeight: '500',
  },
  container: {
    width: '100%',
  },
  listContent: {
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  itemContainer: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  dashedButton: {
    alignItems: 'center',
    justifyContent: 'center',
    borderColor: COLORS.white[50],
    borderStyle: 'dashed',
  },
  buttonText: {
    color: COLORS.white[50],
    fontWeight: '500',
  },
});
