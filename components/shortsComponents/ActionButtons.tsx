import React from 'react';
import { View, TouchableOpacity } from 'react-native';
import { AntDesign, Ionicons } from '@expo/vector-icons';
import { normalized, COLORS } from '@/constants/Theme';
import { APP_Icons } from '@/constants/Images';
import CustomText from '../ui/CustomText';
import Loading from '@/layouts/Loading';

interface ActionButtonsProps {
  isLiked: boolean;
  likesCount: number;
  commentsCount: number;
  viewsCount: number;
  isCreator: boolean;
  deleteLoading: boolean;
  onLikePress: () => void;
  onCommentPress: () => void;
  onViewersPress: () => void;
  onDeletePress?: () => void;
}

const ICONS_SIZE = 22;

/**
 * Action buttons component for shorts
 * Displays like, comment, share, view, and delete buttons
 */
const ActionButtons: React.FC<ActionButtonsProps> = ({
  isLiked,
  likesCount,
  commentsCount,
  viewsCount,
  isCreator,
  deleteLoading,
  onLikePress,
  onCommentPress,
  onViewersPress,
  onDeletePress,
}) => {
  return (
    <View className='absolute right-4 bottom-1/4 z-20'>
      <View className='flex-col items-center gap-y-6'>
        {/* Like Button */}
        <View className='items-center'>
          <TouchableOpacity onPress={onLikePress} className='items-center'>
            <AntDesign
              name='heart'
              size={normalized(ICONS_SIZE)}
              color={isLiked ? COLORS.danger : COLORS.white[50]}
            />
          </TouchableOpacity>
          <CustomText className='text-white-50 text-h6 mt-1'>
            {likesCount}
          </CustomText>
        </View>

        {/* Comment Button */}
        <View className='items-center'>
          <TouchableOpacity onPress={onCommentPress} className='items-center'>
            <Ionicons
              name='chatbubble-outline'
              size={normalized(ICONS_SIZE)}
              color={COLORS.white[50]}
            />
          </TouchableOpacity>
          <CustomText className='text-white-50 text-h6 mt-1'>
            {commentsCount}
          </CustomText>
        </View>

        {/* Views Button */}
        <View className='items-center'>
          <TouchableOpacity
            onPress={() => (isCreator ? onViewersPress() : {})}
            className='items-center'
          >
            <APP_Icons.EyeIcon
              width={normalized(ICONS_SIZE)}
              height={normalized(ICONS_SIZE)}
            />
          </TouchableOpacity>
          <CustomText className='text-white-50 text-h6 mt-1'>
            {viewsCount}
          </CustomText>
        </View>

        {/* Delete Button (Only for creator) */}
        {isCreator && (
          <View className='items-center'>
            <TouchableOpacity onPress={onDeletePress} className='items-center'>
              {deleteLoading ? (
                <Loading isLoading={deleteLoading} />
              ) : (
                <APP_Icons.TrashIcon
                  width={normalized(ICONS_SIZE)}
                  height={normalized(ICONS_SIZE)}
                />
              )}
            </TouchableOpacity>
            <CustomText className='text-white-50 text-h6 mt-1'>
              Delete
            </CustomText>
          </View>
        )}
      </View>
    </View>
  );
};

export default ActionButtons;
