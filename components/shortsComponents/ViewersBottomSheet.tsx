import React, { forwardRef } from 'react';
import { View, Platform } from 'react-native';
import BottomSheet, { BottomSheetView } from '@gorhom/bottom-sheet';
import { COLORS, SCREEN_HEIGHT } from '@/constants/Theme';
import CustomText from '../ui/CustomText';
import ShortsSeenList from './ShortsSeenList';

interface ViewersBottomSheetProps {
  viewsCount: number;
  viewedBy: any[];
  creatorId: string;
  isFoldable: boolean;
}

/**
 * Viewers bottom sheet component for shorts
 * Displays a list of users who have viewed the short
 */
const ViewersBottomSheet = forwardRef<BottomSheet, ViewersBottomSheetProps>(
  ({ viewsCount, viewedBy, creatorId, isFoldable }, ref) => {
    return (
      <BottomSheet
        ref={ref}
        snapPoints={isFoldable ? ['0%', '40%', '60%'] : ['50%', '70%']}
        index={-1}
        enablePanDownToClose={true}
        style={[
          { flex: 1 },
          isFoldable && {
            // Adjust for foldable devices
            maxHeight: SCREEN_HEIGHT * 0.7,
            marginTop: Platform.OS === 'android' ? 20 : 0,
          },
        ]}
        handleStyle={isFoldable ? { paddingTop: 10 } : {}}
        handleIndicatorStyle={isFoldable ? { width: 60, height: 6 } : {}}
        backgroundStyle={{
          backgroundColor: COLORS.white[50],
          borderTopLeftRadius: 20,
          borderTopRightRadius: 20,
        }}
      >
        <BottomSheetView
          className='px-4'
          style={isFoldable ? { paddingBottom: 20 } : {}}
        >
          <View className='border-b border-b-neutral-200 flex-row items-center justify-between pb-3 h-16'>
            <CustomText className='text-h3 font-semibold text-black'>
              Viewers
            </CustomText>
            <CustomText className='text-h4 text-neutral-500'>
              {viewsCount} views
            </CustomText>
          </View>
          <ShortsSeenList
            data={viewedBy?.filter(
              (item, index, self) =>
                index === self.findIndex((t) => t._id === item._id),
            )}
            creatorId={creatorId}
          />
        </BottomSheetView>
      </BottomSheet>
    );
  },
);

export default ViewersBottomSheet;
