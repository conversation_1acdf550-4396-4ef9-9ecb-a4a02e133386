import { TouchableOpacity, View } from 'react-native';
import React, { memo, useMemo } from 'react';
import { FlatList } from 'react-native-gesture-handler';
import Avatar from '../ui/Avatar';
import CustomText from '../ui/CustomText';
import { APP_Icons } from '@/constants/Images';
import { normalized } from '@/constants/Theme';
import { IUserInfo } from '@/types';
import { router } from 'expo-router';
import { useSession } from '@/context/AuthContext';
import ChatButtonLink from '../ui/ChatButtonLink/ChatButtonLink';

type IShortsSeenList = {
  data: IUserInfo[];
  creatorId?: string; // Add creator ID to check against current user
};

const RenderItem = memo(
  ({ item, isCreator }: { item: IUserInfo; isCreator: boolean }) => {
    return (
      <View className='flex-row items-center justify-between'>
        <View className='flex-row items-center gap-3'>
          <Avatar
            source={{ uri: item?.image || '' }}
            text={item.displayName}
            size={normalized(32)}
          />
          <CustomText className='text-h4 font-semibold'>
            {item.displayName}
          </CustomText>
        </View>

        {/* Only show navigation button if not the creator */}
        {!isCreator && (
          <ChatButtonLink user={item}>
            <APP_Icons.NavigationOutline
              width={normalized(20)}
              height={normalized(20)}
            />
          </ChatButtonLink>
        )}
      </View>
    );
  },
);

const ListEmptyComponent = memo(() => (
  <View className='flex-1 justify-center items-center'>
    <CustomText className='text-h2 font-medium text-black'>
      No one has seen this short yet!
    </CustomText>
  </View>
));

const ShortsSeenList = ({ data, creatorId }: IShortsSeenList) => {
  const { user } = useSession();

  // Check if current user is the creator of the short
  const isCreator = useMemo(
    () => user?._id === creatorId,
    [user?._id, creatorId],
  );

  return (
    <FlatList
      data={data}
      ItemSeparatorComponent={() => <View className='mb-3' />}
      contentContainerStyle={{
        flexGrow: !data.length ? 0 : 1,
        height: !data.length ? 70 : 'auto',
      }}
      contentContainerClassName='px-2 py-3'
      initialNumToRender={10}
      maxToRenderPerBatch={20}
      showsVerticalScrollIndicator={false}
      ListEmptyComponent={<ListEmptyComponent />}
      removeClippedSubviews={true}
      renderItem={({ item }) => (
        <RenderItem item={item} isCreator={isCreator} />
      )}
      keyExtractor={(item) => item._id}
    />
  );
};

export default ShortsSeenList;
