import React, { memo, useEffect, useRef } from 'react';
import MapView, { Marker, PROVIDER_GOOGLE, Camera } from 'react-native-maps';
import { normalized } from '@/constants/Theme';

type Props = {
  latitude: number;
  longitude: number;
  zoom?: number;
  altitude?: number;
};

const SmallLocationMap = ({
  latitude,
  longitude,
  zoom = 7,
  altitude = 10,
}: Props) => {
  const mapRef = useRef<MapView | null>(null);

  useEffect(() => {
    if (mapRef.current) {
      // Zooming in using animateCamera
      const camera: Camera = {
        center: { latitude, longitude },
        pitch: 0,
        heading: 0,
        zoom: zoom,
        altitude: altitude,
      };

      mapRef.current.animateCamera(camera, { duration: 1000 });
    }
  }, [latitude, longitude]);

  return (
    <MapView
      ref={mapRef}
      provider={PROVIDER_GOOGLE}
      style={{ width: '100%', height: '100%', borderRadius: normalized(12) }}
      initialRegion={{
        latitude,
        longitude,
        latitudeDelta: 0.0005, // Very small values for close zoom
        longitudeDelta: 0.0005,
      }}
    >
      <Marker coordinate={{ latitude, longitude }} />
    </MapView>
  );
};

export default memo(SmallLocationMap);
