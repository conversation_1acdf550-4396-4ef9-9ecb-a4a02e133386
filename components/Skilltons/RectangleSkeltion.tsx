import React, { memo, useEffect } from 'react';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

interface RectangularSkeletonProps {
  style?: string;
  children?: React.ReactNode;
}

const RectangularSkeleton = (props: RectangularSkeletonProps) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withRepeat(
      withTiming(1, { duration: 1000 }), // Animation duration
      -1, // Infinite loop
      true, // Auto-reverse
    );
  }, [progress]);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      opacity: interpolate(progress.value, [0, 1], [0.5, 1]), // Smooth shimmer effect
    };
  });

  return (
    <Animated.View
      className={`w-[95%] h-16 rounded-xl mx-2 bg-neutral-300 ${props.style}`}
      style={animatedStyle}
    >
      <>{props.children}</>
    </Animated.View>
  );
};

export default memo(RectangularSkeleton);
