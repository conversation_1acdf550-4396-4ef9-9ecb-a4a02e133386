import { COLORS } from '@/constants/Theme';
import { useEffect, useMemo } from 'react';
import { useWindowDimensions } from 'react-native';
import Animated, {
  interpolateColor,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

type CircleSkeletonProps = {
  size?: number;
  minSize?: number;
  maxSize?: number;
  spacing?: number;
};

const CircleSkeleton = (props: CircleSkeletonProps) => {
  const progress = useSharedValue(0);
  const { width: screenWidth } = useWindowDimensions();

  // Calculate responsive size based on screen width
  const _CircleSize = useMemo(() => {
    // If explicit size is provided, use it
    if (props.size) return props.size;

    // Otherwise calculate responsive size
    const minSize = props.minSize || 40;
    const maxSize = props.maxSize || 70;

    // Calculate size as percentage of screen width (5-10%)
    const responsiveSize = screenWidth * 0.08;

    // Clamp between min and max sizes
    return Math.max(minSize, Math.min(maxSize, responsiveSize));
  }, [props.size, props.minSize, props.maxSize, screenWidth]);

  // Calculate responsive spacing
  const spacing = useMemo(() => {
    return props.spacing !== undefined ? props.spacing : _CircleSize * 0.05;
  }, [props.spacing, _CircleSize]);

  useEffect(() => {
    progress.value = withRepeat(
      withTiming(1, { duration: 1000 }), // Fade duration
      -1, // Infinite loop
      true, // Auto-reverse
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => {
    return {
      backgroundColor: interpolateColor(
        progress.value,
        [0, 1],
        [COLORS.neutral[300], COLORS.neutral[600]],
      ),
    };
  });

  return (
    <Animated.View
      className='rounded-full'
      style={[
        {
          width: _CircleSize,
          height: _CircleSize,
          marginHorizontal: spacing,
          marginVertical: spacing / 2,
        },
        animatedStyle,
      ]}
    />
  );
};

export default CircleSkeleton;
