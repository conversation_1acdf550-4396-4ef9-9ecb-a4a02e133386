import React, { memo, useEffect } from 'react';
import Animated, {
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from 'react-native-reanimated';

interface RectangularSkeletonProps {
  style?: string;
}

const CardSkeleton = ({ style }: RectangularSkeletonProps) => {
  const progress = useSharedValue(0);

  useEffect(() => {
    progress.value = withRepeat(
      withTiming(1, { duration: 1000 }), // Smoother animation duration
      -1, // Infinite loop
      true, // Auto-reverse
    );
  }, []);

  const animatedStyle = useAnimatedStyle(() => ({
    opacity: interpolate(progress.value, [0, 1], [0.3, 1]), // Smooth shimmer effect
  }));

  return (
    <Animated.View
      className={`w-[95%] rounded-xl p-4 mx-2 bg-neutral-300 ${style}`}
      style={animatedStyle}
    >
      <Animated.View
        className='w-full h-[70%] rounded-xl bg-neutral-500'
        style={animatedStyle}
      />
      <Animated.View
        className='w-[40%] h-[10px] mt-3 rounded-sm bg-neutral-500'
        style={animatedStyle}
      />
      <Animated.View
        className='w-[60%] h-[10px] mt-2 rounded-sm bg-neutral-500'
        style={animatedStyle}
      />
      <Animated.View
        className='w-[20%] h-[10px] mt-2 ml-auto rounded-sm bg-neutral-500'
        style={animatedStyle}
      />
    </Animated.View>
  );
};

export default memo(CardSkeleton);
