import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  ReactNode,
  useRef,
} from 'react';
import { AppState, AppStateStatus, Platform } from 'react-native';
import * as BackgroundFetch from 'expo-background-fetch';
import * as TaskManager from 'expo-task-manager';
import { useOnlineStatus } from '@/hooks/useOnlineStatus';

const BACKGROUND_FETCH_TASK = 'background-fetch';

// We'll define the task in the component where we have access to the hook

async function registerBackgroundFetchAsync() {
  if (Platform.OS === 'web') return;
  try {
    const isRegistered = await TaskManager.isTaskRegisteredAsync(
      BACKGROUND_FETCH_TASK,
    );
    if (!isRegistered) {
      await BackgroundFetch.registerTaskAsync(BACKGROUND_FETCH_TASK, {
        minimumInterval: 60 * 60, // in seconds
        stopOnTerminate: false,
        startOnBoot: true,
      });
    }
  } catch (err) {
    console.error('Task Register failed:', err);
  }
}

async function unregisterBackgroundFetchAsync() {
  if (Platform.OS === 'web') return;
  try {
    // Check if the task is registered before attempting to unregister
    const isRegistered = await TaskManager.isTaskRegisteredAsync(
      BACKGROUND_FETCH_TASK
    );
    
    if (isRegistered) {
      await BackgroundFetch.unregisterTaskAsync(BACKGROUND_FETCH_TASK);
    }
  } catch (err) {
    console.error('Task Unregister failed:', err);
  }
}

interface AppStateContextType {
  appState: AppStateStatus;
  isActive: boolean;
  lastActiveAt: Date | null;
}

const AppStateContext = createContext<AppStateContextType | undefined>(
  undefined,
);

interface AppStateProviderProps {
  children: ReactNode;
}

export const AppStateProvider: React.FC<AppStateProviderProps> = ({
  children,
}) => {
  // App state tracking
  const [appState, setAppState] = useState<AppStateStatus>(
    AppState.currentState,
  );
  const [isActive, setIsActive] = useState<boolean>(true);
  const [lastActiveAt, setLastActiveAt] = useState<Date | null>(new Date());
  const [isFirstLaunch, setIsFirstLaunch] = useState<boolean>(false);

  // Use our custom hook for online status management
  const { debouncedUpdateOnlineStatus, updateUserState, cleanupOnlineStatus } =
    useOnlineStatus();

  // Flag to track real state changes vs layout changes
  const isRealStateChange = useRef<boolean>(false);

  // Define the background task with access to our hook
  useEffect(() => {
    if (
      Platform.OS !== 'web' &&
      !TaskManager.isTaskDefined(BACKGROUND_FETCH_TASK)
    ) {
      TaskManager.defineTask(BACKGROUND_FETCH_TASK, async () => {
        try {
          // Use null to indicate background task
          updateUserState(null);
          return BackgroundFetch.BackgroundFetchResult.NewData;
        } catch (error) {
          console.error('Background fetch failed:', error);
          return BackgroundFetch.BackgroundFetchResult.Failed;
        }
      });
    }
  }, [updateUserState]);

  const handleAppStateChange = useCallback(
    async (nextAppState: AppStateStatus) => {
      if (appState !== nextAppState) {
        // This is a real app state change, not just a layout change
        isRealStateChange.current = true;

        if (nextAppState === 'active') {
          setIsActive(true);
          setLastActiveAt(new Date());
          // Pass true as second parameter to indicate this is a real state change
          debouncedUpdateOnlineStatus(true, true);
          
          // Only try to unregister if we're not on web
          if (Platform.OS !== 'web') {
            try {
              const isRegistered = await TaskManager.isTaskRegisteredAsync(
                BACKGROUND_FETCH_TASK
              );
              if (isRegistered) {
                await BackgroundFetch.unregisterTaskAsync(BACKGROUND_FETCH_TASK);
              }
            } catch (err) {
              console.log('Background task management error:', err);
            }
          }
        } else {
          setIsActive(false);
          // Pass true as second parameter to indicate this is a real state change
          debouncedUpdateOnlineStatus(false, true);
          
          // Only try to register if we're not on web
          if (Platform.OS !== 'web') {
            await registerBackgroundFetchAsync();
          }
        }
        setAppState(nextAppState);

        // Reset the flag after handling
        setTimeout(() => {
          isRealStateChange.current = false;
        }, 500);
      }
    },
    [appState, debouncedUpdateOnlineStatus],
  );

  useEffect(() => {
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    // Initial update with debounce
    debouncedUpdateOnlineStatus(true, true);

    return () => {
      subscription.remove();

      // Use the cleanup function from our hook
      cleanupOnlineStatus();
    };
  }, [handleAppStateChange, debouncedUpdateOnlineStatus, cleanupOnlineStatus]);

  const value = {
    appState,
    isActive,
    lastActiveAt,
    isFirstLaunch,
    setIsFirstLaunch,
  };

  return (
    <AppStateContext.Provider value={value}>
      {children}
    </AppStateContext.Provider>
  );
};

export const useAppState = () => {
  const context = useContext(AppStateContext);
  if (!context) {
    throw new Error('useAppState must be used within an AppStateProvider');
  }
  return context;
};
