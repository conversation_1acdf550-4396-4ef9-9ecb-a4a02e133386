import React, {
  createContext,
  useContext,
  useEffect,
  useRef,
  useState,
  useCallback,
} from 'react';
import { AppState, AppStateStatus } from 'react-native';
import io, { Socket } from 'socket.io-client';
import { BASE_URL } from '@/constants/Config';
import { useSession } from './AuthContext';

// Socket URL
const SOCKET_URL = BASE_URL;

// Create a singleton socket instance
let socketInstance: Socket | null = null;

// Define context type
interface SocketContextType {
  isConnected: boolean;
  addEventListener: (event: string, handler: (data: any) => void) => () => void;
  emitEvent: (
    event: string,
    data: any,
    callback?: (response: any) => void,
  ) => void;
}

// Create context
const SocketContext = createContext<SocketContextType | undefined>(undefined);

// Provider props
interface SocketProviderProps {
  children: React.ReactNode;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({ children }) => {
  const { userId } = useSession();
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const appState = useRef(AppState.currentState);

  // Initialize socket connection
  const initializeSocket = useCallback(() => {
    if (!userId) return null;

    // Use existing socket if available
    if (socketInstance && socketInstance.connected) {
      socketRef.current = socketInstance;
      setIsConnected(true);
      return socketInstance;
    }

    // Create new socket connection
    try {
      const socket = io(SOCKET_URL, {
        transports: ['websocket', 'polling'], // Add polling as fallback
        query: { userId },
        reconnection: true,
        reconnectionAttempts: 10, // Increase attempts
        reconnectionDelay: 1000,
        timeout: 10000, // Add timeout
      });

      socketInstance = socket;
      socketRef.current = socket;

      // Set up event listeners
      socket.on('connect', () => {
        console.log('Socket connected:', socket.id);
        setIsConnected(true);
        socket.emit('register', { userId });
      });

      socket.on('disconnect', () => {
        console.log('Socket disconnected');
        setIsConnected(false);
      });

      socket.on('connect_error', (error) => {
        console.error('Connection error:', error);
        setIsConnected(false);
        // Try to reconnect with polling if websocket fails
        if (!socket?.io?.opts?.transports?.some(t => t === 'polling')) {
          console.log('Attempting to reconnect with polling transport');
          socket.io.opts.transports = ['polling', 'websocket'];
        }
      });

      return socket;
    } catch (error) {
      console.error('Socket initialization error:', error);
      setIsConnected(false);
      return null;
    }
  }, [userId]);

  // Handle app state changes to reconnect when app comes to foreground
  const handleAppStateChange = useCallback(
    (nextAppState: AppStateStatus) => {
      if (
        appState.current.match(/inactive|background/) &&
        nextAppState === 'active' &&
        userId
      ) {
        // App has come to the foreground
        if (!socketRef.current || !socketRef.current.connected) {
          initializeSocket();
        }
      } else if (
        appState.current === 'active' &&
        nextAppState.match(/inactive|background/)
      ) {
        // App has gone to the background
        // We'll keep the socket connection but could implement a timeout here
      }
      appState.current = nextAppState;
    },
    [userId, initializeSocket],
  );

  // Add event listener to socket
  const addEventListener = useCallback(
    (event: string, handler: (data: any) => void) => {
      if (!socketRef.current) return () => {};

      socketRef.current.on(event, handler);
      return () => {
        socketRef.current?.off(event, handler);
      };
    },
    [],
  );

  // Emit event through socket
  const emitEvent = useCallback(
    (event: string, data: any, callback?: (response: any) => void) => {
      if (!socketRef.current || !isConnected) {
        console.warn('Socket not connected, cannot emit event:', event);
        return;
      }

      if (callback) {
        socketRef.current.emit(event, data, callback);
      } else {
        socketRef.current.emit(event, data);
      }
    },
    [isConnected],
  );

  useEffect(() => {
    if (!userId) return;

    // Initialize socket connection
    initializeSocket();

    // Set up app state change listener
    const subscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    // Clean up on unmount
    return () => {
      subscription.remove();

      // We don't disconnect the socket when unmounting individual components
      // The socket connection is managed at the app level
      // We just remove component-specific listeners
      if (socketRef.current) {
        // Keep the socket instance but remove specific listeners
        // that were added by this component
      }
    };
  }, [userId, handleAppStateChange, initializeSocket]);

  // Context value
  const value = {
    isConnected,
    addEventListener,
    emitEvent,
  };

  return (
    <SocketContext.Provider value={value}>{children}</SocketContext.Provider>
  );
};

// Custom hook to use the socket context
export const useSocket = () => {
  const context = useContext(SocketContext);
  if (context === undefined) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
