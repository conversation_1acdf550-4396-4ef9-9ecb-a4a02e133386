import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useRef,
  ReactNode,
} from 'react';

import * as Notifications from 'expo-notifications';
import { AppState, AppStateStatus, Linking } from 'react-native';
import { registerForPushNotificationsAsync } from '@/utils/registerForPushNotificationsAsync';
import useSendNotificationToken from '@/hooks/userHooks/useSendNotificationToken';

import {
  NotificationDeepLinkData,
  handleNotificationNavigation,
  parseNotificationDeepLink,
} from '@/utils/notificationDeepLinking';

import { customToast } from '@/hooks/useCustomToast';

interface NotificationContextType {
  expoPushToken: string | null;
  notification: Notifications.Notification | null;
  error: Error | null;
  handleNotificationPress: (
    notificationData: NotificationDeepLinkData,
  ) => boolean;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined,
);

export const useNotification = () => {
  const context = useContext(NotificationContext);

  if (context === undefined) {
    throw new Error(
      'useNotification must be used within a NotificationProvider',
    );
  }
  return context;
};

interface NotificationProviderProps {
  children: ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({
  children,
}) => {
  const [expoPushToken, setExpoPushToken] = useState<string | null>(null);
  const [notification, setNotification] =
    useState<Notifications.Notification | null>(null);

  const [error, setError] = useState<Error | null>(null);
  const appState = useRef(AppState.currentState);

  useSendNotificationToken(expoPushToken);

  const notificationListener = useRef<Notifications.Subscription | undefined>(
    undefined,
  );
  const responseListener = useRef<Notifications.Subscription | undefined>(
    undefined,
  );

  // Handle notification press
  const handleNotificationPress = (
    notificationData: NotificationDeepLinkData,
  ): boolean => {
    return handleNotificationNavigation(notificationData);
  };

  // Handle deep links
  const handleDeepLink = (event: { url: string }) => {
    const data = parseNotificationDeepLink(event.url);
    if (data) {
      handleNotificationPress(data);
    }
  };

  // Handle app state changes
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (
      appState.current.match(/inactive|background/) &&
      nextAppState === 'active'
    ) {
      // App has come to the foreground
      console.log('App has come to the foreground!');
    }
    appState.current = nextAppState;
  };

  useEffect(() => {
    // Register for push notifications
    registerForPushNotificationsAsync().then(
      (token) => setExpoPushToken(token),
      (error) => setError(error),
    );

    // Listen for incoming notifications
    notificationListener.current =
      Notifications.addNotificationReceivedListener((notification) => {
        console.log('🔔 Notification Received: ', notification);
        setNotification(notification);

        // Show a toast for the notification if app is in foreground
        if (
          appState.current === 'active' &&
          notification.request.content.title
        ) {
          customToast(notification.request.content.title, 'info', {
            onPress: () => {
              // Handle notification press from toast
              const data = notification.request.content
                .data as unknown as NotificationDeepLinkData;
              if (data && data.type && data.dataId) {
                handleNotificationPress(data);
              }
            },
          });
        }
      });

    // Listen for notification responses (when user taps on notification)
    responseListener.current =
      Notifications.addNotificationResponseReceivedListener((response) => {
        console.log(
          '🔔 Notification Response: ',
          JSON.stringify(response, null, 2),
          JSON.stringify(response.notification.request.content.data, null, 2),
        );

        // Handle the notification response here
        const data = response.notification.request.content
          .data as unknown as NotificationDeepLinkData;
        if (data && data.type && data.dataId) {
          handleNotificationPress(data);
        }
      });

    // Listen for deep links
    const subscription = Linking.addEventListener('url', handleDeepLink);

    // Check for initial deep link
    Linking.getInitialURL().then((url) => {
      if (url) {
        handleDeepLink({ url });
      }
    });

    // Listen for app state changes
    const appStateSubscription = AppState.addEventListener(
      'change',
      handleAppStateChange,
    );

    // Cleanup
    return () => {
      if (notificationListener.current) {
        subscription.remove();
      }
      if (responseListener.current) {
        subscription.remove();
      }
      subscription.remove();
      appStateSubscription.remove();
    };
  }, []);

  return (
    <NotificationContext.Provider
      value={{
        expoPushToken,
        notification,
        error,
        handleNotificationPress,
      }}
    >
      {children}
    </NotificationContext.Provider>
  );
};
