{"name": "shareikna-expo", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "dev": "APP_VARIANT=development npx expo start", "preview": "APP_VARIANT=preview npx expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll", "test:ci": "jest --ci --coverage --watchAll=false", "test:performance": "jest --testPathPattern=performance", "lint-expo": "expo lint", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write .", "build:android": "eas build --platform android --profile production", "build:ios": "eas build --platform ios --profile production", "build:preview": "eas build --platform all --profile preview", "analyze:bundle": "npx expo export --dump-assetmap", "analyze:deps": "npx depcheck", "performance:report": "node scripts/performance-report.js", "clean": "expo r -c && npm run clean:cache", "clean:cache": "rm -rf node_modules/.cache && rm -rf .expo && rm -rf dist", "precommit": "npm run lint:fix && npm run format && npm run test:ci"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@babel/runtime": "^7.26.10", "@expo/vector-icons": "^14.0.4", "@expo/webpack-config": "^19.0.1", "@faker-js/faker": "^9.6.0", "@gorhom/bottom-sheet": "^5", "@hookform/resolvers": "^4.1.3", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-community/datetimepicker": "8.3.0", "@react-native-google-signin/google-signin": "^13.2.0", "@react-native-menu/menu": "^1.2.2", "@react-navigation/bottom-tabs": "^7.2.0", "@react-navigation/material-top-tabs": "^7.2.13", "@react-navigation/native": "^7.0.14", "@tanstack/react-query": "^5.69.0", "axios": "^1.8.4", "date-fns": "^4.1.0", "dayjs": "^1.11.13", "dotenv": "^16.5.0", "expo": "^53.0.9", "expo-asset": "~11.1.5", "expo-auth-session": "~6.1.5", "expo-background-fetch": "~13.1.5", "expo-blur": "~14.1.4", "expo-build-properties": "~0.14.6", "expo-constants": "~17.1.6", "expo-crypto": "~14.1.4", "expo-dev-client": "~5.1.8", "expo-device": "~7.1.4", "expo-file-system": "~18.1.10", "expo-font": "~13.3.1", "expo-haptics": "~14.1.4", "expo-image": "~2.1.7", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.4", "expo-linking": "~7.1.5", "expo-localization": "~16.1.5", "expo-location": "~18.1.5", "expo-media-library": "~17.1.6", "expo-notifications": "~0.31.2", "expo-router": "~5.0.7", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.8", "expo-status-bar": "~2.2.3", "expo-symbols": "~0.4.4", "expo-system-ui": "~5.0.7", "expo-task-manager": "~13.1.5", "expo-updates": "~0.28.13", "expo-video": "~2.1.9", "expo-web-browser": "~14.1.6", "i18n-js": "^4.5.1", "i18next": "^24.2.3", "i18next-icu": "^2.3.0", "intl-messageformat": "^10.7.15", "intl-pluralrules": "^2.0.1", "lottie-react-native": "7.2.2", "nativewind": "^4.1.23", "package.json": "link:@react-native/gradle-plugin/package.json", "postcss": "^8.5.3", "react": "19.0.0", "react-dom": "19.0.0", "react-hook-form": "^7.54.2", "react-i18next": "^15.4.1", "react-native": "0.79.2", "react-native-clusterer": "^3.0.0", "react-native-css-interop": "^0.1.22", "react-native-element-dropdown": "^2.12.4", "react-native-gesture-handler": "~2.24.0", "react-native-get-random-values": "^1.11.0", "react-native-google-places-autocomplete": "^2.5.7", "react-native-ios-context-menu": "^3.1.0", "react-native-ios-utilities": "^5.1.2", "react-native-maps": "1.20.1", "react-native-otp-entry": "^1.8.4", "react-native-pager-view": "6.7.1", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.10.0", "react-native-svg": "15.11.2", "react-native-svg-transformer": "^1.5.1", "react-native-tab-view": "^4.1.0", "react-native-toast-message": "^2.3.0", "react-native-ui-datepicker": "^2.0.12", "react-native-vector-icons": "^10.2.0", "react-native-virtualized-view": "^1.0.0", "react-native-vision-camera": "^4.6.4", "react-native-web": "^0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "uuid": "^11.1.0", "zeego": "^2.0.4", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@babel/core": "^7.26.10", "@babel/plugin-transform-react-jsx": "^7.25.9", "@dev-plugins/react-query": "~0.2.0", "@eslint/js": "^9.24.0", "@tanstack/eslint-plugin-query": "^5.68.0", "@types/jest": "^29.5.14", "@types/react": "~19.0.10", "@types/react-test-renderer": "^18.3.1", "@typescript-eslint/eslint-plugin": "^8.29.0", "@typescript-eslint/parser": "^8.29.0", "babel-preset-expo": "~13.0.0", "eslint": "^9.24.0", "eslint-config-expo": "~9.2.0", "eslint-config-prettier": "^10.1.1", "eslint-plugin-expo": "^0.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-prettier": "^5.2.6", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-native": "^5.0.0", "expo-doctor": "^1.12.8", "globals": "^16.0.0", "jest": "^29.7.0", "jest-expo": "~53.0.5", "prettier": "3.5.3", "react-test-renderer": "18.3.1", "tailwindcss": "^3.4.17", "typescript": "^5.8.3"}, "private": true}