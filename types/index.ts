import { TextInputProps } from 'react-native';

export enum GENDER_ENUM {
  MALE = 'male',
  FEMALE = 'female',
}
export enum NOTIFICATIONS_TYPES {
  APPROVED_REQUEST = 'approve_request',
  REJECTED_REQUEST = 'reject_request',
  SEND_ACTIVITY_INVITE = 'send_activity_invite',
  JOIN_ACTIVITY = 'join_activity',
  CHAT_MESSAGE = 'chat_message',
  NEW_COMMENT = 'new_comment',
  NEW_LIKE = 'new_like',
  NEW_FOLLOWER = 'new_followers',
}

export interface ICategory {
  _id: string;
  createdAt?: Date;
  updatedAt?: Date;
  english: string;
  arabic: string;
  icon?: string;
}

export enum RESTRICTIONS_ENUM {
  public = 'public',
  private = 'private',
}

// file for common types
export interface ILocation {
  address?: string;
  latitude: number;
  longitude: number;
}

export interface IUser {
  preferences: { receiveNotifications: boolean };
  _id: string;
  address: string;
  categories: string[];
  createdAt: Date;
  userId?: string;
  dateOfBirth: Date;
  following: IUserInfo[];
  followers: IUserInfo[];
  displayName: string;
  email: string;
  isVerified: boolean;
  countryCode: string;
  eventsCount: number;
  followersCount: number;
  followingCount: number;
  gender: 'male' | 'female';
  isApple?: boolean;
  stripeCustomerId?: string;
  isOnline?: boolean;
  googleId: string;
  image: string;
  info: [];
  into: [];
  isAdmin: 'user' | 'admin';

  location: { latitude: number; longitude: number };
  password: string;
  phoneNumber: string;
  updatedAt: Date;
  userType: 'simple';
  userStatus: 'active';
  username: string;
}

export interface IExploreItem {
  _id: string;
  name: string;
  description: string;
  coverImage: string;
  images: string[];
  rating: {
    average: number;
    count: number;
  };
  categories: {
    _id: string;
    english: string;
    arabic: string;
    icon: string;
  }[];
  location: {
    latitude: number;
    longitude: number;
    _id: string;
  };
  tags: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ITopEventsItem {
  location: ILocation;
  _id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  city: string[];
  district: string[];
  country: string[];
  region: string[];
  status: 'active' | 'inactive';
  images: string[];
  organizerName: string;
  eventCategories: string[]; // TODO Make it typeof categories;
  price: number;
  type: 'event' | 'activity';
  contactInformation: string;
  description: string;
  startTime: string;
  endTime: string;
  activityToAdd: [];
  createdAt: Date;
  updatedAt: Date;
}

export interface IShortsItem {
  _id: string;
  userId: {
    displayName: string;
    username: string;
    image: string;
    _id: string;
  };
  title: string;
  description: string;
  videoUrl: string;
  likesCount: number;
  commentsCount: number;
  viewsCount: number;
  tags: string[];
  isPublic: boolean;
  createdAt: Date;
  updatedAt: Date;
  thumbnail: string;
  viewedBy: IUserInfo[];
  likedBy: IUserInfo[];
}

export interface INearYouItem {
  location: ILocation;
  pointsAssighnment: {
    likePoints: number;
    membersPoints: number;
  };
  _id: string;
  userId: string;
  name: string;
  category: {
    heading: string;
    subitems: [];
    _id: string;
  }[];
  nearYouAddresses: [];
  price: string;
  currency: string;
  socialCircles: [];
  selectedParticipants: [];
  startDate: Date;
  endDate: Date;
  images: string[];
  numberOfTickets: 0;
  startTime: string;
  endTime: string;
  type: string;
  description: string;
  members: [];
  state: string;
  participant: [];
  image: string;
  ratings: [];
  comments: [];
  createdAt: Date;
  updatedAt: Date;

  distance: {
    value: number;
    unit: 'km' | 'm';
  };
}

export interface IActivityItem {
  _id: string;
  address: string;
  category: ICategory[];
  comments: [];
  pendingParticipants: string[];
  createdAt: Date;
  userId: {
    displayName: string;
    _id: string;
    image: string;
    username: string;
  };
  currency: 'sar' | 'usd';
  description: string;
  endDate: Date;
  endTime: Date;
  images: string[];
  location: ILocation;
  maxParticipants: 380;
  members: [];
  name: 'Discover the best in KSA';
  nearYouAddresses: [];
  numberOfTickets: 0;
  participant: {
    participants: {
      _id: string;
      displayName: string;
      image: string;
      username: string;
    }[];
    total: number;
  };
  permissionToJoin: true;
  pointsAssignment: {
    likePoints: 0;
    membersPoints: 0;
  };
  price: string;
  ratings: [];
  restriction: RESTRICTIONS_ENUM;
  selectedParticipants: [];
  socialCircles: [];
  startDate: Date;
  startTime: Date;
  state: '';
  type: '';
  updatedAt: Date;
  virtual: boolean;
  averageRating: string;
  memberFollowersCounts: [];
  taskData: [];
  totalReviews: 0;
}

export interface IActivityParticipant {
  _id: string;
  displayName: string;
  image: string;
  isfollowed: boolean;
  username: string;
}

export interface IInterestItem {
  id: string;
  name: string;
  createdAt: Date;
}

export interface ISocialCircleItem {
  _id: string;
  image: string;
  name: string;
  description: string;
  members: string[];
}

export type IUserInfo = {
  _id: string;
  username: string;
  displayName: string;
  image?: string;
  name?: string;
};

export type ISocialCircle = {
  _id: string;
  image: string;
  name: string;
  description: string;
  userId: string;
  createdAt?: Date;
  members: IUserInfo[];
};

export type IEventItem = {
  location: ILocation;
  _id: string;
  name: string;
  startDate: Date;
  endDate: Date;
  city: string[];
  district: string[];
  country: string[];
  region: string[];
  status: 'inactive' | 'active';
  images: string[];
  organizerName: string;
  type: 'event';
  price: number;
  tags: string[];
  contactInformation: string;
  description: string;
  startTime: string;
  endTime: string;
  activityToAdd: [];
  createdAt: Date;
  updatedAt: Date;
};

export type IMediaItem = {
  likes: {
    likesCount: number;
    userId: string[];
  };
  _id: string;
  postedByUserId: IUserInfo;
  content: string;
  image: string[];
  tags: [''];
  sharesCount: number;
  createdAt: Date;
};

export type ILikesItem = {
  _id: string;
  content: string;
  image: string;
  sharesCount: number;
  postedByUserId: {
    _id: string;
    username: string;
    displayName: string;
    image: string;
  };
  createdAt: Date;
};

export type IPost = {
  _id: string;
  postedByUserId: {
    displayName: string;
    username: string;
    image: string;
    _id: string;
  };
  content: string;
  image: string[];
  src: string[];
  tags: string[];
  likes: {
    likesCount: number;
    userId: string[];
  };
  mention: string[];
  allowComments: boolean;
  visibility: 'public';
  socialCircle: { _id: string; name: string; image: string };
  sharesCount: number;
  comments: string[];
  share: string[];
  createdAt: Date;
  updatedAt: Date;
};

export type IRequestItem = {
  _id: string;
  activityName: string;
  createdAt: Date;
  type: 'admin' | 'request';
  pending: IUserInfo[];
  user: IUserInfo;
};

export type IComment = {
  _id: string;
  isOptimistic?: boolean;
  parentId: null | string;
  userId: {
    _id: string;
    username: string;
    displayName: string;
    image: string;
  };
  comment: string;
  createdAt: Date;
  likedBy: IUserInfo[];
  replyCount: number;
  likedCount: number;
};

export type IReplyItem = {
  commentId: string;
  parentId: string;
  userId: IUserInfo;
  reply: string;
  images: string[];
  likedBy: string[];
  _id: string;
  createdAt: Date;
  updatedAt: Date;
};
export type INestedCommentsProps = {
  comments: IComment[];
  onSubmit?: (content: string) => void;
  onEdit?: (content: string) => void;
  onDelete?: (commentId: string) => void;
  onUpVote?: (commentId: string) => void;
  onDownVote?: (commentId: string) => void;
  onLike?: (commentId: string) => void;
  inputProps?: TextInputProps;
  isDark?: boolean;
};

export type IReviewItem = {
  _id: string;
  review: string;
  createdAt: Date;
  userId: IUserInfo;
};

export interface IFollower {
  _id: string;
  displayName: string;
  email: string;
  image: string;
  username: string;
  isFollowed?: boolean;
}

export interface INotificationItem {
  createdAt: Date;
  dataId: string;
  description: string;
  read: boolean;
  title: string;
  type: NOTIFICATIONS_TYPES;
  userId: IUserInfo;
  _id: string;
}

export interface IMessage {
  _id: string;
  createdAt: Date;
  text: string;
  senderId: string;
  recipientId: string;
  pending?: boolean;
  isRead?: boolean; // Add this property to track seen status
}
