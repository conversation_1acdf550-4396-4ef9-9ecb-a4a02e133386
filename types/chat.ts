export interface ChatMessage {
  id: string;
  text: string;
  senderId: string;
  receiverId: string;
  timestamp: string;
  createdAt: Date;
  type: 'text' | 'image';
  status?: 'sent' | 'delivered' | 'read';
  pending?: boolean;
  isRead?: boolean;
}

export interface IChatRoom {
  _id: string;
  type: 'group' | 'single';
  title: string;
  image: string;
  members: string[];
  createdBy: string;
  firebaseRoomId: string;
  createdAt: Date;
  updatedAt: Date;
  unseenCount: number;
}

export interface IDirectChats {
  user_id: string;
  username: string;
  displayName: string;
  profile_picture: string;
  is_follower: boolean;
  is_following: boolean;
  last_message: {
    content: string;
    timestamp: Date;
    sender_id: string;
  };
  unseen_message_count: number;
  is_online: boolean;
}
