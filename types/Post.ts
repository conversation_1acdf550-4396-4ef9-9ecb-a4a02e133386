import { IUserInfo } from '.';

export interface IPostType {
  _id: string;
  postedByUserId: IUserInfo;
  content: string;
  image: string[];
  tags: string[];
  allowComments: boolean;
  visibility: 'public' | 'private';
  comments: string[];
  createdAt: Date;
  updatedAt: Date;
}
/*

 _id: string;
  postedByUserId: {
    displayName: string;
    username: string;
    image: string;
    _id: string;
  };
  content: string;
  image: string[];
  src: string[];
  tags: string[];
  likes: {
    likesCount: number;
    userId: string[];
  };
  mention: string[];
  allowComments: boolean;
  visibility: 'public';
  socialCircle: { _id: string; name: string; image: string };
  sharesCount: number;
  comments: string[];
  share: string[];
  createdAt: Date;
  updatedAt: Date;

*/
