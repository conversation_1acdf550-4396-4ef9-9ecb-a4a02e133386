import axiosInstance from '@/utils/axiosInstance';
import { INotificationItem } from '@/types';

// --- Response Types ---

export interface INotificationsAPIPayload {
  success: boolean;
  message: string;
  data: INotificationItem[];
}

export interface IUpdateNotificationsAPIResponse {
  success: boolean;
  message: string;
  updatedData: Array<{
    notificationId: string;
    userId: string;
    title: string;
    description: string;
    tokenId: string;
    createdAt: Date;
    updatedAt: Date;
    read: boolean;
  }>;
}

export interface IToggleNotificationsAPIResponse {
  success: boolean;
  message: string;
}

// --- Helpers ---

const handleRequest = async <T>(request: Promise<{ data: T }>): Promise<T> => {
  try {
    const { data } = await request;
    return data;
  } catch (error) {
    throw error; // could log or wrap the error here if needed
  }
};

// --- API Methods ---

export const getUserNotificationsAPI = async (
  userId: string,
): Promise<INotificationsAPIPayload> => {
  const params = new URLSearchParams({ userId });
  return handleRequest(
    axiosInstance.get<INotificationsAPIPayload>(
      `/user/auth/userNotification?${params}`,
    ),
  );
};

export const updateUserNotificationSeenStatusAPI = async (
  notificationId: string,
): Promise<IUpdateNotificationsAPIResponse> => {
  return handleRequest(
    axiosInstance.put<IUpdateNotificationsAPIResponse>(
      '/user/auth/updateSeenStatus',
      {
        notificationId,
      },
    ),
  );
};

export const toggleUserNotificationSeenStatusAPI = async (
  receiveNotifications: boolean,
): Promise<IToggleNotificationsAPIResponse> => {
  return handleRequest(
    axiosInstance.patch<IToggleNotificationsAPIResponse>(
      '/user/auth/notificationSettings',
      {
        receiveNotifications,
      },
    ),
  );
};
