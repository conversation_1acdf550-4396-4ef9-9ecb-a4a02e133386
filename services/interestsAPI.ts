import { IInterestItem } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

type IGetAllInterestsResponse = {
  success: boolean;
  message: string;
  data: IInterestItem[];
};

export const getAllInterestsAPI =
  async (): Promise<IGetAllInterestsResponse> => {
    try {
      const response = await axiosInstance.get<IGetAllInterestsResponse>(
        '/interest/index/getAllInterests',
      );
      return response.data;
    } catch (error) {
      throw error;
    }
  };

type getInterestsByUserIdResponse = {
  success: boolean;
  message: string;
  data: {
    userId: string;
    interests: { interestId: string; interestName: string }[];
  };
};
export const getInterestsByUserIdAPI = async (
  userId: string,
): Promise<getInterestsByUserIdResponse> => {
  try {
    const response = await axiosInstance.get<getInterestsByUserIdResponse>(
      `/interest/index/getInterestByUserId?userId=${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
