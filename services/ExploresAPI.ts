import { IExploreItem, IReviewItem } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

export interface IGetExploreAPIResponse {
  success: boolean;
  message: string;
  data: IExploreItem[];
}

// Get Explore API
export interface IAPIResponse<T> {
  success: boolean;
  message: string;
  data: T;
  total: number;
}

export interface IGetEXploreAPIParams {
  id?: string;
  categories?: string;
  rating?: string;
  latitude?: number;
  longitude?: number;
  tags?: string[];
  page: number;
  limit?: number;
}
export const GetExploreAPI = async (
  params: IGetEXploreAPIParams,
): Promise<IAPIResponse<IExploreItem[]> | null> => {
  try {
    const response = await axiosInstance.get<IAPIResponse<IExploreItem[]>>(
      '/explore/index/getExplores',
      {
        params,
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

// Get Single explore

interface ISingleExploreApiResponse {
  success: boolean;
  message: string;
  data: IExploreItem[];
}

export const getExploreById = async ({
  id,
}: {
  id: string;
}): Promise<ISingleExploreApiResponse | null> => {
  try {
    const response = await axiosInstance.get(
      `/explore/index/getExplores?id=${id}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IAddExploreReviewResponse = {
  success: boolean;
  message: string;
};
export const addExploreReviewAPI = async ({
  exploreId,
  review,
}: {
  exploreId: string;
  review: string;
}): Promise<IAddExploreReviewResponse> => {
  try {
    const response = await axiosInstance.post<IAddExploreReviewResponse>(
      '/explore/index/addReview',
      {
        exploreId,
        review,
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IExploreResponse = {
  success: boolean;
  reviews: {
    exploreId: string;
    reviews: IReviewItem[];
  };
};

export const getExploreReviewAPI = async (
  exploreId: string,
): Promise<IExploreResponse> => {
  try {
    const response = await axiosInstance.get<IExploreResponse>(
      `/explore/index/getReview?exploreId=${exploreId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
