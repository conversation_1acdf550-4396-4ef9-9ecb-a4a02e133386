import { IPost, ISocialCircle, ISocialCircleItem, IUserInfo } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

export type ICreateSocialCirclesPayload = {
  name: string;
  members: string;
  description: string;
  image: string;
};
type createSocialCircleAPIResponse = {
  success: boolean;
  message: string;
  socialCircle: ISocialCircleItem[];
};
/**
 *
 * sent as multi part form data axios by default handles this
 
 */
export const createSocialCircleAPI = async (
  payload: FormData,
): Promise<createSocialCircleAPIResponse> => {
  try {
    const response = await axiosInstance.post<createSocialCircleAPIResponse>(
      `/socialCircle/index/createSocialCircle`,
      payload,
      {
        headers: {
          accept: 'application/json',
          'content-type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // Ensures FormData is sent correctly
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type socialCircleByUsersIDResponse = {
  success: boolean;
  message: string;
  data: {
    creator: IUserInfo;
    socialCircles: ISocialCircle[];
    otherSocialCircles: ISocialCircle[];
  };
};

// get social circles for user
export const getSocialCircleByUserID = async (
  id: string,
): Promise<socialCircleByUsersIDResponse> => {
  try {
    const response = await axiosInstance.get<socialCircleByUsersIDResponse>(
      `/socialCircle/index/getSocialCircleByUserId?userId=${id}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type getSocialCircleByIDResponse = {
  success: boolean;
  message: string;
  data: {
    creator: IUserInfo;
    socialCircle: ISocialCircleItem;
  };
};

export const getSocialCircleByID = async (
  id: string,
): Promise<getSocialCircleByIDResponse> => {
  try {
    const response = await axiosInstance.get<getSocialCircleByIDResponse>(
      `/socialCircle/index/getSocialCircleById?id=${id}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IDeleteSocialCircleByIDResponse = {
  message: string;
  status: number;
};

export const DeleteSocialCircleAPI = async (
  id: string,
): Promise<IDeleteSocialCircleByIDResponse> => {
  try {
    const response =
      await axiosInstance.delete<IDeleteSocialCircleByIDResponse>(
        `/socialCircle/index/deleteSocialCircleById/${id}`,
      );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface IUpdateSocialCircleResponse {
  success: boolean;
  message: string;
  socialCircle: ISocialCircleItem;
}

export const UpdateSocialCircleAPI = async ({
  _id,
  payload,
}: {
  _id: string;
  payload: FormData;
}): Promise<IUpdateSocialCircleResponse> => {
  try {
    const response = await axiosInstance.put<IUpdateSocialCircleResponse>(
      `/socialCircle/index/updateSocialCircle/${_id}`,
      payload,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export type ICreateSocialCirclePostPayload = {
  content: string;
  tags: string; // coma serrated tags
  image: string[];
  socialCircle: string; // id for social circle
};

interface IGetPostByCircleIDResponse {
  success: boolean;
  data: IPost[];
}

export const getPostsBySocialCirclesIdAPI = async (
  socialCircleId: string,
): Promise<IGetPostByCircleIDResponse> => {
  try {
    const response = await axiosInstance.get<IGetPostByCircleIDResponse>(
      `/posts/index/getPostBySocialCircleId?socialCircleId=${socialCircleId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
