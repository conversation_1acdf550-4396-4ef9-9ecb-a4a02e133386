import { ILikesItem, ILocation, IMediaItem, IPost } from '@/types';
import { IPostType } from '@/types/Post';
import axiosInstance from '@/utils/axiosInstance';

interface IGetProfileDataResponse {
  success: boolean;
  data: {
    preferences: { reciveNotifications: boolean };
    _id: string;
    username: string;
    email: string;
    displayName: string;
    phoneNumber: string;
    address: string;
    followersCount: string;
    followingCount: string;
    userType: string;
    location: ILocation;
    image: string;
    dateOfBirth: string;
    gender: string;
    createdAt: string;
  };
}

export const getProfileDataAPI = async (): Promise<IGetProfileDataResponse> => {
  try {
    const response = await axiosInstance.get<IGetProfileDataResponse>(
      `/user/profile/getProfileDetails`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IGetAllPostsAPI = {
  currentPage: number;
  data: IPost[];
  message: string;
  success: boolean;
  total: number;
  totalPages: number;
};

export const getAllPostsAPI = async (
  page: number = 1,
  limit: number = 10,
): Promise<IGetAllPostsAPI> => {
  try {
    const response = await axiosInstance.get<IGetAllPostsAPI>(
      `/user/profile/getAllPosts`,
      {
        params: {
          page,
          limit,
        },
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface IGetFollowerPostAPIResponse {
  success: boolean;
  message: string;
  data: IPostType[];
  pagination: {
    page: number;
    limit: number;
  };
}

export const getFollowersPostsAPI = async ({
  page = 1,
  limit = 10,
}: {
  page?: number;
  limit?: number;
}): Promise<IGetFollowerPostAPIResponse> => {
  try {
    const response = await axiosInstance.get<IGetFollowerPostAPIResponse>(
      '/posts/index/getFollowingPosts',
      {
        params: { page, limit },
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface IGetAllMediaResponse {
  success: boolean;
  message: string;
  total: number;
  data: IMediaItem[];
}

export const getAllMediaAPI = async (): Promise<IGetAllMediaResponse> => {
  try {
    const response = await axiosInstance.get<IGetAllMediaResponse>(
      '/user/profile/getAllMedia',
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export type IGetMediaByUserIdResponse = {
  success: boolean;
  message: string;
  total: number;
  currentPage: number;
  totalPages: number;
  data: IMediaItem[];
  pages?: { data: IMediaItem[] }[];
};

type IGetMediaByUserIDPayload = {
  userId: string;
  page?: number;
  limit?: number;
};
export const getMediaByUserIdAPI = async (
  params: IGetMediaByUserIDPayload,
): Promise<IGetMediaByUserIdResponse> => {
  try {
    const response = await axiosInstance.get<IGetMediaByUserIdResponse>(
      `/posts/index/getPostMedia`,
      {
        params,
      },
    );

    if (!response.data.success) {
      throw new Error(response.data.message);
    }

    return response.data;
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`Failed to fetch user media: ${error.message}`);
    }
    throw new Error('Failed to fetch user media');
  }
};
interface IGetUserLikesAPI {
  success: boolean;
  message: string;
  total: number;
  data: ILikesItem[];
}
export const getUserLikesAPI = async (
  userId: string,
): Promise<IGetUserLikesAPI> => {
  try {
    const response = await axiosInstance.get<IGetUserLikesAPI>(
      `/user/profile/getAllLikes?userId=${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const followUserAPI = async (userIdToFollow: string) => {
  try {
    const response = await axiosInstance.post('/user/profile/followUser', {
      userIdToFollow,
    });
    return response;
  } catch (error) {
    throw error;
  }
};

type IGetPostsByUserIdAPIResponse = {
  success: boolean;
  message: string;
  posts: IPost[];
};

export const GetPostsByUserIdAPI = async (
  userId: string,
): Promise<IGetPostsByUserIdAPIResponse> => {
  try {
    const response = await axiosInstance.get<IGetPostsByUserIdAPIResponse>(
      `/posts/index/getPostsByUserId?userId=${userId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
