import { IComment, IReplyItem } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

// type ICommentPayload = {
//   postId: string
//   comment: string
//   images?: string[]
// }

type ICreateCommentResponse = {
  success: boolean;
  message: string;
  comment: IComment;
};

export const createCommentAPI = async (
  formData: FormData,
): Promise<ICreateCommentResponse> => {
  try {
    const response = await axiosInstance.post<ICreateCommentResponse>(
      `/posts/index/createComments`,
      formData,
      {
        headers: {
          accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // ensure it sends raw FormData
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export type IGetCommentsPayload = {
  postId: string;
  limit?: number;
  page?: number;
};

type IGetCommentsResponse = {
  success: boolean;
  total: number;
  limit: number;
  page: number;
  data: {
    postId: string;
    comments: IComment[];
  };
};

export const getAllPostCommentsById = async (
  payload: IGetCommentsPayload,
): Promise<IGetCommentsResponse> => {
  try {
    const response = await axiosInstance.get<IGetCommentsResponse>(
      `/posts/index/getAllCommentsByPostId`,
      { params: payload }, // Pass payload dynamically as query params
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IReplyCommentPayload = {
  success: boolean;
  message: string;
  data: IReplyItem;
};

// commentId: string
// replyId: string
// images: string[]
export const replayPostCommentAPI = async (
  formData: FormData,
): Promise<IReplyCommentPayload> => {
  try {
    const response = await axiosInstance.post<IReplyCommentPayload>(
      '/posts/index/replyToComment',
      formData,
      {
        headers: {
          accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // ensure it sends raw FormData
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IDeleteCommentResponse = {
  success: boolean;
  message: string;
};

export const deleteCommentAPI = async (
  commentId: string,
): Promise<IDeleteCommentResponse> => {
  try {
    const response = await axiosInstance.delete<IDeleteCommentResponse>(
      `/posts/index/deleteComment?commentId=${commentId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IUpdateCommentAPIResponse = {
  success: boolean;
  message: string;
  data: IComment;
};

/*
  form data as CommentId:string | null , and comment:string
*/
export const updateCommentAPI = async (
  formData: FormData,
): Promise<IUpdateCommentAPIResponse> => {
  try {
    const response = await axiosInstance.put<IUpdateCommentAPIResponse>(
      `/posts/index/updateComments`,
      formData,
      {
        headers: {
          accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // ensure it sends raw FormData
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type likeAndUnlikeCommentAPIResponse = {
  success: boolean;
  message: string;
};

export const likeCommentAPI = async (
  commentId: string,
): Promise<likeAndUnlikeCommentAPIResponse> => {
  try {
    const response = await axiosInstance.post<likeAndUnlikeCommentAPIResponse>(
      `/posts/index/likedComment`,
      { commentId },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const unLikeComment = async (
  commentId: string,
): Promise<likeAndUnlikeCommentAPIResponse> => {
  try {
    const response = await axiosInstance.post<likeAndUnlikeCommentAPIResponse>(
      `/posts/index/unlikedComment`,
      { commentId },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type getRepliesByCommentId = {
  success: boolean;
  message: string;
  data: {
    commentId: string;
    replies: IReplyItem[];
  };
};

export const getRepliesByCommentIdAPI = async (
  commentId: string,
): Promise<getRepliesByCommentId> => {
  try {
    const response = await axiosInstance.get<getRepliesByCommentId>(
      `/posts/index/getRepliesByCommentId?commentId=${commentId}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
