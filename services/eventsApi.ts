import { IEventItem, ITopEventsItem } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

export interface IGetAllEventsAPIPayload {
  page?: number;
  region?: string;
  country?: string;
  city?: string;
  district?: string;
}

type iGetAllEventsResponse = {
  success: boolean;
  total: number;
  limit: number;
  data: IEventItem[];
};
export const getAllEventsAPI = async ({
  page = 1,
  region,
  country,
  city,
  district,
}: IGetAllEventsAPIPayload): Promise<iGetAllEventsResponse> => {
  try {
    const response = await axiosInstance.get<iGetAllEventsResponse>(
      `/club/index/allEvents?page${page}&region=${region}&country=${country}&city=${city}&district=${district}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type ISearchEventsAPIResponse = {
  appliedFilters: {
    type: 'eventName';
    value: string;
  }[];
  currentPage: number;
  data: ITopEventsItem[];
};

export const SearchEventAPI = async (
  eventName: string,
  page?: number,
): Promise<ISearchEventsAPIResponse> => {
  try {
    const response = await axiosInstance.get<ISearchEventsAPIResponse>(
      `/club/index/searchEvents?eventName=${eventName}&page=${page}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IGetEventsByIdResponse = {
  success: boolean;
  data: IEventItem;
};

export const getEventsByIdAPI = async (
  id: string,
): Promise<IGetEventsByIdResponse> => {
  try {
    const response = await axiosInstance.get<IGetEventsByIdResponse>(
      `/club/index/getEventById?id=${id}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

export interface IGetTopEventsAPIResponse {
  message: string;
  data: ITopEventsItem[];
}

// Get Top Events (Near you events )
export const GetTopEventsAPI = async ({
  limit = 3,
  categories = '',
}: {
  limit?: number;
  categories?: string;
}): Promise<IGetTopEventsAPIResponse | null> => {
  try {
    const response = await axiosInstance.get<IGetTopEventsAPIResponse>(
      `/club/index/topEvents?limit=${limit}${categories && `?categories=${categories}`}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
