import { IPost } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

interface IPostByIdResponse {
  success: boolean;
  message: string;
  data: IPost;
}
export const getPostByIdAPI = async (
  id: string,
): Promise<IPostByIdResponse> => {
  try {
    const response = await axiosInstance.get<IPostByIdResponse>(
      `/posts/index/getPostsById?id=${id}`,
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

type IDeletePostResponse = {
  success: boolean;
  message: string;
};
export const deletePostAPI = async (
  postId: string,
): Promise<IDeletePostResponse> => {
  try {
    const response = await axiosInstance.delete<IDeletePostResponse>(
      `/posts/index/deletePost`,
      {
        data: {
          postId,
        },
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface IIncrementPostLikeResponse {
  message: string;
  isLiked: boolean;
  posts: IPost;
}

export const incrementPostLikeAPI = async (
  id: string,
): Promise<IIncrementPostLikeResponse> => {
  try {
    const response = await axiosInstance.post<IIncrementPostLikeResponse>(
      `/posts/index/incrementLikes`,
      { id },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface IDecrementPostLike {
  message: string;
  isLiked: boolean;
  posts: IPost;
}
export const decrementPostLikeAPI = async (
  id: string,
): Promise<IDecrementPostLike> => {
  try {
    const response = await axiosInstance.post<IDecrementPostLike>(
      '/posts/index/decrementLikes',
      {
        id,
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};

interface ICreateSocialCirclePostResponse {
  success: boolean;
  message: string;
  post: IPost;
}

export const CreatePostAPI = async (
  payload: FormData,
): Promise<ICreateSocialCirclePostResponse> => {
  try {
    const response = await axiosInstance.post<ICreateSocialCirclePostResponse>(
      `/posts/index/createPost`,
      payload,
      {
        headers: {
          accept: 'application/json',
          'content-type': 'multipart/form-data',
        },
        transformRequest: (data) => data, // Ensures FormData is sent correctly
      },
    );
    return response.data;
  } catch (error) {
    throw error;
  }
};
