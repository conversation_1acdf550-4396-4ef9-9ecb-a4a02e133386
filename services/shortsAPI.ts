import { IShortsItem } from '@/types';
import axiosInstance from '@/utils/axiosInstance';

// Common response interface for success/message
interface IBaseResponse {
  success: boolean;
  message: string;
}

// Types for upload short payload
export interface UploadShortAPIPayloadTypes {
  file: string;
  userId: string;
  title: string;
  description: string;
  tags: string[];
}

// Response type for upload shorts
interface IUploadShortsAPIResponse extends IBaseResponse {
  fileUrl: string;
  metadata: {
    userId: string;
    title: string;
    description: string;
    tags: string[];
  };
}

/**
 * Uploads a new short (video/image) with metadata
 * @param payload FormData containing file and metadata
 */
export const UploadShortAPI = async (
  payload: FormData,
): Promise<IUploadShortsAPIResponse | null> => {
  try {
    const response = await axiosInstance.post<IUploadShortsAPIResponse>(
      `/upload/index/uploadShorts`,
      payload,
      {
        headers: {
          accept: 'application/json',
          'content-type': 'multipart/form-data',
        },
        transformRequest: (data) => data,
      },
    );
    return response.data;
  } catch (error: any) {
    console.error('UploadShortAPI Error:', error?.response?.data || error);
    throw new Error(
      error?.response?.data?.message || 'Failed to upload short video',
    );
  }
};

/**
 * Deletes a short by ID
 * @param shortId ID of the short to delete
 */
export const DeleteShortAPI = async ({
  shortId,
}: {
  shortId: string;
}): Promise<IBaseResponse | null> => {
  const response = await axiosInstance.delete<IBaseResponse>(
    `/posts/index/deleteShort`,
    { data: shortId },
  );
  return response.data;
};

// Response type for getting shorts
interface IGetShortsAPIResponse extends IBaseResponse {
  total: number;
  data: IShortsItem[];
}

/**
 * Retrieves shorts with pagination and filtering
 * @param params.shortId Optional - Specific short ID
 * @param params.page Required - Page number (default: 1)
 * @param params.userId Optional - Filter by user ID
 * @param params.limit Optional - Items per page
 */
export const GetShortsAPI = async ({
  shortId,
  page = 1,
  limit,
  userId,
}: {
  shortId?: string;
  page: number;
  userId?: string;
  limit?: number;
}): Promise<IGetShortsAPIResponse> => {
  const params = new URLSearchParams({
    page: page.toString(),
    ...(shortId && { shortId }),
    ...(userId && { userId }),
    ...(limit && { limit: limit.toString() }),
  });

  const response = await axiosInstance.get<IGetShortsAPIResponse>(
    `/posts/index/getShorts?${params.toString()}`,
  );
  return response.data;
};

/**
 * Deletes a short by ID (alternative endpoint)
 * @param shortId ID of the short to delete
 */
export const deleteShortAPI = async (
  shortId: string,
): Promise<IBaseResponse> => {
  const response = await axiosInstance.delete<IBaseResponse>(
    `/posts/index/deleteShorts`,
    { data: { shortId } },
  );
  return response.data;
};

/**
 * Increments view count for a short
 * @param shortId ID of the short
 */
export const viewShortAPI = async (shortId: string): Promise<IBaseResponse> => {
  const response = await axiosInstance.put(`/posts/index/viewedShorts`, {
    shortId,
  });
  return response.data;
};

/**
 * Likes a short
 * @param shortId ID of the short
 */
export const likeShortAPI = async (shortId: string): Promise<IBaseResponse> => {
  const response = await axiosInstance.put(`/posts/index/likedShorts`, {
    shortId,
  });
  return response.data;
};

/**
 * Unlikes a short
 * @param shortId ID of the short
 */
export const unLikeShortAPI = async (
  shortId: string,
): Promise<IBaseResponse> => {
  const response = await axiosInstance.put(`/posts/index/unlikedShorts`, {
    shortId,
  });
  return response.data;
};

/**
 * Adds a comment to a short
 * @param payload Object containing shortId and comment text
 */
export const commentShortAPI = async (payload: {
  shortId: string;
  comment: string;
}): Promise<IBaseResponse> => {
  const response = await axiosInstance.put(
    `/posts/index/commentShorts`,
    payload,
  );
  return response.data;
};
