{
  "cli": {
    "version": ">= 14.4.0",
    "appVersionSource": "remote"
  },
  "build": {
    "local-apk": {
      "android": {
        "buildType": "apk",
        "distribution": "internal"
      },
      "env": {
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-otjv9bthuasafsb0v7b66ef2btut7psn"
      }
    },
    "preview-apk": {
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleDebug", // Change to debug build
        "distribution": "internal"
      },
      "autoIncrement": true,
      "env": {
        "APP_VARIANT": "preview",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-9rnfsp1dg87e8gi3o9s1n2ka0e3vmnse"
      }
    },
    "preview-aab": {
      "android": {
        "buildType": "app-bundle",
        "distribution": "internal"
      },
      "autoIncrement": true,
      "env": {
        "APP_VARIANT": "preview",
        "NODE_ENV": "preview",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-9rnfsp1dg87e8gi3o9s1n2ka0e3vmnse"
      }
    },
    "preview-ios": {
      "ios": {
        "distribution": "internal",
        "buildConfiguration": "Release"
      },
      "autoIncrement": true,
      "env": {
        "APP_VARIANT": "preview",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-9rnfsp1dg87e8gi3o9s1n2ka0e3vmnse"
      }
    },
    "production-apk": {
      "android": {
        "buildType": "apk",
        "distribution": "internal"
      },
      "env": {
        "APP_VARIANT": "production",
        "NODE_ENV": "production",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-otjv9bthuasafsb0v7b66ef2btut7psn"
      }
    },
    "production-test": {
      "android": {
        "buildType": "apk",
        "distribution": "internal"
      },
      "ios": {
        "simulator": true
      },
      "env": {
        "APP_VARIANT": "production",
        "NODE_ENV": "production",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-otjv9bthuasafsb0v7b66ef2btut7psn"
      }
    },
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "simulator": true
      },
      "env": {
        "APP_VARIANT": "development",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-f4sticiqtaa6c3hkhdimj3qqej4441ch"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "simulator": true
      },
      "env": {
        "APP_VARIANT": "preview",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-9rnfsp1dg87e8gi3o9s1n2ka0e3vmnse"
      }
    },
    "preview-simulator": {
      "extends": "preview",
      "ios": {
        "simulator": true,
        "buildConfiguration": "Debug"
      },
      "android": {
        "buildType": "apk",
        "gradleCommand": ":app:assembleDebug"
      },
      "developmentClient": true,
      "env": {
        "APP_VARIANT": "preview",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-9rnfsp1dg87e8gi3o9s1n2ka0e3vmnse"
      }
    },
    "production": {
      "autoIncrement": true,
      "android": {
        "buildType": "app-bundle",
        "distribution": "store"
      },
      "ios": {
        "distribution": "store"
      },
      "env": {
        "APP_VARIANT": "production",
        "NODE_ENV": "production",
        "EXPO_PUBLIC_IOS_URL_SCHEME": "com.googleusercontent.apps.383390341391-otjv9bthuasafsb0v7b66ef2btut7psn"
      }
    }
  },
  "submit": {
    "production": {}
  }
}
