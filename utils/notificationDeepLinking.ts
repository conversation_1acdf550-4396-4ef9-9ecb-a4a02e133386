import { NOTIFICATIONS_TYPES } from '@/types';
import { router } from 'expo-router';

/**
 * Interface for notification data used in deep linking
 */
export interface NotificationDeepLinkData {
  type: NOTIFICATIONS_TYPES;
  dataId: string;
  userId?: string;
}

/**
 * Handles navigation based on notification type and data
 *
 * @param notification - The notification data containing type and IDs
 * @returns boolean - Whether the navigation was successful
 */
export const handleNotificationNavigation = (
  notification: NotificationDeepLinkData,
): boolean => {
  if (!notification || !notification.type || !notification.dataId) {
    console.error('Invalid notification data for navigation', notification);
    return false;
  }

  try {
    switch (notification.type) {
      case NOTIFICATIONS_TYPES.CHAT_MESSAGE:
        // Navigate to the specific chat
        router.push({
          pathname: '/(protected)/(tabs)/messages',
          params: { id: notification.dataId },
        });
        break;

      case NOTIFICATIONS_TYPES.NEW_COMMENT:
        // Navigate to the post with comments
        router.push({
          pathname: '/(protected)/socialCircles/post/[id]',
          params: { id: notification.dataId },
        });
        break;

      case NOTIFICATIONS_TYPES.NEW_LIKE:
        // Navigate to the post that was liked
        router.push({
          pathname: '/(protected)/profile/post/[id]',
          params: { id: notification.dataId },
        });
        break;

      case NOTIFICATIONS_TYPES.NEW_FOLLOWER:
        // Navigate to the profile of the user who followed
        if (notification.userId) {
          router.push({
            pathname: '/(protected)/(tabs)/profile',
            params: { id: notification.userId },
          });
        }
        break;

      case NOTIFICATIONS_TYPES.JOIN_ACTIVITY:
      case NOTIFICATIONS_TYPES.APPROVED_REQUEST:
      case NOTIFICATIONS_TYPES.REJECTED_REQUEST:
      case NOTIFICATIONS_TYPES.SEND_ACTIVITY_INVITE:
        // Navigate to the specific activity
        router.push(`/(protected)/activity/${notification.dataId}`);
        break;

      default:
        // For unknown types, navigate to the notifications screen
        router.push('/(protected)/notifications');
        return false;
    }
    return true;
  } catch (error) {
    console.error('Error navigating from notification:', error);
    return false;
  }
};

/**
 * Creates a deep link URL for notifications
 *
 * @param notification - The notification data
 * @returns string - The deep link URL
 */
export const createNotificationDeepLink = (
  notification: NotificationDeepLinkData,
): string => {
  const baseUrl = 'shareikna://';
  const params = new URLSearchParams();

  params.append('type', notification.type);
  params.append('dataId', notification.dataId);

  if (notification.userId) {
    params.append('userId', notification.userId);
  }

  return `${baseUrl}notification?${params.toString()}`;
};

/**
 * Parses a deep link URL into notification data
 *
 * @param url - The deep link URL
 * @returns NotificationDeepLinkData | null - The parsed notification data or null if invalid
 */
export const parseNotificationDeepLink = (
  url: string,
): NotificationDeepLinkData | null => {
  try {
    // Check if it's a notification deep link
    if (!url || !url.includes('notification')) {
      return null;
    }

    // Extract the query parameters
    const queryString = url.split('?')[1];
    if (!queryString) return null;

    const params = new URLSearchParams(queryString);
    const type = params.get('type') as NOTIFICATIONS_TYPES;
    const dataId = params.get('dataId');
    const userId = params.get('userId');

    if (!type || !dataId) return null;

    return {
      type,
      dataId,
      userId: userId || undefined,
    };
  } catch (error) {
    console.error('Error parsing notification deep link:', error);
    return null;
  }
};
