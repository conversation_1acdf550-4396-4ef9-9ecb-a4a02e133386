import { ImagePickerAsset } from 'expo-image-picker';
import { Platform } from 'react-native';

export const changeAssetToFormData = (
  formData: FormData,
  appendName: string,
  image: ImagePickerAsset,
) => {
  const imageFile = {
    uri: Platform.OS === 'ios' ? image.uri.replace('file://', '') : image.uri,
    name: `upload_${Date.now()}.jpg`, // ✅ Give a proper filename
    type: image.mimeType || 'image/jpeg', // ✅ Ensure type is set
  };

  formData.append(appendName, imageFile as any);
};
