/**
 * Map clustering utilities inspired by the Medium article approach
 * Provides configuration and helper functions for optimal clustering performance
 */

import { SCREEN_HEIGHT, SCREEN_WIDTH } from '@/constants/Theme';

// Clustering configuration based on the Medium article recommendations
export const CLUSTER_CONFIG = {
  // Radius for clustering points (in pixels)
  radius: 50,

  // Maximum zoom level for clustering
  maxZoom: 16,

  // Minimum number of points to form a cluster
  minPoints: 2,

  // Padding around clusters
  padding: 10,

  // Screen dimensions for clustering calculations
  dimensions: {
    width: SCREEN_WIDTH,
    height: SCREEN_HEIGHT,
  },
};

// Performance optimization settings
export const CLUSTER_PERFORMANCE = {
  // Reduce re-renders by limiting updates
  updateThrottle: 100, // ms

  // Batch size for processing large datasets
  batchSize: 50,

  // Maximum number of markers to render at once
  maxMarkersToRender: 200,
};

// Cluster size categories based on point count
export const CLUSTER_SIZES = {
  small: {
    range: [2, 9],
    size: 50,
    textSize: 12,
    borderWidth: 3,
  },
  medium: {
    range: [10, 24],
    size: 65,
    textSize: 14,
    borderWidth: 4,
  },
  large: {
    range: [25, 49],
    size: 80,
    textSize: 16,
    borderWidth: 5,
  },
  extraLarge: {
    range: [50, Infinity],
    size: 95,
    textSize: 18,
    borderWidth: 6,
  },
};

/**
 * Get cluster size configuration based on point count
 */
export const getClusterSize = (count: number) => {
  if (count >= CLUSTER_SIZES.extraLarge.range[0])
    return CLUSTER_SIZES.extraLarge;
  if (count >= CLUSTER_SIZES.large.range[0]) return CLUSTER_SIZES.large;
  if (count >= CLUSTER_SIZES.medium.range[0]) return CLUSTER_SIZES.medium;
  return CLUSTER_SIZES.small;
};

/**
 * Get cluster color based on point count
 */
export const getClusterColor = (count: number) => {
  if (count >= CLUSTER_SIZES.extraLarge.range[0])
    return CLUSTER_COLORS.extraLarge;
  if (count >= CLUSTER_SIZES.large.range[0]) return CLUSTER_COLORS.large;
  if (count >= CLUSTER_SIZES.medium.range[0]) return CLUSTER_COLORS.medium;
  return CLUSTER_COLORS.small;
};

/**
 * Validate if coordinates are valid for clustering
 */
export const isValidCoordinate = (lat: number, lng: number): boolean => {
  return (
    typeof lat === 'number' &&
    typeof lng === 'number' &&
    !isNaN(lat) &&
    !isNaN(lng) &&
    lat !== 0 &&
    lng !== 0 &&
    lat >= -90 &&
    lat <= 90 &&
    lng >= -180 &&
    lng <= 180
  );
};

/**
 * Extract coordinates from a cluster point
 */
export const extractCoordinates = (
  point: any,
): { lat: number; lng: number } | null => {
  try {
    const coords = point.geometry?.coordinates;
    if (!coords || !Array.isArray(coords) || coords.length < 2) {
      return null;
    }

    const lng = coords[0];
    const lat = coords[1];

    if (!isValidCoordinate(lat, lng)) {
      return null;
    }

    return { lat, lng };
  } catch (error) {
    console.warn('Error extracting coordinates:', error);
    return null;
  }
};

/**
 * Get images from cluster properties for preview
 */
export const getClusterImages = (
  point: any,
  maxImages: number = 4,
): string[] => {
  try {
    const images = point.properties?.images;
    if (!images || !Array.isArray(images)) {
      return [];
    }

    return images
      .filter((img: any) => typeof img === 'string' && img.length > 0)
      .slice(0, maxImages);
  } catch (error) {
    console.warn('Error extracting cluster images:', error);
    return [];
  }
};

/**
 * Performance monitoring for clustering operations
 */
export const measureClusteringPerformance = (
  operation: () => any,
  operationName: string,
) => {
  const startTime = performance.now();
  const result = operation();
  const endTime = performance.now();
  const duration = endTime - startTime;

  if (duration > 100) {
    // Log slow operations
    console.warn(
      `Slow clustering operation: ${operationName} took ${duration.toFixed(2)}ms`,
    );
  }

  return result;
};

/**
 * Throttle function for performance optimization
 */
export const throttle = <T extends (...args: any[]) => any>(
  func: T,
  delay: number,
): ((...args: Parameters<T>) => void) => {
  let timeoutId: NodeJS.Timeout | null = null;
  let lastExecTime = 0;

  return (...args: Parameters<T>) => {
    const currentTime = Date.now();

    if (currentTime - lastExecTime > delay) {
      func(...args);
      lastExecTime = currentTime;
    } else {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      timeoutId = setTimeout(
        () => {
          func(...args);
          lastExecTime = Date.now();
        },
        delay - (currentTime - lastExecTime),
      );
    }
  };
};

export const CLUSTER_COLORS = {
  small: '#4CAF50', // Green
  medium: '#FF9800', // Orange
  large: '#F44336', // Red
  extraLarge: '#9C27B0', // Purple
};
