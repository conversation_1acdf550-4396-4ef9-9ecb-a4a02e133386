import { useState } from 'react';
import { z } from 'zod';

// Validation schema
const LoginSchema = z.object({
  email: z.string().email({ message: 'Enter a valid email address' }),
  password: z
    .string()
    .min(8, { message: 'Password must be at least 8 characters' })
    .nonempty({ message: 'Password is required' }),
});

const useLoginValidation = () => {
  const [errors, setErrors] = useState<{ [key: string]: string | undefined }>(
    {},
  );

  const validate = (form: { email: string; password: string }): boolean => {
    try {
      // Validate the form against the schema
      LoginSchema.parse(form);

      // If no errors, clear the error state
      setErrors({});
      return true;
    } catch (error) {
      if (error instanceof z.ZodError) {
        // Map Zod errors to the form errors state
        const formErrors = error.errors.reduce(
          (acc, err) => {
            const key = err.path[0] as string;
            acc[key] = err.message;
            return acc;
          },
          {} as { [key: string]: string },
        );

        setErrors(formErrors);
      }
      return false;
    }
  };

  return { errors, validate };
};

export default useLoginValidation;
