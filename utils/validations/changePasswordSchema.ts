import { z } from 'zod';

export const changePasswordSchema = z
  .object({
    oldPassword: z.string({ message: 'Current password is required' }),
    newPassword: z
      .string({ message: 'Must enter a valid password' })
      .min(8, { message: 'Must be at least 8 characters long' })
      .regex(/[a-z]/, { message: 'Must include at least one lowercase letter' })
      .regex(/[A-Z]/, { message: 'Must include at least one uppercase letter' })
      .regex(/\d/, { message: 'Must include at least one number' })
      .regex(/[\W_]/, {
        message: 'Must include at least one special character',
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    path: ['confirmPassword'],
    message: 'Passwords must match',
  });

export const validatePassword = (password: string) => ({
  atLeast8Characters: {
    message: 'Must be at least 8 characters long',
    isError: password.length < 8,
  },
  lowerCase: {
    message: 'Must include at least one lowercase letter',
    isError: !/[a-z]/.test(password),
  },
  upperCase: {
    message: 'Must include at least one uppercase letter',
    isError: !/[A-Z]/.test(password),
  },
  includeNumber: {
    message: 'Must include at least one number',
    isError: !/\d/.test(password),
  },
  special: {
    message: 'Must include at least one special character',
    isError: !/[\W_]/.test(password),
  },
});
