import { z } from 'zod';
export const passwordValidation = z
  .string()
  .min(8, { message: 'Must be at least 8 characters long' })
  .regex(/[a-z]/, { message: 'Must include at least one lowercase letter' })
  .regex(/[A-Z]/, { message: 'Must include at least one uppercase letter' })
  .regex(/[0-9]/, { message: 'Must include at least one number' })
  .regex(/[\W_]/, { message: 'Must include at least one special character' });

// Schema for registration form validation
export const registerSchema = z
  .object({
    email: z.string().email({ message: 'Enter a valid email address' }),
    password: passwordValidation,
    confirmPassword: z.string(),
    username: z
      .string()
      .max(20, { message: 'Username must be 20 characters or less' }),
  })
  .superRefine((data, ctx) => {
    if (data.password !== data.confirmPassword) {
      ctx.addIssue({
        path: ['confirmPassword'],
        message: 'Passwords don’t match.',
        code: z.ZodIssueCode.custom,
      });
    }
  });
