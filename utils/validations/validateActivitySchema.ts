import { RESTRICTIONS_ENUM } from '@/types';
import { z } from 'zod';

export const MAX_DESCRIPTION_LETTERS = 275;

const ICategorySchema = z.object({
  _id: z.string(),
  english: z.string(),
  arabic: z.string(),
  icon: z.string().optional(),
});

const IUserInfoSchema = z.object({
  _id: z.string(),
  name: z.string(),
  image: z.string(),
});

const ICircleSchema = z.object({
  _id: z.string(),
  image: z.string(),
  name: z.string(),
  description: z.string(),
  userId: z.string(),
  members: z.array(IUserInfoSchema),
});

const IImagesSchema = z.object({
  uri: z.string(),
  width: z.number(),
  height: z.number(),
  type: z.string(),
  fileName: z.string().optional(),
  duration: z.number().optional(),
  fileSize: z.number().optional(),
  mimeType: z.string().optional(),
});

const validateCreateActivitySchema = z
  .object({
    activityName: z.string().nonempty('Activity name is required'),
    restriction: z.nativeEnum(RESTRICTIONS_ENUM),
    category: z.array(ICategorySchema).min(1, { message: 'At least one category must be selected' }),
    socialCircles: z.array(ICircleSchema).optional(),
    images: z.array(IImagesSchema),
    isPaid: z.boolean(),
    description: z
      .string()
      .max(
        MAX_DESCRIPTION_LETTERS,
        `Description can't exceed ${MAX_DESCRIPTION_LETTERS} characters`,
      ),
    start: z.object({
      date: z.date(),
      time: z.date(),
    }),
    end: z.object({
      date: z.date(),
      time: z.date(),
    }),
    virtual: z.boolean().optional(),
    link: z.string().optional(),
    price: z.string().optional(),
    maxParticipants: z.string().optional(),
    permissionToJoin: z.boolean().optional(),
    location: z
      .object({
        longitude: z.number().optional(),
        latitude: z.number().optional(),
        address: z.string().optional(),
      })
      .optional(),
  })
  .refine(
    (data) => {
      const startDateTime = new Date(
        data.start.date.toDateString() + ' ' + data.start.time.toTimeString(),
      );
      const endDateTime = new Date(
        data.end.date.toDateString() + ' ' + data.end.time.toTimeString(),
      );
      return startDateTime <= endDateTime;
    },
    {
      message: 'Start date & time must be before or equal to end date & time',
      path: ['start'],
    },
  )
  .refine(
    (data) =>
      !(
        data.isPaid &&
        (!data.price || isNaN(Number(data.price)) || Number(data.price) <= 0)
      ),
    {
      message: 'Paid activity must have a valid price greater than 0',
      path: ['price'],
    },
  )
  .refine(
    (data) => !(data.virtual && (!data.link || data.link.trim().length === 0)),
    {
      message: 'Virtual event must have a link',
      path: ['link'],
    },
  );

type IForm = z.infer<typeof validateCreateActivitySchema>;

export { validateCreateActivitySchema, IForm };
