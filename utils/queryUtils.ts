/**
 * Utility functions and constants for React Query
 * Provides consistent caching strategies across the app
 */

// Cache durations based on data type
export const CACHE_TIME = {
  // For frequently changing data (e.g., messages, notifications)
  FREQUENT: 30 * 1000, // 30 seconds

  // For moderately changing data (e.g., posts, comments)
  MODERATE: 2 * 60 * 1000, // 2 minutes

  // For rarely changing data (e.g., user profiles, categories)
  RARE: 10 * 60 * 1000, // 10 minutes

  // For static data (e.g., app configuration)
  STATIC: Infinity, // Never stale
};

// GC (Garbage Collection) times - how long to keep data in cache when not in use
export const GC_TIME = {
  FREQUENT: 5 * 60 * 1000, // 5 minutes
  MODERATE: 10 * 60 * 1000, // 10 minutes
  RARE: 30 * 60 * 1000, // 30 minutes
  STATIC: Infinity, // Keep forever
};

// Performance monitoring for queries
export const logQueryPerformance = (queryKey: unknown[], startTime: number, endTime: number) => {
  const duration = endTime - startTime;
  if (duration > 1000) { // Log slow queries (>1s)
    console.warn(`Slow query detected: ${JSON.stringify(queryKey)} took ${duration}ms`);
  }
};

// Standard query keys to ensure consistency
export const QUERY_KEYS = {
  USERS: (userId?: string) => ['users', userId],
  USER_PROFILE: (userId?: string) => ['users', userId, 'profile'],
  USER_POSTS: (userId?: string) => ['users', userId, 'posts'],
  USER_MEDIA: (userId?: string) => ['users', userId, 'media'],
  USER_LIKES: (userId?: string) => ['users', userId, 'likes'],
  USER_FOLLOWERS: (userId?: string) => ['users', userId, 'followers'],
  USER_FOLLOWING: (userId?: string) => ['users', userId, 'following'],

  POSTS: (postId?: string) => ['posts', postId],
  POST_COMMENTS: (postId?: string) => ['posts', postId, 'comments'],
  POST_LIKES: (postId?: string) => ['posts', postId, 'likes'],

  ACTIVITIES: (activityId?: string) => ['activities', activityId],
  ACTIVITY_PARTICIPANTS: (activityId?: string) => ['activities', activityId, 'participants'],

  EXPLORES: (exploreId?: string) => ['explores', exploreId],
  EXPLORE_REVIEWS: (exploreId?: string) => ['explores', exploreId, 'reviews'],

  CATEGORIES: () => ['categories'],
  FEED: (page?: number) => ['feed', page],
  SHORTS: (shortId?: string) => ['shorts', shortId],
  SOCIAL_CIRCLES: (circleId?: string) => ['socialCircles', circleId],
  NEAR_YOU: (params?: { latitude?: number; longitude?: number }) =>
    ['nearYou', params?.latitude, params?.longitude],
};

/**
 * Updates a specific item in a list without refetching the entire list
 *
 * @param queryClient - The React Query client
 * @param queryKey - The query key for the list
 * @param itemId - The ID of the item to update
 * @param updateFn - Function that receives the old item and returns the updated item
 */
export const updateItemInList = (
  queryClient: any,
  queryKey: unknown[],
  itemId: string,
  updateFn: (oldItem: any) => any
) => {
  queryClient.setQueryData(queryKey, (oldData: any) => {
    if (!oldData || !oldData.data) return oldData;

    // Handle different data structures
    if (Array.isArray(oldData.data)) {
      return {
        ...oldData,
        data: oldData.data.map((item: any) =>
          item._id === itemId ? updateFn(item) : item
        ),
      };
    }

    // Handle paginated data
    if (oldData.pages) {
      return {
        ...oldData,
        pages: oldData.pages.map((page: any) => ({
          ...page,
          data: Array.isArray(page.data)
            ? page.data.map((item: any) =>
                item._id === itemId ? updateFn(item) : item
              )
            : page.data,
        })),
      };
    }

    return oldData;
  });
};

/**
 * Prefetches data that will likely be needed soon
 *
 * @param queryClient - The React Query client
 * @param queryKey - The query key for the data to prefetch
 * @param queryFn - The function to fetch the data
 * @param staleTime - How long the data should be considered fresh
 */
export const prefetchData = (
  queryClient: any,
  queryKey: unknown[],
  queryFn: () => Promise<any>,
  staleTime = CACHE_TIME.MODERATE
) => {
  queryClient.prefetchQuery({
    queryKey,
    queryFn,
    staleTime,
  });
};
