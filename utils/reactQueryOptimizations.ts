import { QueryClient } from '@tanstack/react-query';
import { Platform } from 'react-native';

/**
 * Optimized React Query configuration for better tab navigation performance
 * Especially tuned for Android performance improvements
 */
export const createOptimizedQueryClient = () => {
  return new QueryClient({
    defaultOptions: {
      queries: {
        // Aggressive caching for better tab switching
        staleTime: Platform.OS === 'android' ? 10 * 60 * 1000 : 5 * 60 * 1000, // 10min on Android, 5min on iOS
        gcTime: Platform.OS === 'android' ? 20 * 60 * 1000 : 10 * 60 * 1000, // 20min on Android, 10min on iOS
        
        // Reduce network requests during tab switches
        refetchOnWindowFocus: false,
        refetchOnReconnect: true,
        refetchOnMount: false, // Don't refetch on mount if data exists
        
        // Retry configuration
        retry: (failureCount, error: any) => {
          if (error?.response?.status >= 400 && error?.response?.status < 500) {
            return false;
          }
          return failureCount < 2; // Reduced retries for faster failures
        },
        retryDelay: (attemptIndex) => Math.min(500 * 2 ** attemptIndex, 5000), // Faster retry delays
        
        // Network mode for better offline handling
        networkMode: 'online',
        
        // Background refetching optimization
        refetchInterval: false, // Disable automatic refetching
        refetchIntervalInBackground: false,
      },
      mutations: {
        retry: 1,
        networkMode: 'online',
        // Faster mutation timeouts
        mutationCache: {
          onError: (error) => {
            console.warn('Mutation error:', error);
          },
        },
      },
    },
  });
};

/**
 * Query keys for consistent caching across the app
 */
export const OPTIMIZED_QUERY_KEYS = {
  // User-related queries
  USER_PROFILE: (userId: string) => ['user', 'profile', userId],
  USER_POSTS: (userId: string) => ['user', 'posts', userId],
  USER_MEDIA: (userId: string) => ['user', 'media', userId],
  USER_LIKES: (userId: string) => ['user', 'likes', userId],
  
  // Activity-related queries
  JOINED_ACTIVITIES: () => ['activities', 'joined'],
  CREATED_ACTIVITIES: () => ['activities', 'created'],
  ACTIVITY_DETAIL: (activityId: string) => ['activity', activityId],
  
  // Messages-related queries
  ALL_CHATS: () => ['chats', 'all'],
  CHAT_MESSAGES: (chatId: string) => ['chat', 'messages', chatId],
  
  // Social circles
  SOCIAL_CIRCLES: (userId: string) => ['socialCircles', userId],
  FOLLOWERS_POSTS: () => ['posts', 'followers'],
} as const;

/**
 * Cache time constants optimized for different data types
 */
export const CACHE_TIMES = {
  // User data changes rarely
  USER_PROFILE: 15 * 60 * 1000, // 15 minutes
  
  // Posts and media change more frequently
  POSTS: 5 * 60 * 1000, // 5 minutes
  MEDIA: 5 * 60 * 1000, // 5 minutes
  
  // Activities change less frequently
  ACTIVITIES: 10 * 60 * 1000, // 10 minutes
  
  // Messages need to be fresh
  MESSAGES: 2 * 60 * 1000, // 2 minutes
  
  // Social circles change rarely
  SOCIAL_CIRCLES: 10 * 60 * 1000, // 10 minutes
} as const;

/**
 * Prefetch strategies for better tab navigation performance
 */
export const prefetchStrategies = {
  /**
   * Prefetch user profile data when navigating to profile tab
   */
  prefetchUserProfile: (queryClient: QueryClient, userId: string) => {
    queryClient.prefetchQuery({
      queryKey: OPTIMIZED_QUERY_KEYS.USER_PROFILE(userId),
      staleTime: CACHE_TIMES.USER_PROFILE,
    });
  },

  /**
   * Prefetch activities when navigating to activities tab
   */
  prefetchActivities: (queryClient: QueryClient) => {
    queryClient.prefetchQuery({
      queryKey: OPTIMIZED_QUERY_KEYS.JOINED_ACTIVITIES(),
      staleTime: CACHE_TIMES.ACTIVITIES,
    });
  },

  /**
   * Prefetch messages when navigating to messages tab
   */
  prefetchMessages: (queryClient: QueryClient) => {
    queryClient.prefetchQuery({
      queryKey: OPTIMIZED_QUERY_KEYS.ALL_CHATS(),
      staleTime: CACHE_TIMES.MESSAGES,
    });
  },

  /**
   * Prefetch social circles data
   */
  prefetchSocialCircles: (queryClient: QueryClient, userId: string) => {
    queryClient.prefetchQuery({
      queryKey: OPTIMIZED_QUERY_KEYS.SOCIAL_CIRCLES(userId),
      staleTime: CACHE_TIMES.SOCIAL_CIRCLES,
    });
  },
};

/**
 * Performance monitoring for React Query
 */
export const queryPerformanceMonitor = {
  onQueryStart: (queryKey: string) => {
    if (__DEV__) {
      console.log(`🔄 Query started: ${queryKey}`);
    }
  },

  onQuerySuccess: (queryKey: string, duration: number) => {
    if (__DEV__ && duration > 1000) {
      console.warn(`🐌 Slow query: ${queryKey} took ${duration}ms`);
    }
  },

  onQueryError: (queryKey: string, error: any) => {
    if (__DEV__) {
      console.error(`❌ Query failed: ${queryKey}`, error);
    }
  },
};

/**
 * Android-specific optimizations for React Query
 */
export const androidQueryOptimizations = Platform.OS === 'android' ? {
  // More aggressive caching on Android
  defaultStaleTime: 10 * 60 * 1000, // 10 minutes
  defaultGcTime: 20 * 60 * 1000, // 20 minutes
  
  // Reduce background activity
  refetchOnWindowFocus: false,
  refetchOnMount: false,
  
  // Faster timeouts
  queryTimeout: 5000, // 5 seconds
  mutationTimeout: 10000, // 10 seconds
} : {};

/**
 * Utility to invalidate queries efficiently
 */
export const invalidateQueries = {
  userProfile: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: OPTIMIZED_QUERY_KEYS.USER_PROFILE(userId),
    });
  },

  userPosts: (queryClient: QueryClient, userId: string) => {
    queryClient.invalidateQueries({
      queryKey: OPTIMIZED_QUERY_KEYS.USER_POSTS(userId),
    });
  },

  activities: (queryClient: QueryClient) => {
    queryClient.invalidateQueries({
      queryKey: OPTIMIZED_QUERY_KEYS.JOINED_ACTIVITIES(),
    });
    queryClient.invalidateQueries({
      queryKey: OPTIMIZED_QUERY_KEYS.CREATED_ACTIVITIES(),
    });
  },

  messages: (queryClient: QueryClient) => {
    queryClient.invalidateQueries({
      queryKey: OPTIMIZED_QUERY_KEYS.ALL_CHATS(),
    });
  },
};
