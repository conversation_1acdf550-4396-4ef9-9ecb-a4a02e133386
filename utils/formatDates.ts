import dayjs from 'dayjs';

export const getMonthFromDate = (date: Date) => {
  return dayjs(date).format('MMM');
};

export const getDayFromDate = (date: Date) => {
  return dayjs(date).format('ddd');
};

export const getDayNumber = (date: Date) => {
  return dayjs(date).format('D'); // Returns the day without leading zero
};

export const formatDateDayMonthWeek = (date?: Date | string) => {
  if (!date) return '';

  // Parse date string into Date object if it's a string
  const parsedDate = date instanceof Date ? date : new Date(date);

  // Check if the parsedDate is valid
  if (isNaN(parsedDate.getTime())) {
    return '';
  }

  return parsedDate.toLocaleDateString('en-US', {
    weekday: 'short', // "Fri"
    month: 'short', // "Jan"
    day: '2-digit', // "19"
  });
};

export const formatTimeRange = (
  start: Date | undefined,
  end: Date | undefined,
) => {
  const formatOptions: Intl.DateTimeFormatOptions = {
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
  };
  if (!start || !end) return '';

  const startTime = new Date(start)
    .toLocaleTimeString('en-US', formatOptions)
    .replace(/^0/, ''); // Remove leading zero
  const endTime = new Date(end)
    .toLocaleTimeString('en-US', formatOptions)
    .replace(/^0/, ''); // Remove leading zero

  return `${startTime} : ${endTime}`;
};

// date of birth to date
export const convertDateToLocaleStringToNormalDate = (dateString: string) => {
  const [month, day, year] = dateString.split('/'); // Split the string into parts
  // Add leading zeros if month or day is a single digit
  const formattedDateString = `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  const dateObject = new Date(formattedDateString); // Create a Date object in YYYY-MM-DD format
  return dateObject;
};

/**
 * Converts a date string in "DD/MM/YYYY" format to a UTC date string.
 * @param {string} dateString - The date string in "DD/MM/YYYY" format.
 * @returns {string} - The UTC date string in ISO format (e.g., "YYYY-MM-DDTHH:mm:ss.sssZ").
 */
export function convertToUTCDate(dateString: string) {
  if (!dateString || typeof dateString !== 'string') {
    throw new Error("Invalid date string. Expected format: 'DD/MM/YYYY'.");
  }

  // Split the date string into day, month, and year
  const [day, month, year] = dateString.split('/');

  // Validate the date components
  if (
    !day ||
    !month ||
    !year ||
    isNaN(Number(day)) ||
    isNaN(Number(month)) ||
    isNaN(Number(year))
  ) {
    throw new Error("Invalid date string. Expected format: 'DD/MM/YYYY'.");
  }

  // Create a Date object in UTC (Note: Months are 0-indexed in JavaScript)
  const date = new Date(Date.UTC(Number(year), Number(month) - 1, Number(day)));

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    throw new Error('Invalid date.');
  }

  // Convert to UTC string in ISO format
  return date.toISOString();
}

export const timeAgo = (dateString: Date): string => {
  const date = new Date(dateString);
  const now = new Date();
  const seconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  const intervals: { [key: string]: number } = {
    year: 31536000,
    month: 2592000,
    week: 604800,
    day: 86400,
    hour: 3600,
    minute: 60,
  };

  for (const key in intervals) {
    const interval = Math.floor(seconds / intervals[key]);
    if (interval > 0) {
      return `${interval} ${key}${interval !== 1 ? 's' : ''} ago`;
    }
  }

  return 'just now';
};
