import { APP_Icons } from '../constants/Images';

export function generateUniqueCode(length = 5) {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';

  for (let i = 0; i < length; i++) {
    const randomIndex = Math.floor(Math.random() * characters.length);
    result += characters.charAt(randomIndex);
  }
  // Example usage:
  // Output: A random 5-character code (e.g., "aB2c7")
  return result;
}

export const toggleState = (
  setState: React.Dispatch<React.SetStateAction<boolean>>,
) => {
  setState((prev) => !prev);
};

export const formattedCurrency = (currency: string, fill?: string) => {
  switch (currency) {
    case 'sar':
      return <APP_Icons.SARIcon width={20}
height={50}
fill={fill} />;
    case 'usd':
      return '$';
    default:
      return currency?.toUpperCase();
  }
};
