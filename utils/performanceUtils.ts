/**
 * Performance utilities for monitoring and optimizing app performance
 */

interface PerformanceEntry {
  name: string;
  startTime: number;
  duration: number;
  type: 'render' | 'api' | 'navigation' | 'interaction';
}

class PerformanceMonitor {
  private entries: PerformanceEntry[] = [];
  private maxEntries = 100; // Keep only last 100 entries

  // Log performance entry
  logEntry(entry: PerformanceEntry) {
    this.entries.push(entry);

    // Keep only recent entries
    if (this.entries.length > this.maxEntries) {
      this.entries = this.entries.slice(-this.maxEntries);
    }

    // Log slow operations
    if (entry.duration > this.getThreshold(entry.type)) {
      console.warn(
        `Slow ${entry.type}: ${entry.name} took ${entry.duration.toFixed(2)}ms`,
      );
    }
  }

  // Get performance threshold for different operation types
  private getThreshold(type: string): number {
    switch (type) {
      case 'render':
        return 16; // 60fps = 16.67ms per frame
      case 'api':
        return 1000; // 1 second for API calls
      case 'navigation':
        return 300; // 300ms for navigation
      case 'interaction':
        return 100; // 100ms for user interactions
      default:
        return 100;
    }
  }

  // Get performance summary
  getSummary() {
    const summary = {
      totalEntries: this.entries.length,
      averageDuration: 0,
      slowOperations: 0,
      byType: {} as Record<string, { count: number; avgDuration: number }>,
    };

    if (this.entries.length === 0) return summary;

    // Calculate averages and slow operations
    let totalDuration = 0;
    const typeStats: Record<string, { total: number; count: number }> = {};

    this.entries.forEach((entry) => {
      totalDuration += entry.duration;

      if (entry.duration > this.getThreshold(entry.type)) {
        summary.slowOperations++;
      }

      if (!typeStats[entry.type]) {
        typeStats[entry.type] = { total: 0, count: 0 };
      }
      typeStats[entry.type].total += entry.duration;
      typeStats[entry.type].count++;
    });

    summary.averageDuration = totalDuration / this.entries.length;

    // Calculate by-type statistics
    Object.keys(typeStats).forEach((type) => {
      summary.byType[type] = {
        count: typeStats[type].count,
        avgDuration: typeStats[type].total / typeStats[type].count,
      };
    });

    return summary;
  }

  // Clear all entries
  clear() {
    this.entries = [];
  }

  // Get recent slow operations
  getSlowOperations(limit = 10) {
    return this.entries
      .filter((entry) => entry.duration > this.getThreshold(entry.type))
      .sort((a, b) => b.duration - a.duration)
      .slice(0, limit);
  }
}

// Global performance monitor instance
export const performanceMonitor = new PerformanceMonitor();

// Utility functions for measuring performance
export const measurePerformance = {
  // Measure synchronous operations
  sync: <T>(
    operation: () => T,
    name: string,
    type: PerformanceEntry['type'] = 'interaction',
  ): T => {
    const startTime = performance.now();
    const result = operation();
    const duration = performance.now() - startTime;

    performanceMonitor.logEntry({
      name,
      startTime,
      duration,
      type,
    });

    return result;
  },

  // Measure asynchronous operations
  async: async <T>(
    operation: () => Promise<T>,
    name: string,
    type: PerformanceEntry['type'] = 'api',
  ): Promise<T> => {
    const startTime = performance.now();
    const result = await operation();
    const duration = performance.now() - startTime;

    performanceMonitor.logEntry({
      name,
      startTime,
      duration,
      type,
    });

    return result;
  },

  // Start a manual measurement
  start: (name: string) => {
    const startTime = performance.now();
    return {
      end: (type: PerformanceEntry['type'] = 'interaction') => {
        const duration = performance.now() - startTime;
        performanceMonitor.logEntry({
          name,
          startTime,
          duration,
          type,
        });
        return duration;
      },
    };
  },
};

// React Query performance wrapper
export const withPerformanceTracking = (
  queryFn: () => Promise<any>,
  queryKey: string,
) => {
  return () => measurePerformance.async(queryFn, `Query: ${queryKey}`, 'api');
};

// Navigation performance tracking
export const trackNavigation = (screenName: string) => {
  const measurement = measurePerformance.start(`Navigation to ${screenName}`);
  return () => measurement.end('navigation');
};

// Memory usage tracking (basic implementation)
export const trackMemoryUsage = () => {
  // Note: React Native doesn't have direct memory API
  // This would need native module implementation for accurate memory tracking
  if (__DEV__) {
    console.log('Memory tracking would be implemented with native modules');
  }
};

// Bundle size analysis helper
export const analyzeBundleSize = () => {
  if (__DEV__) {
    console.log('Bundle size analysis:');
    console.log('- Use Metro bundle analyzer for detailed analysis');
    console.log('- Check for large dependencies');
    console.log('- Implement code splitting where possible');
  }
};

// Performance report generator
export const generatePerformanceReport = () => {
  const summary = performanceMonitor.getSummary();
  const slowOps = performanceMonitor.getSlowOperations();

  console.group('📊 Performance Report');
  console.log('Summary:', summary);
  console.log('Slow Operations:', slowOps);
  console.groupEnd();

  return { summary, slowOps };
};
