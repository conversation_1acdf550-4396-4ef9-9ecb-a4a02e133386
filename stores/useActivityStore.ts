import { create } from 'zustand';
import { IActivityItem, ILocation, RESTRICTIONS_ENUM } from '@/types';
import { IForm } from '@/utils/validations/validateActivitySchema';

interface IActivityStore {
  joinActivity: {
    restrictions: RESTRICTIONS_ENUM;
    _id: string;
    requiresAdminAccept: boolean;
  } | null;

  createActivityLocation: ILocation | null;

  sharedActivityParticipantsData: {
    image: string;
    title: string;
    createdAt: string;
    participants: any[];
  } | null;

  activityForm: Partial<IForm> | null;

  setJoinActivity: (joinActivity: IActivityStore['joinActivity']) => void;
  setCreateActivityLocation: (location: ILocation) => void;
  setSharedActivityParticipantsData: (
    data: IActivityStore['sharedActivityParticipantsData'],
  ) => void;
  setActivityForm: (form: Partial<IForm> | null) => void; // accept Partial
  selectedActivity: IActivityItem | null;
  setSelectedActivity: (activity: IActivityItem) => void;
}

export const useActivityStore = create<IActivityStore>((set) => ({
  joinActivity: null,
  createActivityLocation: null,
  sharedActivityParticipantsData: null,
  activityForm: null,
  selectedActivity: null,
  setSelectedActivity: (activity) => set({ selectedActivity: activity }),
  setJoinActivity: (joinActivity) => set({ joinActivity }),
  setCreateActivityLocation: (location) =>
    set({ createActivityLocation: location }),
  setSharedActivityParticipantsData: (data) =>
    set({ sharedActivityParticipantsData: data }),

  setActivityForm: (form) =>
    set((state) => ({
      activityForm: form ? { ...(state.activityForm || {}), ...form } : null,
    })),
}));
