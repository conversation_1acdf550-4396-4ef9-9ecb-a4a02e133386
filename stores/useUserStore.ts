import { create } from 'zustand';
import { IUserInfo } from '@/types';

interface IBlockedUser extends IUserInfo {
  blockedAt?: Date;
}

interface IUserStore {
  blockedUsers: IBlockedUser[];
  setBlockedUsers: (users: IBlockedUser[]) => void;
  addBlockedUser: (user: IBlockedUser) => void;
  removeBlockedUser: (userId: string) => void;
  isUserBlocked: (userId: string) => boolean;
  clearBlockedUsers: () => void;
}

export const useUserStore = create<IUserStore>((set, get) => ({
  blockedUsers: [],
  
  setBlockedUsers: (users) => set({ blockedUsers: users }),
  
  addBlockedUser: (user) => set((state) => ({
    blockedUsers: [...state.blockedUsers.filter(u => u._id !== user._id), user]
  })),
  
  removeBlockedUser: (userId) => set((state) => ({
    blockedUsers: state.blockedUsers.filter(user => user._id !== userId)
  })),
  
  isUserBlocked: (userId) => {
    const { blockedUsers } = get();
    return blockedUsers.some(user => user._id === userId);
  },
  
  clearBlockedUsers: () => set({ blockedUsers: [] }),
}));
