import { Dimensions, PixelRatio } from 'react-native';

export const { width: SCREEN_WIDTH, height: SCREEN_HEIGHT } =
  Dimensions.get('window');
const MAX_HEIGHT = SCREEN_HEIGHT - 150; // Fixed max height
const scale = SCREEN_HEIGHT / 680;
const normalized = (dp: number) => PixelRatio.getFontScale() * dp * scale;

const COLORS = {
  primary: {
    50: '#7a26b1',
    200: '#391162',
  },
  secondary: {
    300: '#c96277',
    400: '#c96277',
    background: '#170e2e',
  },
  accent: {
    100: '#febcbf',
    200: '#fc7980',
    300: '#fb3640',
    400: '#e70510',
    500: '#a2030b',
  },

  neutral: {
    100: '#e4e4e4',
    200: '#d2d2d2',
    300: '#bfbfbf',
    400: '#acacac',
    500: '#9a9a9a',
    600: '#878787',
    700: '#757575',
    800: '#626262',
    900: '#4f4f4f',
    1000: '#3d3d3d',
  },

  error: {
    100: '#f7b0aa',
    200: '#EE6055',
    300: '#E92D1F',
    400: '#B91F13',
    500: '#83160D',
  },

  warning: {
    100: '#FFE9AA',
    200: '#FFD355',
    300: '#FFBD00',
    400: '#C69200',
    500: '#8C6800',
  },

  success: {
    100: '#C3E6C1',
    200: '#86CD82',
    300: '#5BBB56',
    400: '#41973C',
    500: '#2E6B2B',
  },
  starColor: '#FBBC05',
  tertiaryDisabled: '#E2ADB8',
  primaryDisabled: '#E2ADB8',
  white: {
    50: '#fff',
    400: '#B9C5D4',
  },
  text_colors: {
    wite: '#FFFFFF',
    primary: '#C96277',
    secondary: '#ACACAC',
  },
  danger: '#FB3640',
  gray_colors: {
    100: '#E4E4E4',
    200: '#B3B3B3',
  },
  black: '#0A0614',
};

const gradients = {
  primary: ['#170E2E', '#7A26B1'] as readonly [string, string, ...string[]],
  border: ['#7A26B1', '#C96277'] as readonly [string, string, ...string[]],
};

const FONT = {
  fontFamily: 'poppins',
  light: 'poppins-light',
  regular: 'poppins-regular',
  medium: 'poppins-medium',
  semiBold: 'poppins-semibold',
  bold: 'poppins-bold',
};

const SIZES = {
  xxSmall: normalized(8),
  xSmall: normalized(10),
  small: normalized(12),
  regular: normalized(14),
  medium: normalized(16),
  large: normalized(18),
  xLarge: normalized(20),
  xxLarge: normalized(32),
};

const SHADOWS = {
  small: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 2,
  },
  medium: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 5.84,
    elevation: 5,
  },
};

const BUTTONS = {
  primary: {
    backgroundColor: COLORS.secondary[300],
    borderRadius: normalized(20),
    fontFamily: FONT.regular,
    paddingVertical: normalized(5),
  },
  whiteTransparent: {
    backgroundColor: 'rgba(255 255 255 , 0.1)',
    borderColor: 'rgba(255 255 255 , 0.2)',
    borderWidth: 1,
  },
};
export {
  COLORS,
  SIZES,
  SHADOWS,
  BUTTONS,
  gradients,
  normalized,
  FONT,
  MAX_HEIGHT,
};
