# Welcome to your Expo app 👋

This is an [Expo](https://expo.dev) project created with [`create-expo-app`](https://www.npmjs.com/package/create-expo-app).

## Get started

1. Install dependencies

   ```bash
   npm install
   ```

2. Start the app

   ```bash
    npx expo start
   ```

In the output, you'll find options to open the app in a

- [development build](https://docs.expo.dev/develop/development-builds/introduction/)
- [Android emulator](https://docs.expo.dev/workflow/android-studio-emulator/)
- [iOS simulator](https://docs.expo.dev/workflow/ios-simulator/)
- [Expo Go](https://expo.dev/go), a limited sandbox for trying out app development with Expo

You can start developing by editing the files inside the **app** directory. This project uses [file-based routing](https://docs.expo.dev/router/introduction).

## Note

- tailwind css is used to style application
- in theme app i use same as tailwind config to use colors gradient and normalized to render font and sizes based on pixel density and screen ratio for responsive design if component not compatible with tailwind css (Native wind )
- each component is pure no component carry and state

#### Note if you passed function to pure component must wrap it in call back to avoid un-necessary re renders

- screen carries all the state and logic it's children
