/android
/ios
shareikna-expo-94da8-firebase-adminsdk-fbsvc-dcf33ccc0c.json
android/keystores/release.keystore
google-services.json
google-services-preview.json
google-services.dev.json
ios/certs/*
.env.development
.env.preview
credentials.json
.expo
.expo/*
# OSX
#
.DS_Store
.env 
.env.local
.env.production
**/.xcode.env.local
**/Pods/
# Yarn
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/sdks
!.yarn/versions

# Xcode
#
build/
*.pbxuser
!default.pbxuser
*.mode1v3
!default.mode1v3
*.mode2v3
!default.mode2v3
*.perspectivev3
!default.perspectivev3
xcuserdata
*.xccheckout
*.moved-aside
DerivedData
*.hmap
*.ipa
*.xcuserstate
ios/.xcode.env.local
.env
# Android/IntelliJ
#
build/
.idea
.gradle
local.properties
*.iml
*.hprof
.cxx/
*.keystore
!debug.keystore

# node.js
#
node_modules/
npm-debug.log
yarn-error.log

# fastlane
#
# It is recommended to not store the screenshots in the git repo. Instead, use fastlane to re-generate the
# screenshots whenever they are needed.
# For more information about the recommended setup visit:
# https://docs.fastlane.tools/best-practices/source-control/

**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Bundle artifact
*.jsbundle

# Ruby / CocoaPods
/ios/Pods/
/vendor/bundle/

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# testing
/coverage
.yarn
# Expo
.expo
__generated__
web-build
bare-apps

# macOS
.DS_Store

# Node
node_modules
npm-debug.log
yarn-error.log

# Ruby
.direnv

# Env
.envrc.local

# Emacs
*~

# Vim
.*.swp
.*.swo
.*.swn
.*.swm

# VS Code
.vscode/launch.json

# Sublime Text
*.sublime-project
*.sublime-workspace

# Xcode
*.pbxuser
!default.pbxuser
*.xccheckout
*.xcscmblueprint
xcuserdata

# IDEA / Android Studio
*.iml
.gradle
.idea

# Eclipse
.project
.settings

# VSCode
.history/
/vscode/launch.json

# Android
*.apk
*.hprof
ReactAndroid-temp.aar

# Tools
jarjar-rules.txt

# Dynamic Macros
.kernel-ngrok-url

# Template files
/apps/bare-expo/android/app/google-services.json
/apps/bare-expo/ios/BareExpo/GoogleService-Info.plist

# Template projects
templates/**/android/**/generated/*
templates/**/android/app/build
templates/**/Pods/**
templates/**/Podfile.lock
templates/**/yarn.lock

# Codemod
.codemod.bookmark

# Fastlane
/*.cer
/fastlane/report.xml
/fastlane/Preview.html
/fastlane/Deployment
/fastlane/test_output
/Preview.html
/gc_keys.json
/fastlane/gc_keys.json

# CI
/android/logcat.txt

# Shell apps
android-shell-app
shellAppBase-*
shellAppIntermediates
shellAppWorkspaces
/artifacts/*

# Expo Client builds
/client-builds

# Expo web env
.env.local
.env.development.local
.env.test.local
.env.production.local
apps/bare-expo/deploy-url.txt

# Temporary files created by Metro to check the health of the file watcher
.metro-health-check*

# Expo Doc merging
docs/pages/versions/*/react-native/ADDED_*.md
docs/pages/versions/*/react-native/REMOVED_*.md
docs/pages/versions/*/react-native/*.diff

# Expo Go
/apps/expo-go/src/dist

# Prebuilds
/packages/**/*.xcframework
/packages/**/*.spec.json
/packages/**/Info-generated.plist
!crsqlite.xcframework

# iOS
**/ios/.xcode.env.local

android/
ios/
/android
/ios


android/gradle.properties
my-upload-key.keystore
my-debug-key.keystore

# @generated expo-cli sync-8d4afeec25ea8a192358fae2f8e2fc766bdce4ec
# The following patterns were generated by expo-cli

# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# Metro
.metro-health-check*

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# macOS
.DS_Store
*.pem

# local env files
.env*.local

# typescript
*.tsbuildinfo

# @end expo-cli
build-1742692742948.aab

shareikna-expo-94da8-firebase-adminsdk-fbsvc-0de1548960.json
