import { SendAuthOTPAPI } from '@/services/userAPI';
import { useMutation } from '@tanstack/react-query';
import { useCallback } from 'react';
import { customToast } from '../useCustomToast';
import { t } from 'i18next';

const useResendAuthOTP = () => {
  const mutation = useMutation({
    mutationFn: async ({ email }: { email: string }) => {
      return await SendAuthOTPAPI({ email });
    },
    onSuccess: (data) => {
      if (data.success) {
        customToast(t(data.message), 'success');
      }
    },
    onError: (error) => {
      customToast(t(error.message), 'success');
    },
  });

  const handleResendAuthOTP = useCallback(({ email }: { email: string }) => {
    mutation.mutate({ email });
  }, []);

  return {
    loading: mutation.isPending,
    handleResendAuthOTP,

    ...mutation,
  };
};

export default useResendAuthOTP;
