import { useCallback } from 'react';
import { useSession } from '@/context/AuthContext';

import {
  GoogleSignin,
  statusCodes,
} from '@react-native-google-signin/google-signin';

/**
 * Hook for Google login that uses the AuthContext
 * This is a simplified wrapper around the AuthContext's signInWithGoogle method
 */
export const useGoogleLogin = () => {
  const { signInWithGoogle, isGoogleSignInPending } = useSession();

  // Simple wrapper around the context method for backward compatibility
  const googleLogin = useCallback(async () => {
    try {
      await signInWithGoogle();
    } catch (error) {
      console.error('Google login error:', error);
    }
  }, [signInWithGoogle]);

  // Alternative sign-in flow that can be used directly
  const startSignInFlow = async () => {
    try {
      // Check if Google Play Services are available
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });

      // Sign out first to ensure a fresh sign-in attempt
      await GoogleSignin.signOut();

      // Start the Google sign-in flow
      const userInfo = await GoogleSignin.signIn();

      if (userInfo.data?.idToken) {
        // Use the token to authenticate with your backend
        try {
          await signInWithGoogle();
        } catch (error) {
          console.error('Failed to authenticate with backend:', error);
        }
      }

      return userInfo;
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the sign-in flow
        console.log('Sign in cancelled');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log('Sign in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log('Play services not available or outdated');
      } else {
        console.error('Google sign-in error:', error);
      }
      throw error;
    }
  };

  return {
    googleLogin,
    loading: isGoogleSignInPending,
    startSignInFlow,
  };
};
