import { useMutation } from '@tanstack/react-query';
import { useCallback, useState } from 'react';
import { customToast } from '../useCustomToast';
import { t } from 'i18next';
import {
  IValidateAuthOTPPayload,
  ValidateAuthOTPAPI,
} from '@/services/userAPI';

const useValidateOTP = () => {
  const [OTP, setOTP] = useState<string>('');
  const [isValid, setIsValid] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const mutation = useMutation({
    mutationFn: async (payload: IValidateAuthOTPPayload) => {
      return await ValidateAuthOTPAPI(payload);
    },
    onError: (error: { response: { data: { message: string } } }) => {
      customToast(error.response.data.message, 'error');
    },
  });

  const handleOtpChange = useCallback((text: string) => {
    setOTP(text);
    if (text.length === 4) {
      setError(''); // Clear error if OTP is complete
    }
  }, []);

  const handleSubmit = useCallback(
    (payload: IValidateAuthOTPPayload) => {
      if (!OTP || OTP.length < 4) {
        setError(t('Please enter a valid 4-digit OTP'));
        setIsValid(false);
        return; // Early return if OTP is invalid
      }
      setError('');
      setIsValid(true);
      mutation.mutate(payload);
    },
    [OTP, mutation],
  );

  return {
    handleOtpChange,
    ...mutation,
    otpError: error,
    OTP,
    handleSubmit,
    isValid,
    loading: mutation.isPending,
  };
};

export default useValidateOTP;
