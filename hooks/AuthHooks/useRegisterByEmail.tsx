import { useMutation, useQuery } from '@tanstack/react-query';
import { useCallback, useEffect, useState } from 'react';
import { z } from 'zod';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { t } from 'i18next';
import { useRouter } from 'expo-router';
import { setToken } from '@/utils/AuthSecureStore';
import { useSession } from '@/context/AuthContext';

import {
  IRegisterWithEmailPayload,
  RegisterWithEmailAPI,
  SendAuthOTPAPI,
} from '@/services/userAPI';
import useLocation from '../useLocation';
import { customToast } from '../useCustomToast';

// Define the Zod schema for form validation
export const registerSchema = z
  .object({
    email: z
      .string({ message: 'email is required' })
      .email({ message: 'Invalid Email Address' }),
    username: z
      .string({ message: 'username is required' })
      .min(3, { message: 'Username must be at least 3 characters' })
      .max(20, { message: 'Userna<PERSON> cannot exceed 20 characters' }),
    password: z
      .string({ message: 'Must enter a valid password' })
      .min(8, { message: 'Must be at least 8 characters long' })
      .regex(/[a-z]/, { message: 'Must include at least one lowercase letter' })
      .regex(/[A-Z]/, { message: 'Must include at least one uppercase letter' })
      .regex(/\d/, { message: 'Must include at least one number' })
      .regex(/[\W_]/, {
        message: 'Must include at least one special character',
      }),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: 'Passwords must match',
    path: ['confirmPassword'],
  });

// Password validation function
const validatePassword = (password: string) => ({
  atLeast8Characters: {
    message: 'Must be at least 8 characters long',
    isError: password.length < 8,
  },
  lowerCase: {
    message: 'Must include at least one lowercase letter',
    isError: !/[a-z]/.test(password),
  },
  upperCase: {
    message: 'Must include at least one uppercase letter',
    isError: !/[A-Z]/.test(password),
  },
  includeNumber: {
    message: 'Must include at least one number',
    isError: !/\d/.test(password),
  },
  special: {
    message: 'Must include at least one special character',
    isError: !/[\W_]/.test(password),
  },
});

const useRegisterByEmail = () => {
  const router = useRouter();
  const { location } = useLocation();
  const { user } = useSession();
  const [email, setEmail] = useState<string | null>(null);
  const [passwordErrors, setPasswordErrors] = useState(validatePassword(''));
  const [userAddress, setUserAddress] = useState('');

  // Initialize React Hook Form
  const {
    register,
    control,
    handleSubmit,
    setValue,
    watch,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(registerSchema),
  });

  // Watch password field for validation updates
  useEffect(() => {
    const subscription = watch((value) => {
      if (value.password) {
        setPasswordErrors(validatePassword(value.password));
      }
    });
    return () => subscription.unsubscribe();
  }, [watch]);

  // Register mutation (handles user registration)
  const mutation = useMutation({
    mutationFn: async (payload: IRegisterWithEmailPayload) => {
      const response = await RegisterWithEmailAPI(payload);
      return response;
    },
    onSuccess: (data) => {
      if (data?.success) {
        customToast(t(data.message), 'success');
        setToken(data.token || '');
        if (email !== data.user.email) {
          setEmail(data.user.email);
        }
      }
    },
    onError: (error: any) => {
      customToast(
        error.response?.data?.message || 'An error occurred',
        'error',
      );
    },
  });

  // Form submit handler
  const onSubmit = useCallback(
    (formData: any) => {
      const { confirmPassword, ...rest } = formData;
      mutation.mutate({
        ...rest,

        location: {
          latitude: location?.latitude || 0,
          longitude: location?.longitude || 0,
          address: location?.fullAddress || '',
        },
      });
    },
    [location, mutation],
  );

  // Query for sending OTP after successful registration
  const { isSuccess, data, isError, isLoading } = useQuery({
    queryKey: ['SendOTP', email],
    queryFn: async () => {
      if (email) {
        return await SendAuthOTPAPI({ email });
      }
    },
    enabled: Boolean(email),
    staleTime: Infinity,
  });

  // Redirect to OTP page upon successful OTP request
  useEffect(() => {
    if (isSuccess && data?.OTP) {
      customToast(t('OTP sent successfully'), 'success');
      router.push({
        pathname: '/auth/otp',
        params: {
          _id: user?._id,
          type: 'register',
          validOTP: data?.OTP,
          email: email,
        },
      });
    }
  }, [isSuccess, data, router, email, user]);

  // Handle OTP sending failure
  useEffect(() => {
    if (isError) {
      customToast(t('Failed to send OTP'), 'error');
    }
  }, [isError]);

  return {
    register,
    setValue,
    errors,
    control,
    handleSubmit: handleSubmit(onSubmit),
    loading: mutation.isPending || isLoading,
    passwordErrors,
    ...mutation,
  };
};

export default useRegisterByEmail;
