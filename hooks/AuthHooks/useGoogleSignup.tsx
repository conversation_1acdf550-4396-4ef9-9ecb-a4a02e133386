import {
  GoogleSignin,
  isErrorWithCode,
  isSuccessResponse,
  statusCodes,
} from '@react-native-google-signin/google-signin';
import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { customToast } from '../useCustomToast';
import { IRegisterByGoogleBody, SignupByGoogleAPI } from '@/services/userAPI';
import { t } from 'i18next';
import { generateUniqueCode } from '@/utils/common';
import { setToken } from '@/utils/AuthSecureStore';
import { setUserId } from '@/utils/HandleSecureStore';
import { useState } from 'react';
import { useSession } from '@/context/AuthContext';
import { handleError } from '@/utils/errorHandler';

/**
 * Hook for handling Google signup flow
 * @returns Object containing googleSignup function and loading state
 */
export const useGoogleSignup = () => {
  const router = useRouter();
  const [isGoogleSigningIn, setIsGoogleSigningIn] = useState(false);
  const { setUser } = useSession();
  // Register user with Google credentials
  const mutation = useMutation({
    mutationKey: ['register-by-google'],
    mutationFn: async (payload: IRegisterByGoogleBody) => {
      return await SignupByGoogleAPI(payload);
    },
    onSuccess: (data) => {
      if (!data?.token) {
        customToast('Invalid response from server', 'error');
        return;
      }

      // Store authentication data
      setToken(data.token);

      // The API response might include user._id or we might need to use another identifier
      const userId = (data.user as any)?._id || data.user?.email;
      if (userId) {
        setUserId(userId);
      }

      // Update user in session context if available
      if (data.user) {
        // Type assertion to handle the type mismatch
        setUser(data.user as any);
      }

      // Show success message
      customToast('Account created successfully', 'success');

      // Navigate to main app
      router.replace({
        pathname: '/(protected)/(tabs)',
      });
    },
    onError: (error: any) => {
      // Handle specific error cases
      if (error?.response?.status === 409) {
        customToast('User already exists with this Google account', 'error', {
          text1: t('login with your google account'),
        });
        router.replace('/auth');
      } else if (
        error?.response?.status === 403 &&
        error?.response?.data?.user?.userStatus === 'unverified'
      ) {
        // Handle unverified user case
        customToast('Please verify your account to continue', 'error');
        router.replace({
          pathname: '/auth/otp',
          params: {
            email: error?.response?.data?.user?.email,
            type: 'verify',
          },
        });
      } else {
        handleError(error);
        // console.error(error);
        // customToast(
        //   error?.message || 'Failed to register with Google',
        //   'error',
        // );
      }
    },
  });

  // Google sign-in flow
  const googleSignup = async () => {
    try {
      setIsGoogleSigningIn(true);

      // Check if Google Play Services are available
      await GoogleSignin.hasPlayServices({
        showPlayServicesUpdateDialog: true,
      });

      // Sign out first to ensure a fresh sign-in attempt
      await GoogleSignin.signOut();

      // Start the Google sign-in flow
      const userInfo = await GoogleSignin.signIn();

      if (!userInfo.data?.idToken) {
        customToast('Failed to get authentication token from Google', 'error');
        return;
      }

      // Create a username from Google profile data
      const username = createUsernameFromGoogleData({
        name: userInfo.data?.user?.name,
        givenName: userInfo?.data?.user?.givenName,
        familyName: userInfo?.data?.user?.familyName,
      });

      if (userInfo.data) {
        // Register with our backend
        mutation.mutate({
          idToken: userInfo.data.idToken,
          user: {
            username,
            email: userInfo.data.user.email,
            familyName: userInfo.data.user.familyName,
            givenName: userInfo.data.user.givenName,
            id: userInfo.data.user.id,
            name: userInfo.data.user.name,
            photo: userInfo.data.user.photo,
          },
        });
      }
    } catch (error) {
      handleGoogleSignInError(error);
    } finally {
      setIsGoogleSigningIn(false);
    }
  };

  // Helper to create username from Google profile data
  const createUsernameFromGoogleData = (user: any) => {
    const baseName =
      (user.name && user.name.trim()) ||
      `${user.givenName || ''}${user.familyName || ''}`;
    return `${baseName}${generateUniqueCode(5)}`;
  };

  // Handle Google Sign-In errors
  const handleGoogleSignInError = (error: any) => {
    console.log('Google Sign-In Error:', error);

    if (isErrorWithCode(error)) {
      // Handle known error codes
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        // User cancelled the sign-in flow
      } else if (error.code === statusCodes.IN_PROGRESS) {
        customToast('Sign in is already in progress', 'info');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        customToast('Google Play Services not available or outdated', 'error');
      } else if (
        error.code === 'DEVELOPER_ERROR' ||
        error.message?.includes('DEVELOPER_ERROR')
      ) {
        console.error(
          'DEVELOPER_ERROR: This typically means the OAuth configuration is incorrect',
        );
        customToast(
          'Google Sign-In configuration error. Please contact support.',
          'error',
        );
      } else {
        customToast(`Google Sign-in error: ${error.message}`, 'error');
      }
    } else if (error.toString().includes('DEVELOPER_ERROR')) {
      console.error(
        'DEVELOPER_ERROR: This typically means the OAuth configuration is incorrect',
      );
      customToast(
        'Google Sign-In configuration error. Please contact support.',
        'error',
      );
    } else {
      customToast('Failed to sign in with Google', 'error');
    }
  };

  return {
    googleSignup,
    loading: mutation.isPending || isGoogleSigningIn,
    ...mutation,
  };
};
