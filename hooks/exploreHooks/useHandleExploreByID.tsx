import { getExploreById } from '@/services/ExploresAPI';
import { useQuery } from '@tanstack/react-query';

const useHandleExploreByID = ({ id }: { id: string }) => {
  // Fetch Query
  const query = useQuery({
    queryKey: ['getExploreById', id],
    queryFn: async () => {
      if (!id) return;

      const response = await getExploreById({ id });
      if (response?.data) {
      }

      return response;
    },

    enabled: !!id,
    retry: 2, // Retries on failure (optional)
    staleTime: 1000 * 60 * 5, // Caches for 5 minutes (optional)
  });

  return {
    ...query,
  };
};

export default useHandleExploreByID;
