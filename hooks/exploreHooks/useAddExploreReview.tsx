import { addExploreReviewAPI } from '@/services/ExploresAPI';
import { useMutation } from '@tanstack/react-query';

const useAddExploreReview = () => {
  const mutation = useMutation({
    mutationKey: ['create-explore-review'],
    mutationFn: async ({
      exploreId,
      review,
    }: {
      exploreId: string;
      review: string;
    }) => addExploreReviewAPI({ exploreId, review }),
  });
  return { ...mutation };
};

export default useAddExploreReview;
