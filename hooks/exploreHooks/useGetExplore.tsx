import { GetExploreAPI, IGetEXploreAPIParams } from '@/services/ExploresAPI';
import {
  useQuery,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

type ExploreDataType = Awaited<ReturnType<typeof GetExploreAPI>>;

export type UseGetExploreQueryOptions = UseQueryOptions<ExploreDataType>;
export type UseGetExploreQueryResult = UseQueryResult<ExploreDataType>;

const useGetExploreQuery = (
  params: IGetEXploreAPIParams,
  options?: UseGetExploreQueryOptions,
): UseGetExploreQueryResult => {
  return useQuery<ExploreDataType>({
    queryKey: ['get-explore', params],
    queryFn: () => GetExploreAPI(params),
    ...options,
  });
};

export default useGetExploreQuery;
