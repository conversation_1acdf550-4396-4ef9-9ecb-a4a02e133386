import { getExploreReviewAPI } from '@/services/ExploresAPI';
import { useQuery } from '@tanstack/react-query';

const useGetExploreReviews = (exploreId?: string, enabled?: boolean) => {
  const query = useQuery({
    queryKey: ['get-explore-reviews', exploreId],
    queryFn: async () => getExploreReviewAPI(exploreId || ''),
    enabled: enabled,
  });
  return { ...query };
};

export default useGetExploreReviews;
