import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';

const languages = [
  {
    key: 'ar',
    value: 'العربية',
  },
  {
    key: 'en',
    value: 'English',
  },
];
const useLanguage = () => {
  const [isArabic, setIsArabic] = useState<boolean>(false);
  const { i18n } = useTranslation();
  const [language, setLanguage] = useState<'ar' | 'en' | null>(null);

  const handleLanguageChange = React.useCallback(
    (v: 'ar' | 'en') => {
      if (v !== language) {
        i18n.changeLanguage(v).then((res) => {
          setLanguage(v);
        });
      }
    },
    [language, i18n],
  );

  React.useEffect(() => {
    if (i18n.language.includes('ar')) {
      setIsArabic(true);
    } else {
      setIsArabic(false);
    }
  }, []);

  return { isArabic, handleLanguageChange, languages, language };
};

export default useLanguage;
