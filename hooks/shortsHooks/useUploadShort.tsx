import { UploadShortAPI } from '@/services/shortsAPI';
import { handleError } from '@/utils/errorHandler';

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { ImagePickerAsset } from 'expo-image-picker';
import { customToast } from '../useCustomToast';
import { Platform } from 'react-native';
import { router } from 'expo-router';

interface IUploadPayload {
  file?: string; // video URI
  userId: string;
  title: string;
  description?: string;
  tags?: string[];
  image?: ImagePickerAsset; // image from ImagePicker
}

const useUploadShort = () => {
  const queryClient = useQueryClient();

  const generateFormData = (data: IUploadPayload): FormData => {
    const formData = new FormData();

    formData.append('userId', data.userId);
    formData.append('title', data.title);
    formData.append('description', data.title || '');

    // Append tags properly
    if (data.tags && data.tags.length > 0) {
      data.tags.forEach((tag) => formData.append('tags', tag));
    } else {
      formData.append('tags', '');
    }

    // Handle image upload from camera or picker
    if (data.image?.uri) {
      const imageFile = {
        uri:
          Platform.OS === 'ios'
            ? data.image.uri.replace('file://', '')
            : data.image.uri,
        name: `upload_${Date.now()}.jpg`,
        type: data.image.mimeType || 'image/jpeg',
      };
      formData.append('file', imageFile as any);
    }
    // Handle video upload
    else if (data.file) {
      formData.append('file', {
        uri: data.file,
        name: `video_${Date.now()}.mp4`,
        type: 'video/mp4',
      } as any);
    }

    return formData;
  };

  const mutation = useMutation({
    mutationKey: ['upload-short'],
    mutationFn: (formData: FormData) => UploadShortAPI(formData),
    onSuccess: (data) => {
      customToast(data?.message || 'Upload successful!', 'success');
      queryClient.invalidateQueries({ queryKey: ['shorts'] }); // Assuming "shorts" is the key for fetching shorts
      router.replace('/(protected)/(tabs)');
    },
    onError: (err) => {
      if (err.message) {
        customToast(err.message, 'error');
        return;
      }
      handleError(err);
    },
  });

  return { ...mutation, generateFormData };
};

export default useUploadShort;
