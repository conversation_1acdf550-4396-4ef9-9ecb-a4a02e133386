import { GetShortsAPI } from '@/services/shortsAPI';
import { useQuery } from '@tanstack/react-query';

const useGetShorts = ({
  page = 1,
  shortId = '',
  userId = '',
  limit = 10,
}: {
  page?: number;
  shortId?: string;
  userId?: string;
  limit?: number;
}) => {
  const queryKey = ['get-shorts', page, shortId, userId, limit];

  const {
    data: shortsData,
    isLoading,
    isError,
    error,
  } = useQuery({
    queryKey,
    queryFn: () => GetShortsAPI({ page, shortId, userId, limit }),
    staleTime: 60 * 1000, // 1 minutes
  });

  return { shortsData, isLoading, isError, error };
};

export default useGetShorts;
