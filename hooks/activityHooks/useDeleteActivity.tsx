import { deleteActivityAPI } from '@/services/ActivitiesAPI';
import { useMutation } from '@tanstack/react-query';

import { useRouter } from 'expo-router';
import { customToast } from '../useCustomToast';

const useDeleteActivity = () => {
  const router = useRouter();

  const mutation = useMutation({
    mutationKey: ['delete-activity'],
    mutationFn: async (activityId: string) => {
      return await deleteActivityAPI(activityId);
    },
    onSuccess: () => {
      customToast('Activity Deleted', 'success');
      router.replace('/(protected)/(tabs)');
    },
    onError: (error) => {
      customToast(error.message, 'success');
    },
  });
  return { ...mutation };
};

export default useDeleteActivity;
