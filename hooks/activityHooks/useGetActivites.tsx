import { getCalenderActivitiesAPI } from '@/services/HomeScreenAPI';
import { useSuspenseQuery } from '@tanstack/react-query';

const useCalendarActivities = ({ userId }: { userId?: string }) => {
  return useSuspenseQuery({
    queryKey: ['calender-activities', userId],
    queryFn: async () => {
      if (!userId) return []; // Return an empty array if userId is not available
      return await getCalenderActivitiesAPI(userId);
    },
  });
};

export default useCalendarActivities;
