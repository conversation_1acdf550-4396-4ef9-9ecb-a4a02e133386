import { getActivityByIdAPI } from '@/services/ActivitiesAPI';
import { useQuery } from '@tanstack/react-query';

const useGetActivityByID = (activityId: string) => {
  // Get Activity by ID
  const query = useQuery({
    queryKey: ['get-activityId', activityId],
    queryFn: async () => await getActivityByIdAPI(activityId),
    enabled: !!activityId, // Run only when `id` is truthy
    // staleTime: 60 * 1000, // 1 minutes (120,000 ms)
  });
  return { ...query };
};

export default useGetActivityByID;
