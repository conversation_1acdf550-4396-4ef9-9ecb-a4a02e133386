import { createActivityAPI } from '@/services/ActivitiesAPI';
import { RESTRICTIONS_ENUM } from '@/types';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';
import { ImagePickerAsset } from 'expo-image-picker';
import { useForm } from 'react-hook-form';

import { zodResolver } from '@hookform/resolvers/zod';
import { useRouter } from 'expo-router';
import { ZodError } from 'zod';

import { useCallback, useMemo } from 'react';
import { customToast } from '../useCustomToast';
import useImagePicker from '../useImagePicker';

import { changeAssetToFormData } from '@/utils/ImageAssetToFormData';
import { useActivityStore } from '@/stores/useActivityStore';
import {
  IForm,
  MAX_DESCRIPTION_LETTERS,
  validateCreateActivitySchema,
} from '@/utils/validations/validateActivitySchema';

const currentDate = new Date();

const useCreateActivity = () => {
  const router = useRouter();
  const { setActivityForm } = useActivityStore();

  const { pickImage, result, setResult } = useImagePicker({
    imagePickOptions: { allowsMultipleSelection: true, allowsEditing: false },
  });

  const defaultValues = useMemo<IForm>(
    () => ({
      virtual: false,
      isPaid: false,
      permissionToJoin: false,
      restriction: RESTRICTIONS_ENUM.public,
      start: {
        date: currentDate,
        time: currentDate,
      },
      end: {
        date: currentDate,
        time: currentDate,
      },
      category: [],
      socialCircles: [],
      description: '',
      location: { address: '', latitude: 0, longitude: 0 },
      activityName: '',
      link: '',
      maxParticipants: '',
      price: '',
      images: [],
    }),
    [],
  );

  const {
    control,
    handleSubmit,
    setValue,
    watch,
    setError,
    reset,
    formState: { errors },
  } = useForm<IForm>({
    resolver: zodResolver(validateCreateActivitySchema),
    defaultValues,
    mode: 'onChange',
  });

  const createFormData = useCallback(
    (formValues: IForm, images: ImagePickerAsset[]) => {
      const formData = new FormData();

      formData.append('price', String(formValues.price) || '0');
      formData.append('currency', 'sar');
      formData.append('startDate', formValues.start.date.toISOString());
      formData.append('endDate', formValues.end.date.toISOString());
      formData.append('startTime', formValues.start.time.toISOString());
      formData.append('endTime', formValues.end.time.toISOString());
      formData.append('name', formValues.activityName);
      formData.append('description', formValues.description);
      formData.append('link', formValues.link || '');
      formData.append('virtual', JSON.stringify(formValues.virtual));
      formData.append(
        'category',
        formValues.category?.map((c) => c._id).join(', ') || '',
      );
      formData.append(
        'permissionToJoin',
        JSON.stringify(formValues.permissionToJoin),
      );
      formData.append('restriction', formValues.restriction);
      formData.append(
        'maxParticipants',
        String(formValues.maxParticipants || 0),
      );
      formData.append('longitude', String(formValues.location?.longitude || 0));
      formData.append('latitude', String(formValues.location?.latitude || 0));
      formData.append('address', formValues.location?.address || '');

      images.forEach((img) => {
        changeAssetToFormData(formData, 'images', img);
      });

      return formData;
    },
    [],
  );

  const mutation = useMutation({
    mutationKey: ['create-activity'],
    mutationFn: async () => {
      try {
        const formValues = watch(); // Get all current form values

        // Create FormData with all required fields
        const formData = createFormData(formValues, result || []);

        // Send the API request
        return await createActivityAPI(formData);
      } catch (error) {
        if (error instanceof ZodError) {
          error.errors.forEach((err) => {
            setError(err.path[0] as keyof IForm, { message: err.message });
          });
        } else {
          handleError(error);
        }
        throw error; // Re-throw to trigger onError handler
      }
    },
    onSuccess: (data) => {
      if (!data?.success) return;
      customToast('Activity created successfully', 'success');
      router.replace(`/(protected)/activity/${data.calender._id}`);
      reset();
      setActivityForm(null);
    },
    onError: (error: any) => {
      if (error?.status === 400) {
        customToast(error?.response?.data?.error, 'error');
        return;
      }
      customToast(error?.message || error?.response?.data?.error, 'error');
    },
  });

  const handleDeleteImage = useCallback(
    (uri: string) => {
      setResult((prev) => prev?.filter((asset) => asset?.uri !== uri) || []);
    },
    [setResult],
  );

  return {
    control,
    handleSubmit: handleSubmit(() => mutation.mutate()),
    setValue,
    watch,
    errors,
    isSubmitting: mutation.isPending,
    MAX_DESCRIPTION_LETTERS,
    pickImage,
    images: result,
    handleDeleteImage,
  };
};

export default useCreateActivity;
