import {
  ISendActivityToUserPayload,
  sendActivityInviteToUserAPI,
} from '@/services/ActivitiesAPI';
import { useMutation } from '@tanstack/react-query';

const useSendActivityInviteToUser = () => {
  const mutation = useMutation({
    mutationKey: ['send-activity-invite-to-user'],
    mutationFn: async (payload: ISendActivityToUserPayload) =>
      sendActivityInviteToUserAPI(payload),
  });
  return { ...mutation };
};

export default useSendActivityInviteToUser;
