import {
  respondToAdminInviteToActivityAPI,
  respondToUserActivityJoinRequestAPI,
  respondToAdminInviteToActivityPayload,
  IAcceptPendingAPIPayLoad,
} from '@/services/ActivitiesAPI';
import { useMutation } from '@tanstack/react-query';

function useAcceptPendingRequests() {
  const mutation = useMutation({
    mutationKey: ['accept-pending-request'],
    mutationFn: async (payload: IAcceptPendingAPIPayLoad) =>
      respondToUserActivityJoinRequestAPI(payload),
  });
  return { ...mutation };
}

function useRespondToAdminRequest() {
  const mutation = useMutation({
    mutationKey: ['accept-admin-request'],
    mutationFn: async (payload: respondToAdminInviteToActivityPayload) =>
      respondToAdminInviteToActivityAPI(payload),
  });
  return { ...mutation };
}

export { useAcceptPendingRequests, useRespondToAdminRequest };
