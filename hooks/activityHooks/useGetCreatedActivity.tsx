import { GetCreatedActivitiesAPI } from '@/services/ActivitiesAPI';
import {
  useQuery,
  UseQueryOptions,
  UseQueryResult,
} from '@tanstack/react-query';

// Infer the return type of the API
type CreatedActivitiesData = Awaited<
  ReturnType<typeof GetCreatedActivitiesAPI>
>;

// Public types for external usage
export type UseGetCreatedActivitiesOptions = Omit<
  UseQueryOptions<CreatedActivitiesData>,
  'queryKey' | 'queryFn'
>;

export type UseGetCreatedActivitiesResult =
  UseQueryResult<CreatedActivitiesData>;

const useGetCreatedActivities = (
  options?: UseGetCreatedActivitiesOptions,
): UseGetCreatedActivitiesResult => {
  return useQuery<CreatedActivitiesData>({
    queryKey: ['getCreatedActivities'],
    queryFn: GetCreatedActivitiesAPI,
    ...options,
  });
};

export default useGetCreatedActivities;
