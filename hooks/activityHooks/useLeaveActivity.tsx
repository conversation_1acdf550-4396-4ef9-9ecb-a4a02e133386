import { leaveActivityAPI } from '@/services/ActivitiesAPI';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { t } from 'i18next';

const useLeaveActivity = () => {
  const mutation = useMutation({
    mutationKey: ['leave-request'],
    mutationFn: async (activityId: string) => {
      return await leaveActivityAPI(activityId);
    },
    onSuccess: () => {
      customToast(t('you-had-left-the-activity'), 'success');
    },
    onError: (error) => {
      handleError(error);
    },
  });
  return { ...mutation };
};

export default useLeaveActivity;
