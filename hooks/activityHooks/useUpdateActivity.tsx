import {
  IUpdateActivityPayload,
  updateActivityAPI,
} from '@/services/ActivitiesAPI';
import { useMutation } from '@tanstack/react-query';

const useUpdateActivity = () => {
  const mutation = useMutation({
    mutationKey: ['update-activity'],
    mutationFn: async (payload: IUpdateActivityPayload) =>
      updateActivityAPI(payload),
  });
  return { ...mutation };
};

export default useUpdateActivity;
