import { getActivityParticipantsByIdAPI } from '@/services/ActivitiesAPI';
import { useQuery } from '@tanstack/react-query';

const useGetParticipantsByActivityID = (id: string) => {
  const query = useQuery({
    queryKey: ['get-activity-participants', id],
    queryFn: async () => await getActivityParticipantsByIdAPI(id),
    enabled: !!id,
  });

  return { ...query };
};

export default useGetParticipantsByActivityID;
