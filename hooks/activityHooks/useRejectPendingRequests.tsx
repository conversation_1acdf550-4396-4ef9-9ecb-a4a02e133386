import { rejectPendingRequestAPI } from '@/services/ActivitiesAPI';
import { useMutation } from '@tanstack/react-query';

function useRejectPendingRequests() {
  const mutation = useMutation({
    mutationKey: ['reject-pending-request'],
    mutationFn: async (payload: {
      activityId: string;
      participantId: string;
    }) => rejectPendingRequestAPI(payload),
  });
  return { ...mutation };
}

export default useRejectPendingRequests;
