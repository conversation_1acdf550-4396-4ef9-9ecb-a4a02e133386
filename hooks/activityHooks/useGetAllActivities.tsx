import {
  getAllActivitiesAPI,
  GetAllActivitiesAPIPayload,
  IGetAllActivitiesResponse,
} from '@/services/ActivitiesAPI';
import { useInfiniteQuery, InfiniteData } from '@tanstack/react-query';

const useGetAllActivities = (
  filters: Omit<GetAllActivitiesAPIPayload, 'page'>,
) => {
  const query = useInfiniteQuery<
    IGetAllActivitiesResponse,
    Error,
    InfiniteData<IGetAllActivitiesResponse>,
    (string | Omit<GetAllActivitiesAPIPayload, 'page'>)[],
    number
  >({
    queryKey: ['get-all-activities', filters],
    queryFn: async ({ pageParam }) => {
      return await getAllActivitiesAPI({ ...filters, page: pageParam });
    },
    initialPageParam: 1,
    getNextPageParam: (lastPage) => {
      const { page, limit, total } = lastPage;
      const maxPage = Math.ceil(total / limit);
      return page < maxPage ? page + 1 : undefined;
    },
    select: (data) => ({
      ...data,
      pages: data.pages.map((page) => page),
    }),
  });
  return { ...query };
};

export default useGetAllActivities;
