import { GetJoinedActivitiesAPI } from '@/services/ActivitiesAPI';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

const useGetJoinedActivities = (
  options?: Omit<
    UseQueryOptions<any, unknown, any, any>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useQuery({
    queryKey: ['getJoinedActivities'],
    queryFn: GetJoinedActivitiesAPI,
    ...options, // Spread options to allow partial overrides
  });
};

export default useGetJoinedActivities;
