import {
  useCameraPermission,
  useMicrophonePermission,
} from 'react-native-vision-camera';
import * as MediaLibrary from 'expo-media-library';
import * as ImagePicker from 'expo-image-picker';
import * as Location from 'expo-location';
import { useCallback, useMemo, useState } from 'react';

export const useGetAllPermissions = () => {
  const {
    hasPermission: hasCameraPermission,
    requestPermission: requestCameraPermission,
  } = useCameraPermission();

  const {
    hasPermission: hasMicPermission,
    requestPermission: requestMicPermission,
  } = useMicrophonePermission();

  const [mediaPermission, requestMediaPermission] =
    MediaLibrary.usePermissions();
  const [pickerPermission, requestPickerPermission] =
    ImagePicker.useMediaLibraryPermissions();
  const [locationPermission, requestLocationPermission] =
    Location.useForegroundPermissions();

  const [permissions, setPermissions] = useState({
    camera: hasCameraPermission,
    mic: hasMicPermission,
    mediaLibrary: mediaPermission?.granted ?? false,
    imagePicker: pickerPermission?.granted ?? false,
    location: locationPermission?.granted ?? false,
  });

  const refreshPermissions = useCallback(() => {
    const newPermissions = {
      camera: hasCameraPermission,
      mic: hasMicPermission,
      mediaLibrary: mediaPermission?.granted ?? false,
      imagePicker: pickerPermission?.granted ?? false,
      location: locationPermission?.granted ?? false,
    };

    // Only update if there's an actual change
    const hasChanged = Object.entries(newPermissions).some(
      ([key, value]) => permissions[key as keyof typeof permissions] !== value,
    );

    if (hasChanged) {
      setPermissions(newPermissions);
    }
  }, [
    hasCameraPermission,
    hasMicPermission,
    mediaPermission,
    pickerPermission,
    locationPermission,
    permissions,
  ]);

  const requestPermissions = useMemo(
    () => ({
      camera: async () => {
        await requestCameraPermission();
        refreshPermissions();
      },
      mic: async () => {
        await requestMicPermission();
        refreshPermissions();
      },
      mediaLibrary: async () => {
        await requestMediaPermission();
        refreshPermissions();
      },
      imagePicker: async () => {
        await requestPickerPermission();
        refreshPermissions();
      },
      location: async () => {
        await requestLocationPermission();
        refreshPermissions();
      },
    }),
    [
      requestCameraPermission,
      requestMicPermission,
      requestMediaPermission,
      requestPickerPermission,
      requestLocationPermission,
      refreshPermissions,
    ],
  );

  return {
    permissions,
    requestPermissions,
    refreshPermissions,
  };
};
