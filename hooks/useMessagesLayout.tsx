import { useMemo } from 'react';
import { Platform } from 'react-native';
import { useBottomTabBarHeight } from '@react-navigation/bottom-tabs';

interface UseMessagesLayoutReturn {
  bottomMargin: number;
  listClassName: string;
}

/**
 * Custom hook to manage layout calculations for messages screen
 * Handles platform-specific bottom tab spacing
 */
export const useMessagesLayout = (): UseMessagesLayoutReturn => {
  const bottomTabHeight = useBottomTabBarHeight();

  // Memoize layout calculations
  const layoutValues = useMemo(() => {
    const bottomMargin = Platform.OS === 'ios' ? bottomTabHeight : 25;
    const listClassName = `flex-1 mb-[${bottomMargin}px]`;

    return {
      bottomMargin,
      listClassName,
    };
  }, [bottomTabHeight]);

  return layoutValues;
};
