import axiosInstance from '@/utils/axiosInstance';
import { useQuery } from '@tanstack/react-query';
import { useSession } from '@/context/AuthContext';
import { Platform } from 'react-native';

const useSendNotificationToken = (tokenId: string | null) => {
  const { userId } = useSession();

  const query = useQuery({
    queryKey: ['send-notification'],
    queryFn: async () =>
      await axiosInstance.post('/user/auth/addToken', { tokenId }),
    enabled: !!tokenId,
  });

  const { data } = useQuery({
    queryKey: ['user-platform'],
    queryFn: async () => {
      const formData = new FormData();

      formData.append('isApple', Platform.OS === 'ios' ? 'true' : 'false');
      formData.append('userId', userId || '');

      return await axiosInstance.put('/user/auth/updateUser', formData, {
        headers: {
          Accept: 'application/json',
          'Content-Type': 'multipart/form-data',
        },
        transformRequest: (data) => data,
      });
    },
    enabled: !!userId,
  });

  return { ...query };
};

export default useSendNotificationToken;
