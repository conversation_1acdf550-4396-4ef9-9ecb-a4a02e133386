import { updatePasswordAPI } from '@/services/userAPI';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';

interface IPayload {
  oldPassword: string;
  newPassword: string;
}

const useChangePassword = () => {
  const mutation = useMutation({
    mutationKey: ['change-password'],
    mutationFn: async ({ oldPassword, newPassword }: IPayload) => {
      return await updatePasswordAPI({ oldPassword, newPassword });
    },
    onError: (err) => {
      handleError(err);
    },
  });
  return { ...mutation };
};

export default useChangePassword;
