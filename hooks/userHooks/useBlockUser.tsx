import { blockUser<PERSON><PERSON>, unBlockUserAP<PERSON> } from '@/services/userAPI';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';
import { useUserStore } from '@/stores/useUserStore';

interface UseBlockUserResult {
  toggleBlockUser: (userId: string, isBlocked: boolean, userInfo?: { username: string; displayName: string; image?: string }) => void;
  blockUser: (userId: string, userInfo?: { username: string; displayName: string; image?: string }) => void;
  unblockUser: (userId: string) => void;
  isLoading: boolean;
  isBlockingUser: boolean;
  isUnblockingUser: boolean;
}

const useBlockUser = (): UseBlockUserResult => {
  const queryClient = useQueryClient();
  const { addBlockedUser, removeBlockedUser } = useUserStore();

  const blockMutation = useMutation({
    mutationKey: ['block-user'],
    mutationFn: async (userId: string) => blockUserAPI(userId),
    onSuccess: (_, userId) => {
      customToast('User blocked successfully', 'success');

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['user-data'] });
      queryClient.invalidateQueries({ queryKey: ['get-blocked-users'] });
      queryClient.invalidateQueries({ queryKey: ['userData', userId] });
    },
    onError: (error) => {
      handleError(error);
    },
  });

  const unblockMutation = useMutation({
    mutationKey: ['unblock-user'],
    mutationFn: async (userId: string) => unBlockUserAPI(userId),
    onSuccess: (_, userId) => {
      customToast('User unblocked successfully', 'success');

      // Remove from global store
      removeBlockedUser(userId);

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['user-data'] });
      queryClient.invalidateQueries({ queryKey: ['get-blocked-users'] });
      queryClient.invalidateQueries({ queryKey: ['userData', userId] });
    },
    onError: (error) => {
      handleError(error);
    },
  });

  const blockUser = (userId: string, userInfo?: { username: string; displayName: string; image?: string }) => {
    blockMutation.mutate(userId);

    // Optimistically add to global store if userInfo is provided
    if (userInfo) {
      addBlockedUser({
        _id: userId,
        username: userInfo.username,
        displayName: userInfo.displayName,
        image: userInfo.image,
        blockedAt: new Date(),
      });
    }
  };

  const unblockUser = (userId: string) => {
    unblockMutation.mutate(userId);
  };

  const toggleBlockUser = (userId: string, isBlocked: boolean, userInfo?: { username: string; displayName: string; image?: string }) => {
    if (isBlocked) {
      unblockUser(userId);
    } else {
      blockUser(userId, userInfo);
    }
  };

  const isLoading = blockMutation.isPending || unblockMutation.isPending;

  return {
    toggleBlockUser,
    blockUser,
    unblockUser,
    isLoading,
    isBlockingUser: blockMutation.isPending,
    isUnblockingUser: unblockMutation.isPending,
  };
};

export default useBlockUser;
