import { ISearchUserPayload, searchUserAPI } from '@/services/userAPI';
import { useQuery } from '@tanstack/react-query';

export const useSearchUser = (params: ISearchUserPayload, enabled = true) => {
  return useQuery({
    queryKey: ['searchUser', params],
    queryFn: () => searchUserAPI(params),
    enabled: enabled && !!params.query, // don't run if no search term
    staleTime: 1000 * 60 * 1, // 1 minute cache
  });
};
