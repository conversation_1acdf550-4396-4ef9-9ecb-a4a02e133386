import { unFollowUserAPI } from '@/services/userAPI';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';

const useUnFollowUser = () => {
  const mutation = useMutation({
    mutationKey: ['follow-user'],
    mutationFn: async (userId: string) => unFollowUserAPI(userId),
    onSuccess() {
      customToast('User unFollowed successfully', 'success');
    },
    onError(error) {
      handleError(error);
    },
  });
  return { ...mutation };
};

export default useUnFollowUser;
