import { followUserAPI } from '@/services/userAPI';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';

const useFollowUser = () => {
  const mutation = useMutation({
    mutationKey: ['follow-user'],
    mutationFn: async (userId: string) => followUserAPI(userId),
    onSuccess() {
      customToast('User followed successfully', 'success');
    },
    onError(error) {
      handleError(error);
    },
  });
  return { ...mutation };
};

export default useFollowUser;
