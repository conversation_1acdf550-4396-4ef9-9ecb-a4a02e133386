import { useSession } from '@/context/AuthContext';
import { useCallback } from 'react';

const useHandleFollowState = () => {
  const { user } = useSession();

  const isInFollowers = useCallback(
    (userId: string) => {
      if (!user) return false;
      return user.followers?.includes(userId) || false;
    },
    [user],
  );

  const isInFollowing = useCallback(
    (userId: string) => {
      if (!user) return false;
      return user.following?.includes(userId) || false;
    },
    [user],
  );

  return { isInFollowers, isInFollowing };
};

export default useHandleFollowState;
