import { UpdateUserProfileAPI } from '@/services/userAPI';
import { useCallback, useEffect } from 'react';
import { z } from 'zod';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '@/hooks/useCustomToast';
import { handleError } from '@/utils/errorHandler';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { changeAssetToFormData } from '@/utils/ImageAssetToFormData';
import { IUser } from '@/types';
import useImagePicker from '../useImagePicker';
const imageFileSchema = z.object({
  uri: z.string().min(1, { message: 'Image URI is required' }),
  name: z.string().optional(),
  type: z.string().optional(),
});

const FormSchema = z.object({
  displayName: z
    .string()
    .min(3, { message: 'Fullname must be at least 3 characters' })
    .max(20),
  username: z.string().min(3).max(20),
  phoneNumber: z
    .string()
    .regex(/^\d{9,15}$/, { message: 'Enter a valid phone number' })
    .optional(),
  countryCode: z.string().startsWith('+').optional(),
  dateOfBirth: z.date().nullable(),
  gender: z.enum(['male', 'female']),
  image: imageFileSchema.nullable(),
  displayImage: z.string().nullable().optional(),
  email: z.string().email(),
});

type IForm = z.infer<typeof FormSchema>;

const useUpdateUser = (user?: Partial<IUser> | null) => {
  const {
    control,
    watch,
    handleSubmit,
    setValue,
    reset,

    formState: { errors, isValid },
  } = useForm<IForm>({
    defaultValues: {
      displayName: '',
      phoneNumber: '',
      countryCode: '+966',
      dateOfBirth: null,
      gender: 'male',
      image: null,
      displayImage: '',
      email: '',
      username: '',
    },
    resolver: zodResolver(FormSchema),
  });

  const { result, pickImage, setResult } = useImagePicker({
    imagePickOptions: { mediaTypes: ['images'] },
  });

  const handleRemoveImage = useCallback(() => {
    setValue('image', null);
    setValue('displayImage', null);
    setResult([]);
  }, [setValue, setResult]);

  const handleSaveProfile = async ({ userId }: { userId: string }) => {
    const formData = watch();
    mutation.mutate({ ...formData, userId });
  };

  const mutation = useMutation({
    mutationKey: ['updateUserProfile'],
    mutationFn: async (payload: IForm & { userId: string }) => {
      if (!payload.dateOfBirth) {
        customToast('Date of birth is required', 'error');
        return;
      }

      const formData = new FormData();
      formData.append('displayName', payload.displayName);
      formData.append('phoneNumber', payload.phoneNumber || '');
      formData.append('gender', payload.gender);
      formData.append('countryCode', payload.countryCode || '');
      formData.append('dateOfBirth', String(payload.dateOfBirth));
      formData.append('userId', payload.userId);

      // ✅ Append image if selected
      if (result.length > 0) {
        changeAssetToFormData(formData, 'image', result[0]);
      }

      return await UpdateUserProfileAPI(formData);
    },
    onSuccess: (data) => {
      if (data) {
        customToast('user updated successfully', 'success');
      }
    },
    onError: (error: any) => {
      if (error.response?.data?.message) {
        customToast(error.response.data.message, 'error');
      }
      handleError(error);
    },
  });
  // ✅ Update form when `user` data changes
  useEffect(() => {
    if (user) {
      reset({
        displayImage: user.image,
        phoneNumber: user.phoneNumber,
        countryCode: user.countryCode,
        username: user.username,
        displayName: user.displayName,
        email: user.email,
        dateOfBirth: user.dateOfBirth,
        gender: user.gender,
      });
    }
  }, [user, reset]);

  useEffect(() => {
    if (result?.[0]?.uri) {
      setValue('displayImage', result[0].uri);
    }
  }, [result?.[0]]);

  return {
    errors,
    pickImage,
    handleSaveProfile,
    isLoading: mutation.isPending,
    handleRemoveImage,
    control,
    isValid,
    watch,
    handleSubmit,
    setValue,
    ...mutation,
  };
};

export default useUpdateUser;
