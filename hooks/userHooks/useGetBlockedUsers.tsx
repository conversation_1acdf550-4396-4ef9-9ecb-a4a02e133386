import { getBlockedUsersAPI, IBlockedUsersAPIResponse } from '@/services/userAPI';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';
import { useUserStore } from '@/stores/useUserStore';
import { useEffect } from 'react';
import { useSession } from '@/context/AuthContext';

interface UseGetBlockedUsersOptions extends Omit<UseQueryOptions<IBlockedUsersAPIResponse, Error>, 'queryKey' | 'queryFn'> {
  enabled?: boolean;
}

const useGetBlockedUsers = (options?: UseGetBlockedUsersOptions) => {
  const { userId } = useSession();
  const { setBlockedUsers } = useUserStore();

  const query = useQuery<IBlockedUsersAPIResponse, Error>({
    queryKey: ['get-blocked-users', userId],
    queryFn: getBlockedUsersAPI,
    enabled: !!userId && (options?.enabled !== false),
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes (formerly cacheTime)
    retry: 2,
    retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    ...options,
  });

  // Update global store when data is successfully fetched
  useEffect(() => {
    if (query.isSuccess && query.data?.blockedUsers) {
      setBlockedUsers(query.data.blockedUsers);
    }
  }, [query.isSuccess, query.data?.blockedUsers, setBlockedUsers]);

  return {
    ...query,
    blockedUsers: query.data?.blockedUsers || [],
    isBlockedUsersLoading: query.isLoading,
    blockedUsersError: query.error,
  };
};

export default useGetBlockedUsers;
