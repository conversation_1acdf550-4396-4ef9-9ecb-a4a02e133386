import { customToast } from '../useCustomToast';
import { useMutation } from '@tanstack/react-query';
import axiosInstance from '@/utils/axiosInstance';
import { IUser } from '@/types';

interface IUpdateLocationPayLoad {
  latitude: number;
  longitude: number;
  address?: string;
}

type IUpdateProfileResponse = {
  user: IUser;
  message: string;
  status: number;
};

// Create a type for the context values we need
interface LocationContextValues {
  setUser: (user: IUser) => void;
  user: IUser | null;
}

const useUpdateLocation = (contextValues?: LocationContextValues) => {
  const mutation = useMutation({
    mutationKey: ['updateUserLocation'],
    mutationFn: async (location: IUpdateLocationPayLoad) => {
      // Validate before making the request
      if (!location) {
        customToast('Please provide your location', 'error');
        throw new Error('Location address is required'); // Prevents API call
      }

      // Skip update if user already has a location
      if (contextValues?.user?.address && 
          contextValues.user?.location?.latitude && 
          contextValues.user?.location?.longitude) {
        return { data: { user: contextValues.user, message: 'Location already exists', status: 200 } };
      }

      const formData = new FormData();
      formData.append('latitude', String(location.latitude));
      formData.append('longitude', String(location.longitude));
      formData.append('address', location.address ?? ''); // Avoid "undefined"

      return axiosInstance.put<IUpdateProfileResponse>(
        '/user/auth/updateUser',
        formData,
        {
          headers: {
            Accept: 'application/json',
            'Content-Type': 'multipart/form-data',
          },
        },
      );
    },
    onSuccess: (data) => {
      if (contextValues?.setUser) {
        contextValues.setUser(data.data.user);
      }
    },
    onError: (error: any) => {
      console.log(error);
      customToast(
        error.response?.data?.message ||
          'Failed to update location. Try again.',
        'error',
      );
    },
  });

  return { ...mutation };
};

export default useUpdateLocation;
