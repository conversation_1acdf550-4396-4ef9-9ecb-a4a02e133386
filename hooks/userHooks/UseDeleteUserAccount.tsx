import { useMutation } from '@tanstack/react-query';
import { DeleteUserAPI } from '@/services/userAPI';
import { customToast } from '@/hooks/useCustomToast';
import { useSession } from '@/context/AuthContext';
import { useRouter } from 'expo-router';

const UseDeleteUserAccount = () => {
  const { user, signOut } = useSession();
  const router = useRouter();
  const mutaion = useMutation({
    mutationKey: ['delete-user'],
    mutationFn: async () => {
      if (!user) throw new Error('User not found');
      await DeleteUserAPI({ userId: user._id });
    },
    onSuccess: () => {
      customToast('Account Deleted successfully', 'success');
      signOut();
    },
    onError: (error) => {
      customToast(error?.message, 'error');
      if (error.message?.toLowerCase() === 'user not found') {
        signOut();
        router.replace('/auth');
      }
    },
  });
  return { ...mutaion };
};
export default UseDeleteUserAccount;
