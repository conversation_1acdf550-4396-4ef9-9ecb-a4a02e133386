import { updateUserInterestsAPI } from '@/services/userAPI';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';

const useUpdateInterests = () => {
  const mutation = useMutation({
    mutationKey: ['updateInterests'],
    mutationFn: async (FormData: FormData) => {
      return await updateUserInterestsAPI(FormData);
    },
    onSuccess: () => {
      customToast('update success', 'success');
    },
    onError: (err) => {
      handleError(err);
    },
  });

  return { ...mutation };
};

export default useUpdateInterests;
