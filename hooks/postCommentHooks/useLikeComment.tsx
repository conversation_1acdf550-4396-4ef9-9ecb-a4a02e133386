import { likeCommentAPI } from '@/services/commentAPI';
import { useMutation } from '@tanstack/react-query';

const useLikeComment = () => {
  const mutation = useMutation({
    mutationKey: ['like-comment'],
    // Post comment params for now in API V1  carries postId, comment , images[] all sent as formData
    mutationFn: async (commentId: string) => likeCommentAPI(commentId),
  });

  return { ...mutation };
};

export default useLikeComment;
