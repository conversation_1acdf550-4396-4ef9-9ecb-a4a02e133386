import {
  getAllPostCommentsById,
  IGetCommentsPayload,
} from '@/services/commentAPI';
import { useQuery } from '@tanstack/react-query';

const useGetCommentsByPostId = (params: IGetCommentsPayload) => {
  const query = useQuery({
    queryKey: [
      'get-comments-byPostId',
      params.postId,
      params.limit,
      params.page,
    ], // Avoid spreading objects
    queryFn: () => getAllPostCommentsById(params),
    enabled: !!params.postId, // Only fetch when postId is available
  });

  return { ...query };
};

export default useGetCommentsByPostId;
