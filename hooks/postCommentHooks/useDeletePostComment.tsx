import { deleteCommentAPI } from '@/services/commentAPI';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';

const useDeletePostComment = () => {
  const mutation = useMutation({
    mutationKey: ['delete-comment'],
    mutationFn: async (commentId: string) => deleteCommentAPI(commentId),
    onError: (error) => {
      handleError(error);
    },
  });
  return { ...mutation };
};

export default useDeletePostComment;
