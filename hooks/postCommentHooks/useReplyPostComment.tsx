import { replayPostCommentAPI } from '@/services/commentAPI';
import { useMutation } from '@tanstack/react-query';

const useReplyPostComment = () => {
  const mutation = useMutation({
    mutationKey: ['reply-post-comment'],
    // Post comment params for now in API V1  carries postId, comment , images[] all sent as formData
    mutationFn: async (formData: FormData) => replayPostCommentAPI(formData),
  });

  return { ...mutation };
};

export default useReplyPostComment;
