import { updateCommentAPI } from '@/services/commentAPI';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';

const useUpdatePostComment = () => {
  const mutation = useMutation({
    mutationKey: ['updateComment'],
    mutationFn: async (formData: FormData) => updateCommentAPI(formData),
    onError: (error) => {
      handleError(error);
    },
  });
  return { ...mutation };
};

export default useUpdatePostComment;
