import { createCommentAPI } from '@/services/commentAPI';
import { useMutation } from '@tanstack/react-query';

const usePostComment = () => {
  const mutation = useMutation({
    mutationKey: ['create-post'],
    // Post comment params for now in API V1  carries postId, comment , images[] all sent as formData
    mutationFn: async (formData: FormData) => createCommentAPI(formData),
  });

  return { ...mutation };
};

export default usePostComment;
