import { useMutation } from '@tanstack/react-query';
import { toggleUserNotificationSeenStatusAPI } from '@/services/NotificationsAPI';

const UseToggleUserNotifications = () => {
  const mutation = useMutation({
    mutationKey: ['toggleUserNotifications'],
    mutationFn: async (reciveNotificaitons: boolean) =>
      toggleUserNotificationSeenStatusAPI(reciveNotificaitons),
  });
  return { ...mutation };
};
export default UseToggleUserNotifications;
