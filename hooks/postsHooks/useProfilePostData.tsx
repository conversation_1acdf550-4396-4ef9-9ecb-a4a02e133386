import { useState, useEffect, useCallback, useMemo, SetStateAction } from 'react';
import { router } from 'expo-router';
import { IPost } from '@/types';
import { useSession } from '@/context/AuthContext';
import useGetPostById from './useGetPostById';
import useToggleSinglePostLikes from './useToggleSinglePostLikes';
import { useDeletePost } from './useDeletePost';

interface UseProfilePostDataProps {
  postId: string;
}

interface UseProfilePostDataReturn {
  post: IPost | null;
  isLiked: boolean;
  isPostOwner: boolean;
  isLoading: boolean;
  error: any;
  handleToggleLike: () => void;
  handlePostActions: (action: string) => void;
  setPost: (update: SetStateAction<IPost>) => void;
}

/**
 * Custom hook to manage profile post data, like status, ownership, and actions
 * Separates data management from UI components for better performance
 */
export const useProfilePostData = ({ postId }: UseProfilePostDataProps): UseProfilePostDataReturn => {
  const { userId } = useSession();
  const { data, isLoading, error } = useGetPostById(postId);
  const [post, setPost] = useState<IPost | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  // Delete post mutation
  const { mutate: deletePost, isSuccess: isDeleteSuccess } = useDeletePost();

  // Safe setter for post updates
  const safeSetPost = useCallback((update: SetStateAction<IPost>) => {
    setPost((prev) =>
      prev ? (update instanceof Function ? update(prev) : update) : null,
    );
  }, []);

  const { handleToggleLike } = useToggleSinglePostLikes(safeSetPost);

  // Memoize post ownership check
  const isPostOwner = useMemo(() => {
    if (!userId || !post) return false;
    return userId === post.postedByUserId._id;
  }, [userId, post?.postedByUserId._id]);

  // Memoize like status check
  const computedIsLiked = useMemo(() => {
    if (!post || !userId) return false;
    return post.likes.userId.includes(userId);
  }, [post?.likes.userId, userId]);

  // Update post data when API response changes
  useEffect(() => {
    if (data?.data) {
      setPost(data.data);
    }
  }, [data?.data]);

  // Update like status when post changes
  useEffect(() => {
    setIsLiked(computedIsLiked);
  }, [computedIsLiked]);

  // Handle delete success navigation
  useEffect(() => {
    if (isDeleteSuccess) {
      router.replace('/(protected)/(tabs)/profile');
    }
  }, [isDeleteSuccess]);

  // Memoized post actions handler
  const handlePostActions = useCallback((action: string) => {
    if (action === 'delete' && post?._id) {
      deletePost(post._id);
    }
  }, [deletePost, post?._id]);

  // Memoized toggle like handler
  const optimizedToggleLike = useCallback(() => {
    handleToggleLike();
  }, [handleToggleLike]);

  return {
    post,
    isLiked,
    isPostOwner,
    isLoading,
    error,
    handleToggleLike: optimizedToggleLike,
    handlePostActions,
    setPost: safeSetPost,
  };
};
