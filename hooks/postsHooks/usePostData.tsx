import { useState, useEffect, useCallback, useMemo, SetStateAction } from 'react';
import { IPost } from '@/types';
import { useSession } from '@/context/AuthContext';
import useGetPostById from './useGetPostById';
import useToggleSinglePostLikes from './useToggleSinglePostLikes';

interface UsePostDataProps {
  postId: string;
}

interface UsePostDataReturn {
  post: IPost | null;
  isLiked: boolean;
  isPostOwner: boolean;
  isLoading: boolean;
  error: any;
  handleToggleLike: () => void;
  setPost: (update: SetStateAction<IPost>) => void;
}

/**
 * Custom hook to manage post data, like status, and ownership logic
 * Separates data management from UI components for better performance
 */
export const usePostData = ({ postId }: UsePostDataProps): UsePostDataReturn => {
  const { user } = useSession();
  const { data, isLoading, error } = useGetPostById(postId);
  const [post, setPost] = useState<IPost | null>(null);
  const [isLiked, setIsLiked] = useState(false);

  // Safe setter for post updates
  const safeSetPost = useCallback((update: SetStateAction<IPost>) => {
    setPost((prev) =>
      prev ? (update instanceof Function ? update(prev) : update) : null,
    );
  }, []);

  const { handleToggleLike } = useToggleSinglePostLikes(safeSetPost);

  // Memoize post ownership check
  const isPostOwner = useMemo(() => {
    if (!user || !post) return false;
    return user._id === post.postedByUserId._id;
  }, [user?._id, post?.postedByUserId._id]);

  // Update post data when API response changes
  useEffect(() => {
    if (data?.data) {
      setPost(data.data);
    }
  }, [data?.data]);

  // Update like status when post or user changes
  useEffect(() => {
    if (post && user?._id) {
      setIsLiked(post.likes.userId.includes(user._id));
    }
  }, [post?.likes.userId, user?._id]);

  // Memoized toggle like handler
  const optimizedToggleLike = useCallback(() => {
    handleToggleLike();
  }, [handleToggleLike]);

  return {
    post,
    isLiked,
    isPostOwner,
    isLoading,
    error,
    handleToggleLike: optimizedToggleLike,
    setPost: safeSetPost,
  };
};
