import { useState, useCallback } from 'react';

interface UsePostModalReturn {
  showModal: boolean;
  toggleModal: () => void;
  openModal: () => void;
  closeModal: () => void;
}

/**
 * Custom hook to manage post modal state
 * Separates modal logic from main component
 */
export const usePostModal = (): UsePostModalReturn => {
  const [showModal, setShowModal] = useState(false);

  const toggleModal = useCallback(() => {
    setShowModal((prev) => !prev);
  }, []);

  const openModal = useCallback(() => {
    setShowModal(true);
  }, []);

  const closeModal = useCallback(() => {
    setShowModal(false);
  }, []);

  return {
    showModal,
    toggleModal,
    openModal,
    closeModal,
  };
};
