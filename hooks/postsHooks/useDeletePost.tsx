import { deletePostAPI } from '@/services/postsApi';
import { handleError } from '@/utils/errorHandler';
import { useMutation } from '@tanstack/react-query';

export const useDeletePost = () => {
  const mutation = useMutation({
    mutationKey: ['delete-post'],
    mutationFn: async (id: string) => deletePostAPI(id),
    onError: (err) => {
      handleError(err);
    },
  });
  return { ...mutation };
};
