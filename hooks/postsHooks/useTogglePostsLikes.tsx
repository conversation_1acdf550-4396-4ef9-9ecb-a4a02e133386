import { useSession } from '@/context/AuthContext';
import { IPost } from '@/types';
import { Dispatch, SetStateAction, useCallback } from 'react';
import useIncrementPostLike from './useIncrementPostLike';
import useDecrementPostLike from './useDecrementPostLike';

const useTogglePostsLikes = (
  setPosts: Dispatch<SetStateAction<IPost[]>>,
  posts: IPost[],
) => {
  const { user } = useSession();
  const { mutate: likePost } = useIncrementPostLike();
  const { mutate: unLikePost } = useDecrementPostLike();

  const isLiked = useCallback(
    (postId: string) => {
      return posts.some(
        (post) =>
          post._id === postId && post.likes.userId.includes(user?._id || ''),
      );
    },
    [posts, user],
  );

  const handleToggleLike = useCallback(
    (postId: string) => {
      setPosts((prev) =>
        prev.map((post) => {
          if (post._id === postId) {
            const alreadyLiked = isLiked(postId);
            return {
              ...post,
              likes: {
                likesCount: alreadyLiked
                  ? post.likes.likesCount - 1
                  : post.likes.likesCount + 1,
                userId: alreadyLiked
                  ? post.likes.userId.filter((id) => id !== user?._id)
                  : [...post.likes.userId, user?._id || ''],
              },
            };
          }
          return post;
        }),
      );

      if (isLiked(postId)) {
        unLikePost(postId);
      } else {
        likePost(postId);
      }
    },
    [posts, user, setPosts, likePost, unLikePost],
  );

  return { handleToggleLike };
};

export default useTogglePostsLikes;
