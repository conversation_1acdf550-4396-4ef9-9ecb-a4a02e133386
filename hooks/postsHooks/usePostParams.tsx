import { useMemo } from 'react';
import { useLocalSearchParams } from 'expo-router';

interface PostParams {
  id: string;
  circleId: string;
  circleImage: string;
  circleName: string;
}

interface UsePostParamsReturn {
  params: PostParams;
  isValidParams: boolean;
}

/**
 * Custom hook to manage and validate post route parameters
 * Memoizes parameters to prevent unnecessary re-computations
 */
export const usePostParams = (): UsePostParamsReturn => {
  const rawParams = useLocalSearchParams<{
    id: string;
    circleId: string;
    circleImage: string;
    circleName: string;
  }>();

  // Memoize processed parameters
  const params = useMemo((): PostParams => ({
    id: rawParams.id || '',
    circleId: rawParams.circleId || '',
    circleImage: rawParams.circleImage || '',
    circleName: rawParams.circleName || '',
  }), [rawParams.id, rawParams.circleId, rawParams.circleImage, rawParams.circleName]);

  // Validate required parameters
  const isValidParams = useMemo(() => {
    return Boolean(params.id);
  }, [params.id]);

  return {
    params,
    isValidParams,
  };
};
