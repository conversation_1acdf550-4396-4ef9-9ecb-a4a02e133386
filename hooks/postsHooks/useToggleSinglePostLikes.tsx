import { useSession } from '@/context/AuthContext';
import { IPost } from '@/types';
import { Dispatch, SetStateAction, useCallback } from 'react';
import useIncrementPostLike from './useIncrementPostLike';
import useDecrementPostLike from './useDecrementPostLike';

const useToggleSinglePostLikes = (setPost: Dispatch<SetStateAction<IPost>>) => {
  const { user } = useSession();
  const { mutate: likePost } = useIncrementPostLike();
  const { mutate: unLikePost } = useDecrementPostLike();

  const handleToggleLike = useCallback(() => {
    setPost((prev) => {
      if (!prev) return prev; // <- prevent crash if post is undefined

      const alreadyLiked = prev.likes.userId.includes(user?._id || '');

      const updatedPost = {
        ...prev,
        likes: {
          likesCount: alreadyLiked
            ? prev.likes.likesCount - 1
            : prev.likes.likesCount + 1,
          userId: alreadyLiked
            ? prev.likes.userId.filter((id) => id !== user?._id)
            : [...prev.likes.userId, user?._id || ''],
        },
      };

      if (alreadyLiked) {
        unLikePost(prev._id);
      } else {
        likePost(prev._id);
      }

      return updatedPost;
    });
  }, [setPost, user, likePost, unLikePost]);

  return { handleToggleLike };
};

export default useToggleSinglePostLikes;
