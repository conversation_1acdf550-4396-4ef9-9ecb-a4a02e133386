import { useState, useEffect, useCallback } from 'react';
import * as Location from 'expo-location';

type LocationDetails = {
  latitude: number;
  longitude: number;
  country: string | null;
  city: string | null;
  region: string | null;
  fullAddress: string | undefined;
};

const useLocation = () => {
  const [location, setLocation] = useState<LocationDetails | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [permission, setPermission] = useState(false);
  const getLocationName = useCallback(async (lat: number, lng: number) => {
    try {
      const reverseGeocode = await Location.reverseGeocodeAsync({
        latitude: lat,
        longitude: lng,
      });

      if (reverseGeocode.length > 0) {
        const address = reverseGeocode[0];
        return {
          country: address.country,
          city: address.city || address.region,
          region: address.region,
          fullAddress: `${address.city}, ${address.country}`,
        };
      }
      return null;
    } catch (err) {
      setError('Error getting location name');
      return null;
    }
  }, []);

  const getCurrentLocation = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      // Request permission
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status !== 'granted') {
        setError('Permission to access location was denied');
        setPermission(false);
        return;
      }
      setPermission(true);
      // Get current position
      const position = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = position.coords;

      // Get location name
      const locationName = await getLocationName(latitude, longitude);

      setLocation({
        latitude,
        longitude,
        country: locationName?.country ?? null,
        city: locationName?.city ?? null,
        region: locationName?.region ?? null,
        fullAddress: locationName?.fullAddress,
      });
    } catch (err: any) {
      setError('Error getting location: ' + err.message);
    } finally {
      setLoading(false);
    }
  }, [getLocationName]);

  useEffect(() => {
    getCurrentLocation();
  }, [getCurrentLocation]);

  return {
    location,
    loading,
    error,
    refresh: getCurrentLocation,
    getLocationName,
    permission,
  };
};

export default useLocation;
