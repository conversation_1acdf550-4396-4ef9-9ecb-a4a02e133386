import { useRef, useCallback, useEffect, useState } from 'react';
import {
  Camera,
  useCameraDevices,
  useCameraPermission,
  useMicrophonePermission,
} from 'react-native-vision-camera';

const useCamera = () => {
  const { hasPermission, requestPermission } = useCameraPermission();
  const {
    hasPermission: micHasPermission,
    requestPermission: requestMicPermission,
  } = useMicrophonePermission();
  const devices = useCameraDevices();
  const [facing, setFacing] = useState<'back' | 'front'>('back');
  const device = devices.find((d) => d.position === facing);

  const cameraRef = useRef<Camera>(null);
  const recordingRef = useRef<{ stop: () => void } | null>(null);

  const toggleCameraFacing = useCallback(() => {
    setFacing((prev) => (prev === 'back' ? 'front' : 'back'));
  }, []);

  const takePhoto = async (): Promise<string | null> => {
    if (!cameraRef.current) return null;
    try {
      const photo = await cameraRef.current.takePhoto();
      return photo.path;
    } catch (error) {
      console.error('Error capturing photo:', error);
      return null;
    }
  };

  const startRecording = async (): Promise<string | null> => {
    if (!cameraRef.current) return null;
    try {
      return new Promise((resolve, reject) => {
        cameraRef.current?.startRecording({
          onRecordingFinished: (video) => resolve(video.path),
          onRecordingError: (error) => {
            console.error('Recording error:', error);
            reject(error);
          },
        });
        recordingRef.current = {
          stop: () => cameraRef.current?.stopRecording(),
        };
      });
    } catch (error) {
      console.error('Error recording video:', error);
      return null;
    }
  };

  const stopRecording = () => {
    recordingRef.current?.stop();
    recordingRef.current = null;
  };

  useEffect(() => {
    if (!micHasPermission) {
      requestMicPermission();
    }
    if (!hasPermission) requestPermission();
  }, [hasPermission, micHasPermission]);

  return {
    cameraRef,
    device,
    devices,
    takePhoto,
    startRecording,
    stopRecording,
    hasPermission,
    requestPermission,
    toggleCameraFacing,
  };
};

export default useCamera;
