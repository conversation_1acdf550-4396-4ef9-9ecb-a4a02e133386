import {
  getCategoriesAPI,
  IGetCategoriesAResponse,
} from '@/services/HomeScreenAPI';
import { useQuery } from '@tanstack/react-query';
import { CACHE_TIME } from '@/utils/queryUtils';

/**
 * Hook to fetch all categories
 * @returns Query object with categories data
 */
const useCategoriesQuery = () => {
  return useQuery<IGetCategoriesAResponse>({
    queryKey: ['categories'],
    queryFn: getCategoriesAPI,
    staleTime: CACHE_TIME.RARE, // 10 minutes from queryUtils
  });
};

export default useCategoriesQuery;
