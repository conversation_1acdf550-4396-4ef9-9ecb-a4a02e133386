import {
  getMediaByUserIdAPI,
  IGetMediaByUserIdResponse,
} from '@/services/profileAPI';
import {
  useInfiniteQuery,
  UseInfiniteQueryOptions,
} from '@tanstack/react-query';

type MediaQueryParams = {
  userId: string;
  limit?: number;
  page?: number;
};

const useGetMediaByUserId = (
  { userId, limit = 10, page = 1 }: MediaQueryParams,
  options?: Omit<
    UseInfiniteQueryOptions<
      IGetMediaByUserIdResponse,
      unknown,
      IGetMediaByUserIdResponse,
      IGetMediaByUserIdResponse
    >,
    'queryKey' | 'queryFn' | 'getNextPageParam'
  >,
) => {
  return useInfiniteQuery({
    queryKey: ['get-user-media', userId, limit, page],
    queryFn: ({ pageParam = 1 }) =>
      getMediaByUserIdAPI({ userId, limit, page: (pageParam || 1) as number }),
    initialPageParam: 1,
    getNextPageParam: (params) => {
      if (params.currentPage >= params.totalPages) return undefined;
      return params.currentPage + 1;
    },
    enabled: !!userId,
    ...options,
  });
};

export default useGetMediaByUserId;
