import { GetPostsByUserIdAPI } from '@/services/profileAPI';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

const useGetPostsByUserID = (
  userId: string,
  options?: Omit<
    UseQueryOptions<any, unknown, any, any>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useQuery({
    queryKey: ['get-user-posts-by-ID', userId],
    queryFn: () => GetPostsByUserIdAPI(userId),
    enabled: !!userId, // Prevents query execution if `userId` is undefined or empty
    ...options, // Spread options to allow overrides
  });
};

export default useGetPostsByUserID;
