import { getUserLikesAPI } from '@/services/profileAPI';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

const useGetUserLikes = (
  userId: string,
  options?: Omit<
    UseQueryOptions<any, unknown, any, any>,
    'queryKey' | 'queryFn'
  >,
) => {
  return useQuery({
    queryKey: ['get-user-Likes'],
    queryFn: async () => await getUserLikesAPI(userId),
    ...options, // Spread options to allow partial overrides
  });
};

export default useGetUserLikes;
