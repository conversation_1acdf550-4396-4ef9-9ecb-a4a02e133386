import { getAllPostsAPI } from '@/services/profileAPI';
import {
  useInfiniteQuery,
  UseInfiniteQueryOptions,
  UseInfiniteQueryResult,
} from '@tanstack/react-query';
import { IPost } from '@/types';

type GetPostsResponse = {
  success: boolean;
  message: string;
  total: number;
  currentPage: number;
  totalPages: number;
  pages?: [{ data: IPost[] }];
  data: IPost[];
};

const useGetPosts = (
  limit = 10,
  options?: Omit<
    UseInfiniteQueryOptions<
      GetPostsResponse, // data type returned by API
      Error, // error type
      GetPostsResponse, // select type
      readonly [string, number], // snapshot type
      readonly [string, number] // queryKey type
    >,
    'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'
  >,
): UseInfiniteQueryResult<GetPostsResponse> => {
  return useInfiniteQuery<
    GetPostsResponse,
    Error,
    GetPostsResponse,
    GetPostsResponse,
    readonly [string, number]
  >({
    queryKey: ['get-user-posts', limit],
    queryFn: async ({ pageParam = 1 }: { pageParam: number }) =>
      getAllPostsAPI(pageParam, limit),
    getNextPageParam: (lastPage) =>
      lastPage.currentPage < lastPage.totalPages
        ? lastPage.currentPage + 1
        : undefined,
    initialPageParam: 1, //  required now in v5
    ...options,
  });
};

export default useGetPosts;
