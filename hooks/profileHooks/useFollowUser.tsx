import { followUserAPI } from '@/services/profileAPI';
import { useMutation } from '@tanstack/react-query';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';

const useFollowUser = (userToFollow: string) => {
  const mutation = useMutation({
    mutationKey: ['user-to-follow', userToFollow],
    mutationFn: async () => await followUserAPI(userToFollow),
    onSuccess: () => {
      customToast('user had followed successfully', 'success');
    },
    onError: (err) => {
      handleError(err);
    },
  });

  return {};
};

export default useFollowUser;
