import { useEffect, useRef, useCallback } from 'react';
import { InteractionManager, AppState } from 'react-native';

interface PerformanceMetrics {
  renderTime: number;
  interactionTime: number;
  memoryUsage?: number;
}

interface UsePerformanceMonitorOptions {
  componentName: string;
  logSlowRenders?: boolean;
  slowRenderThreshold?: number;
  enableMemoryTracking?: boolean;
}

/**
 * Hook for monitoring component performance
 * Tracks render times, interaction delays, and memory usage
 */
export const usePerformanceMonitor = ({
  componentName,
  logSlowRenders = true,
  slowRenderThreshold = 16, // 60fps = 16.67ms per frame
  enableMemoryTracking = false,
}: UsePerformanceMonitorOptions) => {
  const renderStartTime = useRef<number>(0);
  const mountTime = useRef<number>(0);
  const interactionStartTime = useRef<number>(0);

  // Track component mount time
  useEffect(() => {
    mountTime.current = performance.now();
    
    return () => {
      const unmountTime = performance.now();
      const totalLifetime = unmountTime - mountTime.current;
      
      if (totalLifetime > 1000) { // Log components that live longer than 1s
        console.log(`${componentName} lifetime: ${totalLifetime.toFixed(2)}ms`);
      }
    };
  }, [componentName]);

  // Track render performance
  const startRenderTracking = useCallback(() => {
    renderStartTime.current = performance.now();
  }, []);

  const endRenderTracking = useCallback(() => {
    const renderTime = performance.now() - renderStartTime.current;
    
    if (logSlowRenders && renderTime > slowRenderThreshold) {
      console.warn(
        `Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`
      );
    }
    
    return renderTime;
  }, [componentName, logSlowRenders, slowRenderThreshold]);

  // Track interaction performance
  const trackInteraction = useCallback((interactionName: string) => {
    interactionStartTime.current = performance.now();
    
    return () => {
      const interactionTime = performance.now() - interactionStartTime.current;
      
      if (interactionTime > 100) { // Log slow interactions (>100ms)
        console.warn(
          `Slow interaction in ${componentName}.${interactionName}: ${interactionTime.toFixed(2)}ms`
        );
      }
      
      return interactionTime;
    };
  }, [componentName]);

  // Track memory usage (if enabled)
  const trackMemoryUsage = useCallback(() => {
    if (!enableMemoryTracking) return null;
    
    // Note: React Native doesn't have direct memory API
    // This would need native module implementation
    // For now, we'll return a placeholder
    return {
      jsHeapSizeUsed: 0,
      jsHeapSizeTotal: 0,
      jsHeapSizeLimit: 0,
    };
  }, [enableMemoryTracking]);

  // Performance measurement utilities
  const measureAsync = useCallback(async <T,>(
    operation: () => Promise<T>,
    operationName: string
  ): Promise<{ result: T; duration: number }> => {
    const startTime = performance.now();
    
    try {
      const result = await operation();
      const duration = performance.now() - startTime;
      
      if (duration > 1000) { // Log slow async operations
        console.warn(
          `Slow async operation in ${componentName}.${operationName}: ${duration.toFixed(2)}ms`
        );
      }
      
      return { result, duration };
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `Failed async operation in ${componentName}.${operationName}: ${duration.toFixed(2)}ms`,
        error
      );
      throw error;
    }
  }, [componentName]);

  const measureSync = useCallback(<T,>(
    operation: () => T,
    operationName: string
  ): { result: T; duration: number } => {
    const startTime = performance.now();
    
    try {
      const result = operation();
      const duration = performance.now() - startTime;
      
      if (duration > 16) { // Log operations that might block the UI thread
        console.warn(
          `Potentially blocking operation in ${componentName}.${operationName}: ${duration.toFixed(2)}ms`
        );
      }
      
      return { result, duration };
    } catch (error) {
      const duration = performance.now() - startTime;
      console.error(
        `Failed sync operation in ${componentName}.${operationName}: ${duration.toFixed(2)}ms`,
        error
      );
      throw error;
    }
  }, [componentName]);

  // Interaction Manager utilities
  const runAfterInteractions = useCallback((callback: () => void) => {
    InteractionManager.runAfterInteractions(() => {
      const startTime = performance.now();
      callback();
      const duration = performance.now() - startTime;
      
      if (duration > 16) {
        console.warn(
          `Long-running post-interaction callback in ${componentName}: ${duration.toFixed(2)}ms`
        );
      }
    });
  }, [componentName]);

  return {
    startRenderTracking,
    endRenderTracking,
    trackInteraction,
    trackMemoryUsage,
    measureAsync,
    measureSync,
    runAfterInteractions,
  };
};
