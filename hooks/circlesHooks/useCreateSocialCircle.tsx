import { createSocialCircleAPI } from '@/services/SocialCirclesAPI';
import { handleError } from '@/utils/errorHandler';
import { zodResolver } from '@hookform/resolvers/zod';
import { useMutation } from '@tanstack/react-query';
import { router } from 'expo-router';
import { useForm } from 'react-hook-form';
import { customToast } from '../useCustomToast';
import useImagePicker from '../useImagePicker';
import { changeAssetToFormData } from '@/utils/ImageAssetToFormData';
import { useCallback, useEffect } from 'react';
import { z } from 'zod';

export const createSocialCircleSchema = z.object({
  name: z.string().nonempty({ message: 'must provide circle name' }),
  image: z.string().nonempty({ message: 'Image is required' }),
  members: z.array(z.string()).default([]),
});

const useCreateSocialCircle = () => {
  const hookForm = useForm({
    resolver: zodResolver(createSocialCircleSchema),
    defaultValues: {
      name: '',
      image: '',
      members: [],
    },
  });

  const { pickImage, result, setResult } = useImagePicker({
    imagePickOptions: { mediaTypes: ['images'], selectionLimit: 1 },
  });

  // Update image field when result changes
  useEffect(() => {
    if (result.length > 0) {
      hookForm.setValue('image', result[0].uri);
      hookForm.clearErrors('image');
    }
  }, [result, hookForm.setValue, hookForm.clearErrors]);

  const handleImagePick = async () => {
    await pickImage();
  };

  const handleAddMembers = useCallback(
    (_id: string) => {
      const newMembers = hookForm.getValues('members') || [];
      const updatedMembers = newMembers.includes(_id)
        ? newMembers.filter((v) => v !== _id)
        : [...newMembers, _id];
      hookForm.setValue('members', updatedMembers);
    },
    [hookForm],
  );

  const mutation = useMutation({
    mutationKey: ['create-socialCircle'],
    mutationFn: async (data: {
      name: string;
      image: string;
      members: string[];
    }) => {
      if (!result.length) {
        throw new Error('Image is required');
      }

      const formData = new FormData();
      formData.append('name', data.name);
      formData.append(
        'description',
        'Description must be optional based on design',
      );
      data.members.forEach((member) => formData.append('members', member));
      changeAssetToFormData(formData, 'image', result[0]);

      return await createSocialCircleAPI(formData);
    },
    onSuccess: () => {
      customToast('Circle Created', 'success');
      router.push('/(protected)/(tabs)/socialCircles');
      hookForm.reset({ name: '', image: '', members: [] }); // Reset with default values
      setResult([]); // Clear selected image
    },
    onError: (error: any) => {
      handleError(error);
    },
  });

  const onSubmit = hookForm.handleSubmit((data) => mutation.mutate(data));

  return {
    ...hookForm,
    pickImage,
    result,
    setResult,
    handleImagePick,
    handleAddMembers,
    onSubmit,
    ...mutation,
  };
};

export default useCreateSocialCircle;
