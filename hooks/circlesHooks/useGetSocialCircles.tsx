import { useQuery } from '@tanstack/react-query';
import { getSocialCircleByUserID } from '@/services/SocialCirclesAPI';

const useGetSocialCircles = (userId: string) => {
  const query = useQuery({
    queryKey: ['get user social Circles', userId],
    queryFn: async () => await getSocialCircleByUserID(userId || ''),
    enabled: !!userId,
  });

  return { ...query };
};

export default useGetSocialCircles;
