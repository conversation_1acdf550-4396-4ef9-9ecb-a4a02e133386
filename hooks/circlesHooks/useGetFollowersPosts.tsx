// hooks/useGetFollowersPosts.ts
import { getFollowersPostsAPI } from '@/services/profileAPI';
import { useInfiniteQuery } from '@tanstack/react-query';

interface PaginatedResponse {
  data: any[];
  pagination: {
    page: number;
    limit: number;
  };
}

export const useGetFollowersPosts = (limit = 10) => {
  return useInfiniteQuery<PaginatedResponse>({
    queryKey: ['followersPosts'],
    initialPageParam: 1,
    queryFn: ({ pageParam = 1 }) =>
      getFollowersPostsAPI({ page: pageParam as number, limit }),
    getNextPageParam: (lastPage) => {
      const currentPage = lastPage.pagination.page;
      const totalFetched = lastPage.data.length;
      const pageSize = lastPage.pagination.limit;

      // Fetch next page only if current page was full
      return totalFetched === pageSize ? currentPage + 1 : undefined;
    },
  });
};
