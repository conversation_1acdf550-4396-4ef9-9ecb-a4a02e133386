import { getPostsBySocialCirclesIdAPI } from '@/services/SocialCirclesAPI';
import { useQuery } from '@tanstack/react-query';

export const useGetPostsBySocialCircleId = (id?: string, enabled = true) => {
  const query = useQuery({
    queryKey: ['get-post-by-circle-id', id],
    queryFn: async () => getPostsBySocialCirclesIdAPI(id || ''),
    enabled: !!id && enabled, // combine internal and external control
  });

  return { ...query };
};
