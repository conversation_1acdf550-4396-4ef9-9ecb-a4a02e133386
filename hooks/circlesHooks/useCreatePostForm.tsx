/// <reference lib="dom" />
import { ISocialCircle } from '@/types';
import { useMutation } from '@tanstack/react-query';
import { useForm } from 'react-hook-form';
import useImagePicker from '../useImagePicker';
import { ImagePickerAsset } from 'expo-image-picker';
import { customToast } from '../useCustomToast';
import { handleError } from '@/utils/errorHandler';
import { changeAssetToFormData } from '@/utils/ImageAssetToFormData';
import { router } from 'expo-router';
import { CreatePostAPI } from '@/services/postsApi';

interface IFormData {
  socialCircle: ISocialCircle | null;
  image: ImagePickerAsset[];
  tags: string;
  content: string;
}

const useCreatePostForm = () => {
  const hookForm = useForm({
    defaultValues: {
      postType: 'Public' as 'Public' | 'SocialCircles',
      content: '',
      tags: '' as string, // Ensure this is a string, not null
      image: [] as ImagePickerAsset[],
      socialCircle: null as ISocialCircle | null,
    },
  });

  const { pickImage, result, setResult } = useImagePicker({
    imagePickOptions: {
      allowsEditing: false,
      allowsMultipleSelection: true,
      videoMaxDuration: 30,
      selectionLimit: 4,
    },
  });

  const createFormData = (data: IFormData) => {
    const formData = new FormData();
    formData.append('content', data.content);
    formData.append('tags', data.tags || ''); // Ensure tags are always a string

    result?.forEach((img) => {
      changeAssetToFormData(formData, 'image', img);
    });

    if (data.socialCircle) {
      formData.append('socialCircle', data.socialCircle?._id);
    }

    return formData;
  };

  const mutation = useMutation({
    mutationKey: ['create-social-circles-post'],
    mutationFn: async (data: IFormData) => {
      const formData = createFormData(data);
      return CreatePostAPI(formData);
    },
    onSuccess: () => {
      customToast('Post created successfully', 'success');
      router.replace('/(protected)/(tabs)/socialCircles');
      hookForm.reset();
      setResult([]);
    },
    onError: (err) => {
      handleError(err);
    },
  });
  const onSubmit = hookForm.handleSubmit((data) => mutation.mutate(data));

  return { ...hookForm, ...mutation, result, setResult, pickImage, onSubmit };
};

export default useCreatePostForm;
