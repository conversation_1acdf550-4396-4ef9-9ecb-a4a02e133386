import { changeAssetToFormData } from '@/utils/ImageAssetToFormData';
import { zodResolver } from '@hookform/resolvers/zod';
import { useForm } from 'react-hook-form';
import { z } from 'zod';
import { ImagePickerAsset } from 'expo-image-picker';
import { useMutation } from '@tanstack/react-query';
import { UpdateSocialCircleAPI } from '@/services/SocialCirclesAPI';

import { useCallback, useEffect } from 'react';
import { customToast } from '../useCustomToast';
import useImagePicker from '../useImagePicker';

const imageFileSchema = z.object({
  uri: z.string().min(1, { message: 'Image URI is required' }),
  name: z.string().min(1, { message: 'Image name is required' }),
  type: z.string().optional(), // Optional type for the image
});

const validationSchema = z.object({
  name: z
    .string()
    .min(3, { message: 'Circle name should have at least 3 characters' }),
  image: imageFileSchema.nullable(), // Nullable for cases where image isn't updated
  description: z.string().optional(),
  members: z.array(z.string()).default([]),
});

type IForm = z.infer<typeof validationSchema>;

const useUpdateSocialCircle = (id: string, title?: string, image?: string) => {
  const {
    control,
    handleSubmit,
    setValue,
    watch,
    getValues,
    formState: { errors, isSubmitting },
  } = useForm<IForm>({
    resolver: zodResolver(validationSchema),
    defaultValues: {
      name: title || '',
      image: image
        ? { name: Date.now().toString(), type: '', uri: image }
        : null,
      members: [],
    },
  });

  const mutation = useMutation({
    mutationKey: ['updateSocialCircle', id],
    mutationFn: async (payload: FormData) =>
      await UpdateSocialCircleAPI({ _id: id, payload }),
    onSuccess: () => {
      customToast('Circle updated successfully', 'success');
    },
    onError: (err) => {
      customToast(err.message, 'error');
    },
  });

  const handleAddMembers = useCallback(
    (_id: string) => {
      const newMembers = getValues('members') || [];
      const updatedMembers = newMembers.includes(_id)
        ? newMembers.filter((v) => v !== _id)
        : [...newMembers, _id];

      setValue('members', updatedMembers);
    },
    [getValues, setValue],
  );

  const { pickImage, result, setResult } = useImagePicker({
    imagePickOptions: {
      mediaTypes: ['images'],
      allowsMultipleSelection: false,
    },
  });

  const handleDeleteImage = () => {
    setValue('image', null);
    setResult([]);
  };

  const GenerateFormData = (data: IForm) => {
    const formData = new FormData();
    formData.append('name', data.name);
    formData.append('description', data.description || '');

    if (data.members.length > 0) {
      data.members.forEach((member) => {
        formData.append('members[]', member);
      });
    }

    if (data.image?.uri) {
      changeAssetToFormData(formData, 'image', {
        ...data.image,
        width: result?.[0]?.width ?? 0,
        height: result?.[0]?.height ?? 0,
        type: 'image',
      });
    }

    return formData;
  };

  const onSubmit = handleSubmit((data) => {
    const formData = GenerateFormData(data);
    if (!formData) {
      customToast('No image selected', 'error');
      return;
    }
    mutation.mutate(formData);
  });

  useEffect(() => {
    if (image && !watch('image')?.uri) {
      setValue('image', { name: Date.now().toString(), type: '', uri: image });
    }

    if (title && watch('name') !== title) {
      setValue('name', title);
    }
  }, [image, title, setValue, watch]);

  useEffect(() => {
    if (result?.[0]) {
      const img: ImagePickerAsset = result[0];
      setValue('image', {
        uri: img.uri,
        name: img.fileName ?? `${new Date().getTime()}.jpeg`,
        type: img.mimeType ?? 'image/jpeg',
      });
    }
  }, [result, setValue]);

  return {
    control,
    onSubmit,
    isSubmitting: mutation.isPending || isSubmitting,
    setValue,
    watch,
    errors,
    handleDeleteImage,
    pickImage,
    GenerateFormData,
    handleAddMembers,
    ...mutation,
  };
};

export default useUpdateSocialCircle;
