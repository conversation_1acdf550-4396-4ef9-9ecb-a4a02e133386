import { DeleteSocialCircleAPI } from '@/services/SocialCirclesAPI';
import { useMutation } from '@tanstack/react-query';
import { router } from 'expo-router';
import { customToast } from '../useCustomToast';

const useDeleteSocialCircle = () => {
  const mutation = useMutation({
    mutationKey: ['delete-socialCircle'],
    mutationFn: async (id: string) => await DeleteSocialCircleAPI(id),
    onSuccess: () => {
      router.replace('/(protected)/socialCircles/seeAll');
    },
    onError: (err) => {
      customToast(err.message, 'error');
    },
  });
  return { ...mutation };
};

export default useDeleteSocialCircle;
