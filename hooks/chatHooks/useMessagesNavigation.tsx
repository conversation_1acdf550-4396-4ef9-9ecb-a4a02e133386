import { useCallback } from 'react';
import { router } from 'expo-router';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

interface UseMessagesNavigationReturn {
  navigateToCreateGroup: () => void;
  navigateToChat: (chatId: string, chatData: any) => void;
}

/**
 * Custom hook to manage navigation logic for messages screen
 * Separates navigation concerns and provides performance tracking
 */
export const useMessagesNavigation = (): UseMessagesNavigationReturn => {
  // Performance monitoring
  const { trackInteraction } = usePerformanceMonitor({
    componentName: 'useMessagesNavigation',
    logSlowRenders: true,
  });

  // Memoized navigation to create group
  const navigateToCreateGroup = useCallback(() => {
    const endTracking = trackInteraction('navigateToCreateGroup');
    
    router.push('/(protected)/messages/createGroupRoom');
    
    endTracking();
  }, [trackInteraction]);

  // Memoized navigation to chat
  const navigateToChat = useCallback((chatId: string, chatData: any) => {
    const endTracking = trackInteraction('navigateToChat');
    
    router.push({
      pathname: '/(protected)/messages/[id]',
      params: {
        id: chatId,
        username: chatData.username,
        displayName: chatData.displayName || chatData.username,
        image: chatData.profile_picture,
        isOnline: chatData.is_online?.toString() || 'false',
      },
    });
    
    endTracking();
  }, [trackInteraction]);

  return {
    navigateToCreateGroup,
    navigateToChat,
  };
};
