import {
  IGroupMessagePayload,
  sendGroupChatMessageAPI,
} from '@/services/ChatAPI';
import { useMutation } from '@tanstack/react-query';

const useSendGroupMessage = () => {
  const mutate = useMutation({
    mutationKey: ['send-group-message'],
    mutationFn: async (payload: IGroupMessagePayload) =>
      sendGroupChatMessageAPI(payload),
  });
  return { ...mutate };
};

export default useSendGroupMessage;
