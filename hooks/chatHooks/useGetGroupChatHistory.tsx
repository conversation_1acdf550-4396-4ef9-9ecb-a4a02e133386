import {
  GroupChatHistoryParams,
  GroupChatHistoryResponse,
  getGroupChatMessagesAPI,
} from '@/services/ChatAPI';
import { useInfiniteQuery } from '@tanstack/react-query';

interface UseGetGroupChatHistoryParams extends GroupChatHistoryParams {
  enabled?: boolean;
}

const useGetGroupChatHistory = ({
  groupId,
  limit = 20,
  enabled = true,
}: UseGetGroupChatHistoryParams) => {
  const query = useInfiniteQuery<GroupChatHistoryResponse>({
    queryKey: ['get-group-chat-history', groupId, limit],
    queryFn: async ({ pageParam = 1 }) => {
      try {
        return await getGroupChatMessagesAPI({ groupId, page: pageParam as number, limit });
      } catch (error) {
        console.warn('Group chat history API not available, returning empty data:', error);
        // Return empty data structure if API doesn't exist yet
        return {
          success: true,
          message: 'No messages',
          limit: limit || 20,
          page: pageParam as number,
          totalPages: 1,
          data: [],
        };
      }
    },
    getNextPageParam: (lastPage) =>
      lastPage.page < lastPage.totalPages ? lastPage.page + 1 : undefined,
    initialPageParam: 1,
    enabled: enabled && !!groupId,
    retry: false, // Don't retry if API doesn't exist
  });

  return query;
};

export default useGetGroupChatHistory;
