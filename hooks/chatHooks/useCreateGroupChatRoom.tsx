import { createChatRoomPayload, createGroupChatAPI } from '@/services/ChatAPI';
import { useMutation } from '@tanstack/react-query';

const useCreateGroupChatRoom = () => {
  const mutation = useMutation({
    mutationKey: ['create-room'],
    mutationFn: async (payload: createChatRoomPayload) =>
      createGroupChatAPI(payload),
  });
  return { ...mutation };
};

export default useCreateGroupChatRoom;
