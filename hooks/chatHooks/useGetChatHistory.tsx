import {
  ChatHistoryParams,
  ChatHistoryResponse,
  getChatMessagesAPI,
} from '@/services/ChatAPI';
import { useInfiniteQuery } from '@tanstack/react-query';

interface UseGetChatHistoryParams extends ChatHistoryParams {
  enabled?: boolean;
}

const useGetChatHistory = ({ recipientId, limit = 20, enabled = true }: UseGetChatHistoryParams) => {
  const query = useInfiniteQuery<ChatHistoryResponse>({
    queryKey: ['get-chat-history', recipientId, limit],
    queryFn: async ({ pageParam = 1 }) =>
      getChatMessagesAPI({ recipientId, page: pageParam as number, limit }),
    getNextPageParam: (lastPage) =>
      lastPage.page < lastPage.totalPages ? lastPage.page + 1 : undefined,
    initialPageParam: 1,
    enabled: enabled && !!recipientId,
  });

  return query;
};

export default useGetChatHistory;
