import { useMemo, useCallback } from 'react';
import { useMessagesStore } from '@/store/useMessagesStore';
import useGetAllChats from './useGetALLMessages';
import { usePerformanceMonitor } from '@/hooks/usePerformanceMonitor';

interface UseMessagesDataReturn {
  chats: any[];
  isLoading: boolean;
  error: any;
  searchQuery: string;
  filteredChats: any[];
  hasChats: boolean;
  refreshChats: () => void;
}

/**
 * Custom hook to manage messages data, filtering, and search logic
 * Separates data management from UI components for better performance
 */
export const useMessagesData = (): UseMessagesDataReturn => {
  const { data, isLoading, error, refetch } = useGetAllChats();
  const { searchQuery } = useMessagesStore();

  // Performance monitoring
  const { measureSync } = usePerformanceMonitor({
    componentName: 'useMessagesData',
    logSlowRenders: true,
  });

  // Memoize raw chats data
  const chats = useMemo(() => {
    return data?.directChats || [];
  }, [data?.directChats]);

  // Memoize filtered chats with performance tracking
  const filteredChats = useMemo(() => {
    return measureSync(() => {
      if (!searchQuery.trim()) {
        return chats;
      }

      const query = searchQuery.toLowerCase().trim();
      return chats.filter((item) => {
        const username = item.username?.toLowerCase() || '';
        const displayName = item.displayName?.toLowerCase() || '';
        const lastMessage = item.last_message?.content?.toLowerCase() || '';
        
        return (
          username.includes(query) ||
          displayName.includes(query) ||
          lastMessage.includes(query)
        );
      });
    }, 'filterChats').result;
  }, [chats, searchQuery, measureSync]);

  // Memoize computed values
  const hasChats = useMemo(() => chats.length > 0, [chats.length]);

  // Memoized refresh handler
  const refreshChats = useCallback(() => {
    refetch();
  }, [refetch]);

  return {
    chats,
    isLoading,
    error,
    searchQuery,
    filteredChats,
    hasChats,
    refreshChats,
  };
};
