import { useEffect, useRef } from 'react';
import { Platform } from 'react-native';
import { measurePerformance } from '@/utils/performanceUtils';

interface TabNavigationMetrics {
  tabName: string;
  switchTime: number;
  platform: string;
  timestamp: number;
}

/**
 * Hook to monitor tab navigation performance specifically on Android
 * Tracks tab switch times and logs slow transitions
 */
export const useTabNavigationPerformance = () => {
  const lastTabSwitchTime = useRef<number>(0);
  const currentTab = useRef<string>('');

  const trackTabSwitch = (tabName: string) => {
    const startTime = performance.now();
    
    // If this is not the first tab switch, measure the time since last switch
    if (lastTabSwitchTime.current > 0 && currentTab.current !== tabName) {
      const switchDuration = startTime - lastTabSwitchTime.current;
      
      const metrics: TabNavigationMetrics = {
        tabName,
        switchTime: switchDuration,
        platform: Platform.OS,
        timestamp: Date.now(),
      };

      // Log performance data
      measurePerformance.sync(
        () => metrics,
        `Tab Switch: ${currentTab.current} -> ${tabName}`,
        'navigation'
      );

      // Log warning for slow tab switches on Android
      if (Platform.OS === 'android' && switchDuration > 300) {
        console.warn(
          `🐌 Slow tab switch detected on Android: ${currentTab.current} -> ${tabName} took ${switchDuration.toFixed(2)}ms`
        );
      }
    }

    lastTabSwitchTime.current = startTime;
    currentTab.current = tabName;
  };

  const getPerformanceReport = () => {
    return {
      platform: Platform.OS,
      currentTab: currentTab.current,
      lastSwitchTime: lastTabSwitchTime.current,
    };
  };

  return {
    trackTabSwitch,
    getPerformanceReport,
  };
};

/**
 * Hook to optimize tab screen rendering
 * Provides utilities for lazy loading and performance optimization
 */
export const useTabScreenOptimization = (screenName: string) => {
  const renderStartTime = useRef<number>(0);
  const isFirstRender = useRef<boolean>(true);

  useEffect(() => {
    if (isFirstRender.current) {
      renderStartTime.current = performance.now();
      isFirstRender.current = false;
    }
  }, []);

  const markScreenReady = () => {
    if (renderStartTime.current > 0) {
      const renderTime = performance.now() - renderStartTime.current;
      
      measurePerformance.sync(
        () => ({ screenName, renderTime }),
        `Screen Render: ${screenName}`,
        'render'
      );

      // Log slow screen renders on Android
      if (Platform.OS === 'android' && renderTime > 500) {
        console.warn(
          `🐌 Slow screen render on Android: ${screenName} took ${renderTime.toFixed(2)}ms`
        );
      }

      renderStartTime.current = 0;
    }
  };

  return {
    markScreenReady,
    isFirstRender: isFirstRender.current,
  };
};

/**
 * Android-specific optimizations for tab navigation
 */
export const useAndroidTabOptimizations = () => {
  useEffect(() => {
    if (Platform.OS === 'android') {
      // Enable hardware acceleration for better performance
      console.log('🚀 Android tab optimizations enabled');
    }
  }, []);

  const optimizeForAndroid = {
    // Reduce animation duration for faster perceived performance
    animationConfig: Platform.OS === 'android' ? {
      duration: 200, // Reduced from default 300ms
      useNativeDriver: true,
    } : undefined,

    // Memory optimization settings
    memoryOptimization: Platform.OS === 'android' ? {
      removeClippedSubviews: true,
      maxToRenderPerBatch: 5,
      windowSize: 10,
    } : {},

    // Touch optimization
    touchOptimization: Platform.OS === 'android' ? {
      activeOpacity: 0.7,
      delayPressIn: 0,
      delayPressOut: 0,
    } : {},
  };

  return optimizeForAndroid;
};
