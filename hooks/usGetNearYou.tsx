import { getNearYouAPI } from '@/services/HomeScreenAPI';
import { useQuery } from '@tanstack/react-query';
import useLocation from './useLocation';

type IUseGetNearYou = {
  page?: number;
  latitude?: number;
  longitude?: number;
  limit?: number;
  radius?: number;
};

const useGetNearYou = ({
  page = 1,
  latitude,
  longitude,
  limit = 3,
  radius,
}: IUseGetNearYou) => {
  const { location } = useLocation();
  const query = useQuery({
    queryKey: ['getNearYou', [location?.latitude, location?.longitude]],
    queryFn: async () => {
      if (!location) return;
      return await getNearYouAPI({
        page: page,
        latitude: (latitude && latitude) || location?.latitude,
        longitude: (longitude && longitude) || location?.longitude,
        limit: limit,
        radius: radius,
      });
    },
    enabled: !!location,
  });

  return { ...query };
};

export default useGetNearYou;
