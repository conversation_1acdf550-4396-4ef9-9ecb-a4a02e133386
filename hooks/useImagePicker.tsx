import * as ImagePicker from 'expo-image-picker';
import { useState } from 'react';
import { Platform } from 'react-native';

interface IUseUploadFromMediaLibrary {
  imagePickOptions?: ImagePicker.ImagePickerOptions;
}

const useImagePicker = ({ imagePickOptions }: IUseUploadFromMediaLibrary) => {
  const [pickerPermission, requestPickerPermission] =
    ImagePicker.useMediaLibraryPermissions();

  const [result, setResult] = useState<ImagePicker.ImagePickerAsset[]>([]);

  const pickImage = async () => {
    try {
      const { status } = await requestPickerPermission();
      if (status !== 'granted') {
        console.warn('Permission to access media library is required.');
        return;
      }

      // Convert deprecated MediaTypeOptions to MediaType array if needed
      let mediaTypes: ImagePicker.MediaType[] = ['images'];
      if (imagePickOptions?.mediaTypes) {
        if (typeof imagePickOptions.mediaTypes === 'string') {
          // Handle string values like 'images' or 'videos'
          if (imagePickOptions.mediaTypes === 'images') {
            mediaTypes = ['images'];
          } else if (imagePickOptions.mediaTypes === 'videos') {
            mediaTypes = ['videos'];
          }
          // else if (imagePickOptions.mediaTypes === 'all') {
          //   mediaTypes = ['images', 'videos'];
          // }
        } else if (Array.isArray(imagePickOptions.mediaTypes)) {
          // Handle array values
          mediaTypes = imagePickOptions.mediaTypes.map((type) => {
            if (type === 'images') return 'images';
            if (type === 'videos') return 'videos';
            return type as ImagePicker.MediaType;
          });
        }
      }

      const pickerResult = await ImagePicker.launchImageLibraryAsync({
        mediaTypes,
        allowsEditing: false, // Remove cropping
        quality: 0.7, // Slightly higher quality but still optimized
        exif: false, // Don't include EXIF data to reduce size
        base64: false, // Don't include base64 to reduce memory usage
        ...imagePickOptions,
        // Remove deprecated mediaTypes from options
        ...(imagePickOptions && { mediaTypes: undefined }),
      });

      if (!pickerResult.canceled) {
        const optimizedAssets = pickerResult.assets.map((asset) => ({
          ...asset,
          uri:
            Platform.OS === 'ios'
              ? asset.uri.replace('file://', '')
              : asset.uri,
        }));

        setResult(
          imagePickOptions?.allowsMultipleSelection
            ? [...result, ...optimizedAssets]
            : optimizedAssets,
        );
        return optimizedAssets;
      }
    } catch (error) {
      console.error('Error picking image:', error);
    }
    return null;
  };

  const takePhoto = async () => {
    try {
      const { status } = await ImagePicker.requestCameraPermissionsAsync();
      if (status !== 'granted') {
        console.warn('Permission to access camera is required.');
        return;
      }

      const cameraResult = await ImagePicker.launchCameraAsync({
        mediaTypes: ['images'], // Updated to use new format
        allowsEditing: false, // Remove cropping
        quality: 0.7, // Slightly higher quality but still optimized
        exif: false, // Don't include EXIF data to reduce size
        base64: false, // Don't include base64 to reduce memory usage
      });

      if (!cameraResult.canceled) {
        const optimizedAssets = cameraResult.assets.map((asset) => ({
          ...asset,
          uri:
            Platform.OS === 'ios'
              ? asset.uri.replace('file://', '')
              : asset.uri,
        }));

        setResult(optimizedAssets);
        return optimizedAssets;
      }
    } catch (error) {
      console.error('Error taking photo:', error);
    }
    return null;
  };

  return { pickImage, takePhoto, result, setResult };
};

export default useImagePicker;
