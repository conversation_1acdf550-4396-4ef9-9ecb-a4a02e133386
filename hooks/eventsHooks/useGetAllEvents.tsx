import { getAllEventsAPI, IGetAllEventsAPIPayload } from '@/services/eventsApi';
import { useQuery } from '@tanstack/react-query';

const useGetAllEvents = ({
  page,
  city,
  country,
  district,
  region,
}: IGetAllEventsAPIPayload) => {
  const query = useQuery({
    queryKey: ['get-all-events'],
    queryFn: async () =>
      await getAllEventsAPI({ page, city, country, district, region }),
  });
  return { ...query };
};

export default useGetAllEvents;
