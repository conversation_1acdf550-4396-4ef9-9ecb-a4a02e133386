import { GetTopEventsAPI } from '@/services/eventsApi';
import { useQuery, UseQueryOptions } from '@tanstack/react-query';

const useGetTopEventsQuery = (limit?: number, options?: UseQueryOptions) => {
  return useQuery({
    queryKey: ['get-top-events', limit], // Include limit in queryKey to refetch on change
    queryFn: async () => GetTopEventsAPI({ limit }),
    enabled: !!limit, // Prevents execution if limit is falsy (undefined, null, 0)
    ...options,
  });
};

export default useGetTopEventsQuery;
