import { create } from 'zustand';

interface MessagesState {
  searchQuery: string;
  activeTab: 'all' | 'single' | 'group';
  setSearchQuery: (query: string) => void;
  setActiveTab: (tab: 'all' | 'single' | 'group') => void;
}

export const useMessagesStore = create<MessagesState>((set) => ({
  searchQuery: '',
  activeTab: 'all',
  setSearchQuery: (query) => set({ searchQuery: query }),
  setActiveTab: (tab) => set({ activeTab: tab }),
}));
