#!/usr/bin/env node

/**
 * Performance analysis script for the mobile app
 * Analyzes bundle size, dependencies, and generates performance recommendations
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Generating Performance Report...\n');

// Colors for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
};

function colorize(text, color) {
  return `${colors[color]}${text}${colors.reset}`;
}

// 1. Analyze package.json dependencies
function analyzeDependencies() {
  console.log(colorize('📦 Analyzing Dependencies...', 'blue'));
  
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const deps = Object.keys(packageJson.dependencies || {});
  const devDeps = Object.keys(packageJson.devDependencies || {});
  
  console.log(`  • Production dependencies: ${colorize(deps.length, 'green')}`);
  console.log(`  • Development dependencies: ${colorize(devDeps.length, 'yellow')}`);
  
  // Check for large dependencies
  const largeDependencies = [
    'react-native-maps',
    'react-native-vision-camera',
    'lottie-react-native',
    '@expo/vector-icons',
    'react-native-vector-icons',
  ];
  
  const foundLargeDeps = deps.filter(dep => largeDependencies.includes(dep));
  if (foundLargeDeps.length > 0) {
    console.log(`  • Large dependencies found: ${colorize(foundLargeDeps.join(', '), 'yellow')}`);
    console.log(`    ${colorize('💡 Consider lazy loading or alternatives', 'cyan')}`);
  }
  
  console.log();
}

// 2. Analyze file structure
function analyzeFileStructure() {
  console.log(colorize('📁 Analyzing File Structure...', 'blue'));
  
  const getDirectorySize = (dirPath) => {
    let totalSize = 0;
    const files = fs.readdirSync(dirPath, { withFileTypes: true });
    
    for (const file of files) {
      const fullPath = path.join(dirPath, file.name);
      if (file.isDirectory() && !file.name.startsWith('.') && file.name !== 'node_modules') {
        totalSize += getDirectorySize(fullPath);
      } else if (file.isFile()) {
        totalSize += fs.statSync(fullPath).size;
      }
    }
    
    return totalSize;
  };
  
  const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };
  
  const directories = ['components', 'hooks', 'services', 'utils', 'assets', 'stores'];
  
  directories.forEach(dir => {
    if (fs.existsSync(dir)) {
      const size = getDirectorySize(dir);
      console.log(`  • ${dir}: ${colorize(formatBytes(size), 'green')}`);
    }
  });
  
  console.log();
}

// 3. Check for performance anti-patterns
function checkAntiPatterns() {
  console.log(colorize('🔍 Checking for Performance Anti-patterns...', 'blue'));
  
  const antiPatterns = [
    {
      pattern: /console\.log/g,
      file: '**/*.{ts,tsx,js,jsx}',
      message: 'Console.log statements found (should be removed in production)',
      severity: 'warning'
    },
    {
      pattern: /useEffect\(\s*\(\)\s*=>\s*{[\s\S]*?},\s*\[\]\s*\)/g,
      message: 'Empty dependency arrays in useEffect (potential memory leaks)',
      severity: 'warning'
    },
    {
      pattern: /\.map\(.*\)\.map\(/g,
      message: 'Chained map operations (consider combining)',
      severity: 'info'
    }
  ];
  
  // This is a simplified check - in a real implementation, you'd use a proper AST parser
  console.log(`  • ${colorize('Anti-pattern detection would require AST parsing', 'yellow')}`);
  console.log(`  • ${colorize('💡 Consider using ESLint rules for comprehensive checks', 'cyan')}`);
  console.log();
}

// 4. Generate recommendations
function generateRecommendations() {
  console.log(colorize('💡 Performance Recommendations:', 'magenta'));
  
  const recommendations = [
    {
      category: 'Bundle Size',
      items: [
        'Use dynamic imports for large components',
        'Implement code splitting for different screens',
        'Remove unused dependencies',
        'Use tree shaking for libraries'
      ]
    },
    {
      category: 'React Native Performance',
      items: [
        'Use FlatList with getItemLayout for known item heights',
        'Implement memo() for expensive components',
        'Use useCallback for event handlers',
        'Enable removeClippedSubviews for long lists'
      ]
    },
    {
      category: 'Image Optimization',
      items: [
        'Use WebP format for images',
        'Implement progressive image loading',
        'Add image caching strategy',
        'Resize images to appropriate dimensions'
      ]
    },
    {
      category: 'Network Optimization',
      items: [
        'Implement request deduplication',
        'Use proper cache strategies',
        'Add offline support',
        'Optimize API response sizes'
      ]
    }
  ];
  
  recommendations.forEach(category => {
    console.log(`\n  ${colorize(category.category + ':', 'bright')}`);
    category.items.forEach(item => {
      console.log(`    • ${item}`);
    });
  });
  
  console.log();
}

// 5. Performance metrics summary
function showMetricsSummary() {
  console.log(colorize('📊 Performance Metrics Summary:', 'magenta'));
  
  const metrics = {
    'Bundle Analysis': 'Run `npm run analyze:bundle` for detailed bundle analysis',
    'Dependency Check': 'Run `npm run analyze:deps` to find unused dependencies',
    'Performance Tests': 'Run `npm run test:performance` for performance tests',
    'Memory Profiling': 'Use React DevTools Profiler for component analysis',
    'Network Analysis': 'Use Flipper or React Native Debugger for network monitoring'
  };
  
  Object.entries(metrics).forEach(([key, value]) => {
    console.log(`  • ${colorize(key, 'bright')}: ${value}`);
  });
  
  console.log();
}

// 6. Next steps
function showNextSteps() {
  console.log(colorize('🎯 Next Steps:', 'green'));
  
  const steps = [
    'Implement OptimizedFlatList component for better list performance',
    'Add performance monitoring hooks to critical components',
    'Set up bundle size monitoring in CI/CD',
    'Implement image optimization strategy',
    'Add performance regression tests',
    'Monitor app performance in production'
  ];
  
  steps.forEach((step, index) => {
    console.log(`  ${index + 1}. ${step}`);
  });
  
  console.log();
}

// Main execution
function main() {
  try {
    analyzeDependencies();
    analyzeFileStructure();
    checkAntiPatterns();
    generateRecommendations();
    showMetricsSummary();
    showNextSteps();
    
    console.log(colorize('✅ Performance report generated successfully!', 'green'));
    console.log(colorize('📝 Save this report and track improvements over time.', 'cyan'));
    
  } catch (error) {
    console.error(colorize('❌ Error generating performance report:', 'red'), error.message);
    process.exit(1);
  }
}

main();
