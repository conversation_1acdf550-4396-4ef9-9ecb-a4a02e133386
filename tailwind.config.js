/** @type {import('tailwindcss').Config} */
module.exports = {
  // NOTE: Update this to include the paths to all of your component files.
  content: [
    './app/**/*.{js,jsx,ts,tsx}',
    './components/**/*.{js,jsx,ts,tsx}',
    './layouts/**/*.{js,jsx,ts,tsx}',
  ],
  presets: [require('nativewind/preset')],
  theme: {
    extend: {
      fontFamily: {
        poppins: [
          'Poppins-Regular',
          'Poppins-Light',
          'Poppins-Bold',
          'Poppins-SemiBold',
        ],
      },
      fontSize: {
        '4xl': ['1.75rem'], // 28px to rem
        h1: ['1.5rem', { lineHeight: '2rem' }], // 24px to rem
        h2: ['1.375rem', { lineHeight: '1.875rem' }], // 22px to rem
        h3: ['1.25rem', { lineHeight: '1.75rem' }], // 20px to rem
        h4: ['1.125rem', { lineHeight: '1.625rem' }], // 18px to rem
        h5: ['1rem', { lineHeight: '1.5rem' }], // 16px to rem
        body1: ['0.9375rem', { lineHeight: '1.375rem' }], // 15px to rem
        body2: ['0.875rem', { lineHeight: '1.25rem' }], // 14px to rem
        body3: ['0.75rem', { lineHeight: '1.125rem' }], // 12px to rem
        body4: ['0.625rem', { lineHeight: '0.9375rem' }], // 10px to rem
      },
      colors: {
        primary: {
          50: '#7a26b1',
          200: '#391162',
        },
        secondary: {
          300: '#c96277',
          400: '#c96277',
          background: '#170e2e',
        },
        accent: {
          100: '#febcbf',
          200: '#fc7980',
          300: '#fb3640',
          400: '#e70510',
          500: '#a2030b',
        },

        neutral: {
          100: '#e4e4e4',
          200: '#d2d2d2',
          300: '#bfbfbf',
          400: '#acacac',
          500: '#9a9a9a',
          600: '#878787',
          700: '#757575',
          800: '#626262',
          900: '#4f4f4f',
          1000: '#3d3d3d',
        },

        error: {
          100: '#f7b0aa',
          200: '#EE6055',
          300: '#E92D1F',
          400: '#B91F13',
          500: '#83160D',
        },

        warning: {
          100: '#FFE9AA',
          200: '#FFD355',
          300: '#FFBD00',
          400: '#C69200',
          500: '#8C6800',
        },

        success: {
          100: '#C3E6C1',
          200: '#86CD82',
          300: '#5BBB56',
          400: '#41973C',
          500: '#2E6B2B',
        },

        tertiaryDisabled: '#E2ADB8',
        white: {
          50: '#FFFFFFFF',
          400: '#B9C5D4',
        },
        text_colors: {
          wite: '#FFFFFF',
          primary: '#C96277',
          secondary: '#ACACAC',
        },
        danger: '#FB3640',
        gray_colors: {
          100: '#E4E4E4',
          200: '#B3B3B3',
        },
        black: '#0A0614',
        'star-color': '#FBBC05',
      },
    },
  },
  plugins: [],
}
